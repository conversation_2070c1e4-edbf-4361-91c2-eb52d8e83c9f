[{"project": "zentao-mcp-backend-service", "path": "/Users/<USER>/SourceCode/Git/feiniu/Other/zentaomcpserver/zentao-mcp-backend-service", "checks": {"pyproject_toml": true, "uv_lock": true, "venv_exists": true, "python_version": "3.12.11", "dependencies": {"fastapi": true, "sqlalchemy": true, "pydantic": true}, "uv_tree": true}}, {"project": "zentao-mcp-client", "path": "/Users/<USER>/SourceCode/Git/feiniu/Other/zentaomcpserver/zentao-mcp-client", "checks": {"pyproject_toml": true, "uv_lock": true, "venv_exists": true, "python_version": "3.12.11", "dependencies": {"mcp": true}, "uv_tree": true}}, {"project": "OldMCPService", "path": "/Users/<USER>/SourceCode/Git/feiniu/Other/zentaomcpserver/OldMCPService", "checks": {"pyproject_toml": true, "uv_lock": true, "venv_exists": true, "python_version": "3.12.10", "dependencies": {"mcp": true}, "uv_tree": true}}]