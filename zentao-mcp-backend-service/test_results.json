{"test_time": 766662.574313208, "base_url": "http://localhost:8000", "tests": {"health": {"endpoint": "/health", "status_code": 200, "success": true, "response": {"status": "healthy", "service": "zentao-mcp-backend-service", "version": "0.1.0"}}, "basic": {"basic_endpoints": [{"endpoint": "/", "name": "根路径", "status_code": 200, "success": true}, {"endpoint": "/docs", "name": "API文档", "status_code": 200, "success": true}, {"endpoint": "/openapi.json", "name": "OpenAPI规范", "status_code": 200, "success": true}]}, "api_key": {"endpoint": "/api/v1/admin/keys", "status_code": 403, "success": false}, "token": {"error": "No API Key available", "success": false}, "authenticated": {"error": "No token available", "success": false}, "key_management": {"key_management": [{"endpoint": "/api/v1/admin/keys", "method": "GET", "name": "获取Key列表", "status_code": 403, "success": true}]}}}