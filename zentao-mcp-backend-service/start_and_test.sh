#!/bin/bash

# 禅道MCP服务端启动和测试脚本

set -e

echo "🚀 禅道MCP服务端启动和测试脚本"
echo "=================================="

# 检查依赖
echo "📋 检查依赖..."
if ! command -v uv &> /dev/null; then
    echo "❌ uv 未安装，请先安装 uv"
    exit 1
fi

# 安装依赖
echo "📦 安装依赖..."
uv sync

# 启动服务器（后台运行）
echo "🔧 启动服务器..."
uv run uvicorn main:app --host 127.0.0.1 --port 8000 &
SERVER_PID=$!

# 等待服务器启动
echo "⏳ 等待服务器启动..."
sleep 5

# 检查服务器是否启动成功
if curl -s http://127.0.0.1:8000/health > /dev/null; then
    echo "✅ 服务器启动成功"
else
    echo "❌ 服务器启动失败"
    kill $SERVER_PID 2>/dev/null || true
    exit 1
fi

# 运行测试
echo "🧪 运行接口测试..."
if uv run python run_complete_test.py; then
    echo "✅ 测试完成"
    TEST_SUCCESS=true
else
    echo "❌ 测试失败"
    TEST_SUCCESS=false
fi

# 停止服务器
echo "🛑 停止服务器..."
kill $SERVER_PID 2>/dev/null || true
wait $SERVER_PID 2>/dev/null || true

# 显示结果
echo "=================================="
if [ "$TEST_SUCCESS" = true ]; then
    echo "🎉 所有测试通过！"
    exit 0
else
    echo "💥 测试失败，请检查日志"
    exit 1
fi