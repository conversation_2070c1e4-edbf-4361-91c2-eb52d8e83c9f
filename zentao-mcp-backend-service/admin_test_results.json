{"test_time": 766897.116053583, "base_url": "http://localhost:8000", "tests": {"config": {"error": "无法获取API文档"}, "endpoints": {"admin_endpoints": [{"endpoint": "/api/v1/admin", "error": "All connection attempts failed", "exists": false}, {"endpoint": "/api/v1/admin/keys", "error": "All connection attempts failed", "exists": false}, {"endpoint": "/api/v1/admin/users", "error": "All connection attempts failed", "exists": false}, {"endpoint": "/api/v1/admin/system", "error": "All connection attempts failed", "exists": false}, {"endpoint": "/api/v1/auth/admin", "error": "All connection attempts failed", "exists": false}, {"endpoint": "/api/v1/system/admin", "error": "All connection attempts failed", "exists": false}]}, "auth_methods": {"auth_methods": []}}}