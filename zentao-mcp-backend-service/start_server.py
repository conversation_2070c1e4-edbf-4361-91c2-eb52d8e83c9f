#!/usr/bin/env python3
"""
开发服务器启动脚本
"""
import sys
import uvicorn
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

if __name__ == "__main__":
    print("🚀 启动权限系统开发服务器...")
    print("📍 API文档地址: http://localhost:8000/docs")
    print("🔧 管理界面: http://localhost:8000/admin")
    print("⚡ 热重载已启用")
    print("-" * 50)
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        reload_dirs=["app"],
        log_level="info"
    )