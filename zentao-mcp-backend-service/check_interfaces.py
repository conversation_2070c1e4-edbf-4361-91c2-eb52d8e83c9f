#!/usr/bin/env python3
"""
检查接口是否存在的简单脚本
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000"

# 需要检查的接口列表
INTERFACES_TO_CHECK = [
    # 直接接口
    "/api/v1/mcp/tools/zentao_get_users_by_account",
    "/api/v1/mcp/tools/zentao_get_tasks_by_account", 
    "/api/v1/mcp/tools/zentao_get_tasks_by_dept",
    "/api/v1/mcp/tools/zentao_get_bugs_by_time_and_dept",
    
    # 数据加工接口
    "/api/v1/mcp/tools/analyze_story_workload",
    "/api/v1/mcp/tools/analyze_bugs_by_dept_and_time",
    "/api/v1/mcp/tools/project_summary_analysis",
    "/api/v1/mcp/tools/personnel_workload_analysis",
    "/api/v1/mcp/tools/story_task_relation_query",
    "/api/v1/mcp/tools/bug_to_story_tracking",
    "/api/v1/mcp/tools/filter_bugs_by_criteria",
    "/api/v1/mcp/tools/batch_query_stories",
    "/api/v1/mcp/tools/validate_story_existence",
    
    # 系统工具
    "/api/v1/mcp/tools/mcp_get_health_status",
    "/api/v1/mcp/tools/mcp_get_performance_metrics"
]

def check_interface_exists(endpoint):
    """检查接口是否存在"""
    try:
        # 使用OPTIONS方法检查接口是否存在
        response = requests.options(BASE_URL + endpoint, timeout=5)
        if response.status_code in [200, 405]:  # 405表示方法不允许但接口存在
            return True, response.status_code
        else:
            return False, response.status_code
    except Exception as e:
        return False, str(e)

def main():
    """主函数"""
    print("🔍 检查接口是否存在...")
    
    results = []
    for endpoint in INTERFACES_TO_CHECK:
        exists, status = check_interface_exists(endpoint)
        results.append({
            "endpoint": endpoint,
            "exists": exists,
            "status": status
        })
        
        if exists:
            print(f"✅ {endpoint}")
        else:
            print(f"❌ {endpoint} - {status}")
    
    # 统计
    existing_count = len([r for r in results if r["exists"]])
    total_count = len(results)
    
    print(f"\n📊 检查结果:")
    print(f"总接口数: {total_count}")
    print(f"存在: {existing_count}")
    print(f"缺失: {total_count - existing_count}")
    print(f"完成率: {existing_count/total_count*100:.1f}%")
    
    # 保存结果
    with open("interface_check_result.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n详细结果已保存到: interface_check_result.json")

if __name__ == "__main__":
    main()