#!/usr/bin/env python3
"""
日志系统测试脚本
测试新的日志系统是否正常工作
"""
import asyncio
import json
import sys
from pathlib import Path
import httpx
import time

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from app.core.logging import setup_logging, api_logger, app_logger


async def test_logging_system():
    """测试日志系统"""
    print("🧪 开始测试日志系统...")
    
    # 1. 测试日志配置
    print("\n1️⃣ 测试日志配置...")
    setup_logging(log_level="DEBUG", log_file="logs/test_zentao_mcp.log")
    
    # 2. 测试不同级别的日志
    print("\n2️⃣ 测试不同级别的日志...")
    app_logger.debug("这是一条DEBUG日志")
    app_logger.info("这是一条INFO日志")
    app_logger.warning("这是一条WARNING日志")
    app_logger.error("这是一条ERROR日志")
    
    # 3. 测试带上下文的日志
    print("\n3️⃣ 测试带上下文的日志...")
    api_logger.info(
        "API请求测试",
        extra={
            "request_id": "test-123",
            "endpoint": "/api/v1/test",
            "method": "POST",
            "user_id": "test_user",
            "duration": 150.5
        }
    )
    
    # 4. 测试异常日志
    print("\n4️⃣ 测试异常日志...")
    try:
        raise ValueError("这是一个测试异常")
    except Exception as e:
        api_logger.error(
            "测试异常处理",
            extra={
                "request_id": "test-456",
                "endpoint": "/api/v1/error",
                "method": "GET",
                "error": str(e),
                "error_type": type(e).__name__
            },
            exc_info=True
        )
    
    print("\n✅ 日志系统测试完成！")
    print("📁 日志文件位置: logs/test_zentao_mcp.log")


async def test_api_logging():
    """测试API日志记录"""
    print("\n🌐 开始测试API日志记录...")
    
    # 启动服务器（假设已经在运行）
    base_url = "http://localhost:8000"
    
    async with httpx.AsyncClient() as client:
        try:
            # 1. 测试健康检查
            print("\n1️⃣ 测试健康检查API...")
            response = await client.get(f"{base_url}/health")
            print(f"   状态码: {response.status_code}")
            
            # 2. 测试根路径
            print("\n2️⃣ 测试根路径API...")
            response = await client.get(f"{base_url}/")
            print(f"   状态码: {response.status_code}")
            
            # 3. 测试登录API（会产生错误日志）
            print("\n3️⃣ 测试登录API（无效凭据）...")
            response = await client.post(
                f"{base_url}/api/v1/admin/auth/login",
                json={"username": "invalid", "password": "invalid"}
            )
            print(f"   状态码: {response.status_code}")
            
            # 4. 测试不存在的API（会产生404错误）
            print("\n4️⃣ 测试不存在的API...")
            response = await client.get(f"{base_url}/api/v1/nonexistent")
            print(f"   状态码: {response.status_code}")
            
        except httpx.ConnectError:
            print("❌ 无法连接到服务器，请确保服务器正在运行")
            print("   启动命令: python -m uvicorn main:app --reload")
        except Exception as e:
            print(f"❌ API测试失败: {e}")
    
    print("\n✅ API日志测试完成！")


def check_log_files():
    """检查日志文件"""
    print("\n📋 检查日志文件...")
    
    log_dir = Path("logs")
    if not log_dir.exists():
        print("❌ 日志目录不存在")
        return
    
    log_files = list(log_dir.glob("*.log"))
    if not log_files:
        print("❌ 没有找到日志文件")
        return
    
    for log_file in log_files:
        print(f"\n📄 日志文件: {log_file}")
        print(f"   大小: {log_file.stat().st_size} bytes")
        print(f"   修改时间: {time.ctime(log_file.stat().st_mtime)}")
        
        # 显示最后几行日志
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    print("   最后3行日志:")
                    for line in lines[-3:]:
                        try:
                            # 尝试解析JSON格式的日志
                            log_data = json.loads(line.strip())
                            print(f"     [{log_data.get('level', 'UNKNOWN')}] {log_data.get('message', '')}")
                        except json.JSONDecodeError:
                            # 如果不是JSON格式，直接显示
                            print(f"     {line.strip()}")
        except Exception as e:
            print(f"   ❌ 读取日志文件失败: {e}")


async def main():
    """主函数"""
    print("🚀 日志系统完整测试")
    print("=" * 50)
    
    # 1. 测试日志系统基础功能
    await test_logging_system()
    
    # 2. 检查日志文件
    check_log_files()
    
    # 3. 测试API日志记录
    await test_api_logging()
    
    # 4. 再次检查日志文件
    check_log_files()
    
    print("\n🎉 所有测试完成！")
    print("\n📝 使用建议:")
    print("   1. 查看日志文件: tail -f logs/zentao_mcp.log")
    print("   2. 启动服务: python -m uvicorn main:app --reload")
    print("   3. 查看API文档: http://localhost:8000/docs")


if __name__ == "__main__":
    asyncio.run(main())