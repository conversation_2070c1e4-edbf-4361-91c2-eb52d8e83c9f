# 日志系统使用指南

## 概述

本项目实现了完整的结构化日志系统，支持请求追踪、异常处理和性能监控。

## 核心特性

### 1. 结构化日志输出
- **JSON格式**: 所有日志以JSON格式输出，便于日志分析工具处理
- **请求追踪**: 每个请求分配唯一ID，便于追踪完整的请求生命周期
- **上下文信息**: 自动记录请求方法、路径、用户信息、执行时间等

### 2. 多层次日志记录
- **请求层**: 记录所有API请求的入参、出参和执行时间
- **业务层**: 记录业务逻辑执行过程和结果
- **异常层**: 详细记录异常信息和堆栈跟踪

### 3. 敏感信息保护
- **自动过滤**: 自动过滤密码、token等敏感信息
- **数据脱敏**: 对敏感字段进行脱敏处理

## 日志配置

### 环境变量配置
```bash
# 日志级别
LOG_LEVEL=INFO

# 日志文件路径（留空则只输出到控制台）
LOG_FILE=logs/zentao_mcp.log

# 调试模式
DEBUG=false
```

### 配置文件
复制 `.env.logging.example` 为 `.env` 并根据需要修改：
```bash
cp .env.example .env
```

## 使用方法

### 1. 在代码中使用日志

```python
from app.core.logging import api_logger, app_logger, db_logger

# 基础日志
app_logger.info("应用启动完成")
app_logger.error("系统初始化失败", exc_info=True)

# API日志（带上下文）
api_logger.info(
    "用户登录成功",
    extra={
        "request_id": request_id,
        "user_id": user.id,
        "endpoint": "/api/v1/auth/login",
        "method": "POST"
    }
)

# 数据库操作日志
db_logger.info("执行数据库查询", extra={"query": "SELECT * FROM users"})
```

### 2. 自动日志记录

系统会自动记录：
- **所有API请求**: 包括请求参数、响应数据、执行时间
- **异常信息**: 包括异常类型、错误消息、堆栈跟踪
- **性能指标**: 请求处理时间、数据库查询时间等

## 日志格式

### 标准日志格式
```json
{
  "timestamp": "2025-09-03T01:30:45.123Z",
  "level": "INFO",
  "logger": "zentao_mcp.api",
  "message": "Request completed: POST /api/v1/auth/login - 200",
  "module": "auth",
  "function": "login",
  "line": 45,
  "request_id": "550e8400-e29b-41d4-a716-446655440000",
  "endpoint": "/api/v1/auth/login",
  "method": "POST",
  "status_code": 200,
  "duration_ms": 150.25,
  "user_id": "admin"
}
```

### 异常日志格式
```json
{
  "timestamp": "2025-09-03T01:30:45.123Z",
  "level": "ERROR",
  "logger": "zentao_mcp.api",
  "message": "Request failed: POST /api/v1/auth/login",
  "request_id": "550e8400-e29b-41d4-a716-446655440000",
  "endpoint": "/api/v1/auth/login",
  "method": "POST",
  "error": "Invalid credentials",
  "error_type": "AuthenticationError",
  "exception": "Traceback (most recent call last)...",
  "duration_ms": 50.12
}
```

## 日志分析

### 1. 查看实时日志
```bash
# 查看所有日志
tail -f logs/zentao_mcp.log

# 过滤特定级别的日志
tail -f logs/zentao_mcp.log | grep '"level":"ERROR"'

# 过滤特定请求ID的日志
tail -f logs/zentao_mcp.log | grep '"request_id":"550e8400-e29b-41d4-a716-446655440000"'
```

### 2. 日志查询示例
```bash
# 查找所有登录请求
grep '"endpoint":"/api/v1/auth/login"' logs/zentao_mcp.log

# 查找所有错误日志
grep '"level":"ERROR"' logs/zentao_mcp.log

# 查找响应时间超过1秒的请求
grep -E '"duration_ms":[0-9]{4,}' logs/zentao_mcp.log

# 统计API调用次数
grep -o '"endpoint":"[^"]*"' logs/zentao_mcp.log | sort | uniq -c
```

### 3. 使用jq进行高级查询
```bash
# 安装jq
brew install jq  # macOS
# 或
apt-get install jq  # Ubuntu

# 查询示例
cat logs/zentao_mcp.log | jq 'select(.level == "ERROR")'
cat logs/zentao_mcp.log | jq 'select(.duration_ms > 1000)'
cat logs/zentao_mcp.log | jq '.endpoint' | sort | uniq -c
```

## 性能监控

### 1. 请求性能分析
日志中包含详细的性能指标：
- `duration_ms`: 请求处理时间（毫秒）
- `request_data`: 请求参数大小
- `response_data`: 响应数据大小

### 2. 异常监控
系统会自动记录：
- 异常类型和频率
- 异常发生的接口和时间
- 完整的异常堆栈信息

## 故障排查

### 1. 常见问题

**问题**: 日志文件不生成
**解决**: 检查 `LOG_FILE` 环境变量和目录权限

**问题**: 日志格式不正确
**解决**: 确保使用了正确的logger实例

**问题**: 敏感信息泄露
**解决**: 检查敏感字段是否在过滤列表中

### 2. 调试模式
开启调试模式获取更详细的日志：
```bash
export DEBUG=true
export LOG_LEVEL=DEBUG
```

## 最佳实践

### 1. 日志级别使用
- **DEBUG**: 详细的调试信息，仅在开发环境使用
- **INFO**: 一般信息，如请求完成、业务流程节点
- **WARNING**: 警告信息，如参数验证失败、重试操作
- **ERROR**: 错误信息，如异常、失败的操作
- **CRITICAL**: 严重错误，如系统崩溃、数据丢失

### 2. 上下文信息
始终包含足够的上下文信息：
```python
logger.info(
    "操作描述",
    extra={
        "request_id": request_id,  # 必需
        "user_id": user_id,       # 如果有用户上下文
        "operation": "operation_name",
        "resource_id": resource_id,
        "additional_context": "..."
    }
)
```

### 3. 错误处理
记录异常时包含完整信息：
```python
try:
    # 业务逻辑
    pass
except Exception as e:
    logger.error(
        "操作失败",
        extra={
            "request_id": request_id,
            "error": str(e),
            "error_type": type(e).__name__,
            "operation": "operation_name"
        },
        exc_info=True  # 包含堆栈跟踪
    )
    raise
```

## 测试

运行日志系统测试：
```bash
python test_logging_system.py
```

这将测试：
- 日志配置是否正确
- 不同级别的日志输出
- 上下文信息记录
- 异常日志处理
- API请求日志记录