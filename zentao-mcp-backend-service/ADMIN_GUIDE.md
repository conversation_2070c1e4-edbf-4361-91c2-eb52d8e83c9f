# Zentao MCP 管理员使用指南

## 🔑 管理员API Key认证

### 配置信息
- **管理员API Key**: `admin_super_secret_key_12345` (在.env文件中配置)
- **认证方式**: `Authorization: Bearer <api_key>`
- **管理端点**: `/api/v1/admin/*`

### 使用方法

#### 1. 创建API Key
```bash
curl -X POST "http://localhost:8000/api/v1/admin/keys" \
  -H "Authorization: Bearer admin_super_secret_key_12345" \
  -H "Content-Type: application/json" \
  -d '{
    "user_identifier": "user_001",
    "description": "测试用API Key"
  }'
```

#### 2. 获取API Key列表
```bash
curl -X GET "http://localhost:8000/api/v1/admin/keys" \
  -H "Authorization: Bearer admin_super_secret_key_12345"
```

#### 3. 吊销API Key
```bash
curl -X PUT "http://localhost:8000/api/v1/admin/keys/{key_id}/revoke" \
  -H "Authorization: Bearer admin_super_secret_key_12345"
```

#### 4. 激活API Key
```bash
curl -X PUT "http://localhost:8000/api/v1/admin/keys/{key_id}/activate" \
  -H "Authorization: Bearer admin_super_secret_key_12345"
```

#### 5. 删除API Key
```bash
curl -X DELETE "http://localhost:8000/api/v1/admin/keys/{key_id}" \
  -H "Authorization: Bearer admin_super_secret_key_12345"
```

## 🧪 测试脚本

### 运行管理员测试
```bash
# 正确的管理员API Key测试
python test_admin_correct.py

# 完整的API测试
python test_complete.py

# 快速测试
python quick_test.py
```

## 🔧 配置说明

### .env文件配置
```env

# 数据库配置
DATABASE_URL=sqlite:///./data/zentao_mcp.db

# Zentao API配置
ZENTAO_BASE_URL=http://localhost:8080

# 应用设置
DEBUG=true
```

### 安全注意事项
1. **生产环境**: 请更改默认的管理员API Key
2. **权限控制**: 管理员API Key拥有最高权限，请妥善保管
3. **HTTPS**: 生产环境建议使用HTTPS传输
4. **日志监控**: 建议监控API Key的使用情况

## 📊 API响应格式

### 创建API Key响应
```json
{
  "id": 1,
  "key_hash": "hashed_key_value",
  "user_identifier": "user_001",
  "is_active": true,
  "created_at": "2024-08-29T03:30:00Z",
  "revoked_at": null,
  "plain_key": "actual_api_key_value"
}
```

### API Key列表响应
```json
[
  {
    "id": 1,
    "key_hash": "hashed_key_value",
    "user_identifier": "user_001",
    "is_active": true,
    "created_at": "2024-08-29T03:30:00Z",
    "revoked_at": null
  }
]
```

## 🚀 快速开始

1. **启动服务**
   ```bash
   uvicorn main:app --host 127.0.0.1 --port 8000
   ```

2. **初始化数据库**
   ```bash
   python init_database.py
   ```

3. **测试管理员功能**
   ```bash
   python test_admin_correct.py
   ```

4. **访问API文档**
   - Swagger UI: http://localhost:8000/docs
   - ReDoc: http://localhost:8000/redoc

## 🔍 故障排除

### 常见问题

1. **403 Forbidden**
   - 检查Authorization头格式: `Bearer <api_key>`
   - 确认使用正确的管理员API Key

2. **401 Unauthorized**
   - 检查API Key是否正确
   - 确认请求头包含Authorization

3. **500 Internal Server Error**
   - 检查数据库连接
   - 查看服务器日志

### 调试命令
```bash
# 检查数据库表
sqlite3 data/zentao_mcp.db ".tables"

# 查看API Key记录
sqlite3 data/zentao_mcp.db "SELECT * FROM api_keys;"

# 检查服务状态
curl http://localhost:8000/health