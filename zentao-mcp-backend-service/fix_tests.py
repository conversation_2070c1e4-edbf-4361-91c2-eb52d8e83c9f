#!/usr/bin/env python3
"""
修复测试文件中的模型创建问题
"""

import re

def fix_test_models():
    """修复test_models.py中的问题"""
    
    # 读取文件
    with open('tests/unit/test_models.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复AdminUser测试 - 添加db_session参数和显式设置默认值
    content = re.sub(
        r'def test_create_admin_user\(self\):',
        'def test_create_admin_user(self, db_session):',
        content
    )
    
    content = re.sub(
        r'def test_user_validation\(self\):',
        'def test_user_validation(self, db_session):',
        content
    )
    
    # 修复APIKey测试
    content = re.sub(
        r'def test_create_api_key\(self\):',
        'def test_create_api_key(self, db_session):',
        content
    )
    
    content = re.sub(
        r'def test_api_key_expiration\(self\):',
        'def test_api_key_expiration(self, db_session):',
        content
    )
    
    content = re.sub(
        r'def test_api_key_status\(self\):',
        'def test_api_key_status(self, db_session):',
        content
    )
    
    # 修复AuditLog测试
    content = re.sub(
        r'def test_create_audit_log\(self\):',
        'def test_create_audit_log(self, db_session):',
        content
    )
    
    # 修复UserSession测试
    content = re.sub(
        r'def test_create_user_session\(self\):',
        'def test_create_user_session(self, db_session):',
        content
    )
    
    content = re.sub(
        r'def test_session_expiration\(self\):',
        'def test_session_expiration(self, db_session):',
        content
    )
    
    content = re.sub(
        r'def test_session_status\(self\):',
        'def test_session_status(self, db_session):',
        content
    )
    
    # 添加显式的默认值设置和数据库操作
    replacements = [
        # AdminUser创建
        (
            r'user = AdminUser\(\s*username="test_admin",\s*password_hash=PasswordService\.hash_password\("TestPass123!"\),\s*email="admin@example\.com",\s*user_type=UserType\.ADMIN,\s*phone="13800138000"\s*\)',
            '''user = AdminUser(
            username="test_admin",
            password_hash=PasswordService.hash_password("TestPass123!"),
            email="<EMAIL>",
            user_type=UserType.ADMIN,
            phone="13800138000",
            is_active=True
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)'''
        ),
        
        # AdminUser validation
        (
            r'user = AdminUser\(\s*username="test_user",\s*password_hash="hashed_password",\s*email="user@example\.com",\s*user_type=UserType\.USER\s*\)',
            '''user = AdminUser(
            username="test_user",
            password_hash="hashed_password",
            email="<EMAIL>",
            user_type=UserType.USER,
            is_active=True
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)'''
        ),
        
        # APIKey创建
        (
            r'api_key = APIKey\(\s*key="test_key_123",\s*key_hash="hashed_key",\s*user_identifier="test_user",\s*name="测试密钥",\s*description="这是一个测试API密钥",\s*permissions=\["read", "write"\],\s*expires_at=datetime\.now\(timezone\.utc\) \+ timedelta\(days=30\)\s*\)',
            '''api_key = APIKey(
            key="test_key_123",
            key_value="test_key_123",
            key_hash="hashed_key",
            user_identifier="test_user",
            name="测试密钥",
            description="这是一个测试API密钥",
            permissions=["read", "write"],
            expires_at=datetime.now(timezone.utc) + timedelta(days=30),
            is_active=True
        )
        db_session.add(api_key)
        db_session.commit()
        db_session.refresh(api_key)'''
        ),
        
        # APIKey expiration test
        (
            r'future_key = APIKey\(\s*key="future_key",\s*key_hash="hashed_key",\s*user_identifier="test_user",\s*expires_at=datetime\.now\(timezone\.utc\) \+ timedelta\(days=30\)\s*\)',
            '''future_key = APIKey(
            key="future_key",
            key_value="future_key",
            key_hash="hashed_key",
            user_identifier="test_user",
            expires_at=datetime.now(timezone.utc) + timedelta(days=30),
            is_active=True
        )
        db_session.add(future_key)
        db_session.commit()
        db_session.refresh(future_key)'''
        ),
        
        # APIKey status test
        (
            r'api_key = APIKey\(\s*key="test_key",\s*key_hash="hashed_key",\s*user_identifier="test_user"\s*\)',
            '''api_key = APIKey(
            key="test_key",
            key_value="test_key",
            key_hash="hashed_key",
            user_identifier="test_user",
            is_active=True
        )
        db_session.add(api_key)
        db_session.commit()
        db_session.refresh(api_key)'''
        ),
        
        # AuditLog创建
        (
            r'audit_log = AuditLog\(\s*action="create_user",\s*resource_type="user",\s*resource_id="123",\s*details=\{"username": "test_user", "email": "test@example\.com"\},\s*ip_address="192\.168\.1\.1",\s*user_agent="Test Agent"\s*\)',
            '''audit_log = AuditLog(
            action="create_user",
            resource_type="user",
            resource_id="123",
            details={"username": "test_user", "email": "<EMAIL>"},
            ip_address="***********",
            user_agent="Test Agent"
        )
        db_session.add(audit_log)
        db_session.commit()
        db_session.refresh(audit_log)'''
        ),
        
        # UserSession创建
        (
            r'session = UserSession\(\s*session_token=secrets\.token_urlsafe\(32\),\s*ip_address="192\.168\.1\.1",\s*user_agent="Test Browser",\s*expires_at=datetime\.now\(timezone\.utc\) \+ timedelta\(hours=8\)\s*\)',
            '''session = UserSession(
            session_token=secrets.token_urlsafe(32),
            ip_address="***********",
            user_agent="Test Browser",
            expires_at=datetime.now(timezone.utc) + timedelta(hours=8),
            is_active=True
        )
        db_session.add(session)
        db_session.commit()
        db_session.refresh(session)'''
        ),
        
        # UserSession expiration test
        (
            r'active_session = UserSession\(\s*session_token=secrets\.token_urlsafe\(32\),\s*ip_address="192\.168\.1\.1",\s*user_agent="Test Browser",\s*expires_at=datetime\.now\(timezone\.utc\) \+ timedelta\(hours=8\)\s*\)',
            '''active_session = UserSession(
            session_token=secrets.token_urlsafe(32),
            ip_address="***********",
            user_agent="Test Browser",
            expires_at=datetime.now(timezone.utc) + timedelta(hours=8),
            is_active=True
        )
        db_session.add(active_session)
        db_session.commit()
        db_session.refresh(active_session)'''
        ),
        
        # UserSession status test
        (
            r'session = UserSession\(\s*session_token=secrets\.token_urlsafe\(32\),\s*ip_address="192\.168\.1\.1",\s*user_agent="Test Browser",\s*expires_at=datetime\.now\(timezone\.utc\) \+ timedelta\(hours=8\)\s*\)',
            '''session = UserSession(
            session_token=secrets.token_urlsafe(32),
            ip_address="***********",
            user_agent="Test Browser",
            expires_at=datetime.now(timezone.utc) + timedelta(hours=8),
            is_active=True
        )
        db_session.add(session)
        db_session.commit()
        db_session.refresh(session)'''
        )
    ]
    
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    # 写回文件
    with open('tests/unit/test_models.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已修复 test_models.py")

if __name__ == "__main__":
    fix_test_models()