#!/usr/bin/env python3
"""
接口修复验证测试脚本
验证重复接口问题是否已解决
"""

import sys
import importlib.util
from pathlib import Path

def test_main_imports():
    """测试main.py的导入是否正常"""
    print("🔍 测试 main.py 导入...")
    try:
        # 添加项目根目录到路径
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        # 导入main模块
        spec = importlib.util.spec_from_file_location("main", project_root / "main.py")
        main_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(main_module)
        
        print("✅ main.py 导入成功")
        return True
    except Exception as e:
        print(f"❌ main.py 导入失败: {e}")
        return False

def test_api_router_structure():
    """测试API路由结构"""
    print("\n🔍 测试 API 路由结构...")
    try:
        from app.api.v1.api import api_router
        
        # 检查路由数量
        routes = api_router.routes
        print(f"📊 API路由总数: {len(routes)}")
        
        # 检查路由路径前缀
        route_paths = []
        for route in routes:
            if hasattr(route, 'path'):
                route_paths.append(route.path)
        
        # 检查是否包含各个模块的路由
        auth_routes = [path for path in route_paths if path.startswith('/auth')]
        user_routes = [path for path in route_paths if path.startswith('/users')]
        apikey_routes = [path for path in route_paths if path.startswith('/api-keys')]
        
        if not auth_routes:
            print("❌ 缺少认证相关路由")
            return False
        if not user_routes:
            print("❌ 缺少用户管理路由")
            return False
        if not apikey_routes:
            print("❌ 缺少API Key管理路由")
            return False
        
        print("✅ API路由结构正确")
        print(f"📋 认证路由: {len(auth_routes)}个")
        print(f"📋 用户路由: {len(user_routes)}个") 
        print(f"📋 API Key路由: {len(apikey_routes)}个")
        return True
        
    except Exception as e:
        print(f"❌ API路由测试失败: {e}")
        return False

def test_deleted_files():
    """测试已删除的文件确实不存在"""
    print("\n🔍 测试已删除文件...")
    
    deleted_files = [
        "app/main.py",
        "app/api/v1/endpoints/admin.py"
    ]
    
    all_deleted = True
    for file_path in deleted_files:
        full_path = Path(__file__).parent / file_path
        if full_path.exists():
            print(f"❌ 文件仍然存在: {file_path}")
            all_deleted = False
        else:
            print(f"✅ 文件已删除: {file_path}")
    
    return all_deleted

def test_api_keys_endpoints():
    """测试API Keys端点是否包含所有功能"""
    print("\n🔍 测试 API Keys 端点...")
    try:
        from app.api.v1.endpoints.api_keys import router
        
        # 获取所有路由
        routes = router.routes
        route_info = []
        
        for route in routes:
            if hasattr(route, 'methods') and hasattr(route, 'path'):
                for method in route.methods:
                    if method != 'HEAD':  # 忽略HEAD方法
                        route_info.append(f"{method} {route.path}")
        
        print(f"📊 API Keys 端点总数: {len(route_info)}")
        
        # 检查关键端点
        expected_endpoints = [
            "POST /",
            "GET /",
            "GET /{key_id}",
            "PUT /{key_id}",
            "DELETE /{key_id}",
            "PUT /{key_id}/revoke",
            "PUT /{key_id}/activate",
            "POST /{key_id}/regenerate"
        ]
        
        missing_endpoints = []
        for endpoint in expected_endpoints:
            found = False
            for route in route_info:
                if endpoint.split()[1] in route and endpoint.split()[0] in route:
                    found = True
                    break
            if not found:
                missing_endpoints.append(endpoint)
        
        if missing_endpoints:
            print(f"❌ 缺少端点: {missing_endpoints}")
            return False
        
        print("✅ API Keys 端点完整")
        print("📋 包含端点:")
        for route in sorted(route_info):
            print(f"   {route}")
        
        return True
        
    except Exception as e:
        print(f"❌ API Keys 端点测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始接口修复验证测试\n")
    
    tests = [
        ("文件删除验证", test_deleted_files),
        ("main.py导入测试", test_main_imports),
        ("API路由结构测试", test_api_router_structure),
        ("API Keys端点测试", test_api_keys_endpoints),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n{'='*50}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！接口修复成功！")
        return 0
    else:
        print("⚠️  部分测试失败，需要检查修复结果")
        return 1

if __name__ == "__main__":
    sys.exit(main())