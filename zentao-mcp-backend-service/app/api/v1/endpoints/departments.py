"""
部门管理API端点
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import verify_api_key
from app.core.dependencies import get_department_service
from app.services.department_service import DepartmentService
from typing import Any, Dict

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=Dict[str, Any])
async def get_all_departments(
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    department_service: DepartmentService = Depends(get_department_service)
):
    """
    获取所有部门列表
    
    RESTful API: /apiData/getAllDept
    调用服务层获取数据
    """
    try:
        logger.info(f"用户 {user_identifier} 请求获取所有部门列表")
        response = await department_service.get_all_departments()
        
        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )
        
        logger.info(f"成功获取部门列表，响应码: {response.get('rsCode')}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取所有部门列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取所有部门列表失败: {str(e)}"
        )


@router.get("/{dept_id}/users", response_model=Dict[str, Any])
async def get_users_by_department(
    dept_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    department_service: DepartmentService = Depends(get_department_service)
):
    """
    根据部门ID查询用户列表
    
    RESTful API: /api/getUserByDept
    调用服务层获取数据
    """
    try:
        logger.info(f"用户 {user_identifier} 请求获取部门 {dept_id} 的用户列表")
        response = await department_service.get_users_by_department(dept_id)
        
        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )
        
        logger.info(f"成功获取部门 {dept_id} 用户列表，响应码: {response.get('rsCode')}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"根据部门获取用户列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据部门获取用户列表失败: {str(e)}"
        )