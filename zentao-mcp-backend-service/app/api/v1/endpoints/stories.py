"""
需求管理API端点
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Body, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import verify_api_key
from app.core.dependencies import get_story_service
from app.services.story_service import StoryService
from typing import Any, Dict, List

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/batch", response_model=Dict[str, Any])
async def get_story_info(
    story_ids: List[str] = Body(..., description="需求ID列表"),
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """
    批量获取产品需求工时信息
    
    RESTful API: /api/getStory
    调用服务层获取数据
    """
    try:
        logger.info(f"用户 {user_identifier} 请求获取需求信息: {story_ids}")
        response = await story_service.get_story_info(story_ids)
        
        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )
        
        logger.info(f"成功获取需求信息，响应码: {response.get('rsCode')}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取需求信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取需求信息失败: {str(e)}"
        )


@router.get("/{story_id}", response_model=Dict[str, Any])
async def get_story_detail(
    story_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    story_service: StoryService = Depends(get_story_service)
):
    """
    根据需求ID查询需求详情
    
    RESTful API: /api/getStoryDetail
    调用服务层获取数据
    """
    try:
        logger.info(f"用户 {user_identifier} 请求获取需求 {story_id} 的详情")
        response = await story_service.get_story_by_id(story_id)
        
        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )
        
        logger.info(f"成功获取需求 {story_id} 详情，响应码: {response.get('rsCode')}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取需求详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取需求详情失败: {str(e)}"
        )


@router.post("/batch/end", response_model=Dict[str, Any])
async def get_story_end(
    story_ids: List[str] = Body(..., description="需求ID列表"),
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """
    获取需求结束信息
    
    RESTful API: /api/getStoryEnd
    调用服务层获取数据
    """
    try:
        logger.info(f"用户 {user_identifier} 请求获取需求结束信息: {story_ids}")
        response = await story_service.get_story_end(story_ids)
        
        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )
        
        logger.info(f"成功获取需求结束信息，响应码: {response.get('rsCode')}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取需求结束信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取需求结束信息失败: {str(e)}"
        )


@router.post("/batch/check", response_model=Dict[str, Any])
async def check_story_exists(
    story_ids: List[str] = Body(..., description="需求ID列表"),
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """
    检查需求是否存在
    
    RESTful API: /api/checkStoryExists
    调用服务层获取数据
    """
    try:
        logger.info(f"用户 {user_identifier} 请求检查需求存在性: {story_ids}")
        response = await story_service.check_story_exists(story_ids)
        
        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )
        
        logger.info(f"成功检查需求存在性，响应码: {response.get('rsCode')}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检查需求存在性失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"检查需求存在性失败: {str(e)}"
        )


@router.get("/by-time", response_model=Dict[str, Any])
async def get_stories_by_time(
    status: str = Query(..., description="需求状态"),
    start_date: str = Query(..., description="开始日期 (YYYY-MM-DD HH:MM:SS格式)"),
    end_date: str = Query(..., description="结束日期 (YYYY-MM-DD HH:MM:SS格式)"),
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """
    根据时间段和状态获取需求列表
    
    RESTful API: /api/getStoriesByTime
    调用服务层获取数据
    """
    try:
        logger.info(f"用户 {user_identifier} 请求按时间查询需求，状态: {status}")
        response = await story_service.get_stories_by_time(status, start_date, end_date)
        
        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )
        
        logger.info(f"成功按时间查询需求，响应码: {response.get('rsCode')}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"按时间查询需求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"按时间查询需求失败: {str(e)}"
        )
