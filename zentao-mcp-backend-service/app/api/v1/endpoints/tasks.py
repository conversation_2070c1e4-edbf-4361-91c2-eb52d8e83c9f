"""
任务管理API端点
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import verify_api_key
from app.core.dependencies import get_task_service
from app.services.task_service import TaskService
from typing import Any, Dict

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/{task_id}", response_model=Dict[str, Any])
async def get_task_detail(
    task_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    task_service: TaskService = Depends(get_task_service)
):
    """
    根据任务ID查询任务详情
    
    RESTful API: /api/getTaskById
    调用服务层获取数据
    """
    try:
        logger.info(f"用户 {user_identifier} 请求获取任务 {task_id} 的详情")
        response = await task_service.get_task_by_id(task_id)
        
        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )
        
        logger.info(f"成功获取任务 {task_id} 详情，响应码: {response.get('rsCode')}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务详情失败: {str(e)}"
        )


@router.get("/by-account/{account}", response_model=Dict[str, Any])
async def get_tasks_by_account(
    account: str,
    start_date: str = Query(..., description="开始日期 (YYYY-MM-DD HH:MM:SS格式)"),
    end_date: str = Query(..., description="结束日期 (YYYY-MM-DD HH:MM:SS格式)"),
    is_doing: bool = Query(False, description="是否只查询进行中的任务"),
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """
    根据域账号查询任务
    
    RESTful API: /api/getTaskByAccount
    调用服务层获取数据
    """
    try:
        logger.info(f"用户 {user_identifier} 请求获取账号 {account} 的任务列表")
        response = await task_service.get_tasks_by_account(account, start_date, end_date, is_doing)
        
        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )
        
        logger.info(f"成功获取账号 {account} 任务列表，响应码: {response.get('rsCode')}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"根据账号查询任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据账号查询任务失败: {str(e)}"
        )


@router.get("/by-department/{dept_id}", response_model=Dict[str, Any])
async def get_tasks_by_department(
    dept_id: int,
    start_date: str = Query(..., description="开始日期 (YYYY-MM-DD HH:MM:SS格式)"),
    end_date: str = Query(..., description="结束日期 (YYYY-MM-DD HH:MM:SS格式)"),
    is_doing: bool = Query(False, description="是否只查询进行中的任务"),
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """
    根据部门查询任务列表
    
    RESTful API: /api/getTasksByDept
    调用服务层获取数据
    """
    try:
        logger.info(f"用户 {user_identifier} 请求获取部门 {dept_id} 的任务列表")
        response = await task_service.get_tasks_by_dept(dept_id, start_date, end_date, is_doing)
        
        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )
        
        logger.info(f"成功获取部门 {dept_id} 任务列表，响应码: {response.get('rsCode')}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"根据部门查询任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据部门查询任务失败: {str(e)}"
        )
