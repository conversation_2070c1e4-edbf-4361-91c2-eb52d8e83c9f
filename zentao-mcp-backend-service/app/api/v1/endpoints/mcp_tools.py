"""
MCP工具API端点 - 按设计文档要求的格式
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import verify_api_key
from app.core.dependencies import (
    get_project_service, get_bug_service, get_story_service, 
    get_task_service, get_department_service, get_user_service,
    get_analysis_service, get_system_service
)
from app.services.project_service import ProjectService
from app.services.bug_service import BugService
from app.services.story_service import StoryService
from app.services.task_service import TaskService
from app.services.department_service import DepartmentService
from app.services.user_service import UserService
from app.services.analysis_service import AnalysisService
from app.services.system_service import SystemService
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)
router = APIRouter()


# 项目相关工具
@router.post("/zentao_get_all_projects", response_model=Dict[str, Any])
async def zentao_get_all_projects(
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    project_service: ProjectService = Depends(get_project_service)
):
    """获取所有项目列表"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_all_projects")
        response = await project_service.get_all_projects()
        logger.info(f"成功获取项目列表，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"获取所有项目列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取所有项目列表失败: {str(e)}"
        )


@router.post("/zentao_get_tasks_by_project", response_model=Dict[str, Any])
async def zentao_get_tasks_by_project(
    project_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    project_service: ProjectService = Depends(get_project_service)
):
    """根据项目ID查询任务列表"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_tasks_by_project，项目ID: {project_id}")
        response = await project_service.get_tasks_by_project(project_id)
        logger.info(f"成功获取项目 {project_id} 任务列表，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"根据项目ID查询任务列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据项目ID查询任务列表失败: {str(e)}"
        )


@router.post("/zentao_get_stories_by_project", response_model=Dict[str, Any])
async def zentao_get_stories_by_project(
    project_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    project_service: ProjectService = Depends(get_project_service)
):
    """根据项目ID查询需求列表"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_stories_by_project，项目ID: {project_id}")
        response = await project_service.get_stories_by_project(project_id)
        logger.info(f"成功获取项目 {project_id} 需求列表，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"根据项目ID查询需求列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据项目ID查询需求列表失败: {str(e)}"
        )


@router.post("/zentao_get_bugs_by_project", response_model=Dict[str, Any])
async def zentao_get_bugs_by_project(
    project_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    project_service: ProjectService = Depends(get_project_service)
):
    """根据项目ID查询Bug列表"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_bugs_by_project，项目ID: {project_id}")
        response = await project_service.get_bugs_by_project(project_id)
        logger.info(f"成功获取项目 {project_id} Bug列表，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"根据项目ID查询Bug列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据项目ID查询Bug列表失败: {str(e)}"
        )


# Bug相关工具
@router.post("/zentao_get_bugs_by_time_range", response_model=Dict[str, Any])
async def zentao_get_bugs_by_time_range(
    start_date: str,
    end_date: str,
    dept_id: Optional[int] = None,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    bug_service: BugService = Depends(get_bug_service)
):
    """根据时间段查询Bug列表"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_bugs_by_time_range")
        if dept_id is not None:
            response = await bug_service.get_bugs_by_time_range_and_dept(start_date, end_date, dept_id)
        else:
            response = await bug_service.get_bugs_by_time_range(start_date, end_date)
        logger.info(f"成功获取Bug列表，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"根据时间范围获取Bug列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据时间范围获取Bug列表失败: {str(e)}"
        )


@router.post("/zentao_get_bug_detail", response_model=Dict[str, Any])
async def zentao_get_bug_detail(
    bug_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    bug_service: BugService = Depends(get_bug_service)
):
    """根据Bug ID查询Bug详情"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_bug_detail，Bug ID: {bug_id}")
        response = await bug_service.get_bug_detail(bug_id)
        logger.info(f"成功获取Bug {bug_id} 详情，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"获取Bug详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取Bug详情失败: {str(e)}"
        )


@router.post("/zentao_get_personal_bugs", response_model=Dict[str, Any])
async def zentao_get_personal_bugs(
    account: str,
    status: str,
    start_date: str,
    end_date: str,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    bug_service: BugService = Depends(get_bug_service)
):
    """查询个人Bug"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_personal_bugs，账号: {account}")
        response = await bug_service.get_personal_bugs(account, status, start_date, end_date)
        logger.info(f"成功获取账号 {account} 个人Bug，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"获取个人Bug失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取个人Bug失败: {str(e)}"
        )


# 任务相关工具
@router.post("/zentao_get_task_detail", response_model=Dict[str, Any])
async def zentao_get_task_detail(
    task_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    task_service: TaskService = Depends(get_task_service)
):
    """根据任务ID查询任务详情"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_task_detail，任务ID: {task_id}")
        response = await task_service.get_task_detail(task_id)
        logger.info(f"成功获取任务 {task_id} 详情，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"获取任务详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务详情失败: {str(e)}"
        )


@router.post("/zentao_get_personal_tasks", response_model=Dict[str, Any])
async def zentao_get_personal_tasks(
    account: str,
    status: str,
    start_date: str,
    end_date: str,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    task_service: TaskService = Depends(get_task_service)
):
    """查询个人任务"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_personal_tasks，账号: {account}")
        response = await task_service.get_personal_tasks(account, status, start_date, end_date)
        logger.info(f"成功获取账号 {account} 个人任务，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"获取个人任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取个人任务失败: {str(e)}"
        )


# 需求相关工具
@router.post("/zentao_get_story_detail", response_model=Dict[str, Any])
async def zentao_get_story_detail(
    story_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    story_service: StoryService = Depends(get_story_service)
):
    """根据需求ID查询需求详情"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_story_detail，需求ID: {story_id}")
        response = await story_service.get_story_detail(story_id)
        logger.info(f"成功获取需求 {story_id} 详情，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"获取需求详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取需求详情失败: {str(e)}"
        )


# 部门相关工具
@router.post("/zentao_get_all_departments", response_model=Dict[str, Any])
async def zentao_get_all_departments(
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    department_service: DepartmentService = Depends(get_department_service)
):
    """获取所有部门列表"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_all_departments")
        response = await department_service.get_all_departments()
        logger.info(f"成功获取部门列表，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"获取所有部门列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取所有部门列表失败: {str(e)}"
        )


# 用户相关工具
@router.post("/zentao_get_user_info", response_model=Dict[str, Any])
async def zentao_get_user_info(
    user_account: str,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    user_service: UserService = Depends(get_user_service)
):
    """根据用户账号获取用户信息"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_user_info，账号: {user_account}")
        response = await user_service.get_user_info(user_account)
        logger.info(f"成功获取用户 {user_account} 信息，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"根据用户账号获取用户信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据用户账号获取用户信息失败: {str(e)}"
        )


@router.post("/zentao_get_users_by_accounts", response_model=Dict[str, Any])
async def zentao_get_users_by_accounts(
    accounts: list[str],
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    user_service: UserService = Depends(get_user_service)
):
    """根据用户账号列表获取用户信息"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_users_by_accounts")
        response = await user_service.get_users_by_accounts(accounts)
        logger.info(f"成功获取用户信息，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"根据用户账号列表获取用户信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据用户账号列表获取用户信息失败: {str(e)}"
        )


@router.post("/zentao_get_users_by_account", response_model=Dict[str, Any])
async def zentao_get_users_by_account(
    accounts: list[str],
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    user_service: UserService = Depends(get_user_service)
):
    """根据域账号批量查询用户信息（API映射表要求的接口名）"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_users_by_account")
        response = await user_service.get_users_by_accounts(accounts)
        logger.info(f"成功获取用户信息，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"根据域账号批量查询用户信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据域账号批量查询用户信息失败: {str(e)}"
        )


# 分析相关工具
@router.post("/zentao_get_project_analysis", response_model=Dict[str, Any])
async def zentao_get_project_analysis(
    project_id: int,
    start_date: str,
    end_date: str,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """获取项目分析数据"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_project_analysis，项目ID: {project_id}")
        response = await analysis_service.get_project_analysis(project_id, start_date, end_date)
        logger.info(f"成功获取项目 {project_id} 分析数据，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"获取项目分析数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目分析数据失败: {str(e)}"
        )


# 部门用户相关工具
@router.post("/zentao_get_users_by_dept", response_model=Dict[str, Any])
async def zentao_get_users_by_dept(
    dept_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    department_service: DepartmentService = Depends(get_department_service)
):
    """根据部门查询用户列表"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_users_by_dept，部门ID: {dept_id}")
        response = await department_service.get_users_by_dept(dept_id)
        logger.info(f"成功获取部门 {dept_id} 用户列表，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"根据部门查询用户列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据部门查询用户列表失败: {str(e)}"
        )


# 需求工时相关工具
@router.post("/zentao_get_story_effort", response_model=Dict[str, Any])
async def zentao_get_story_effort(
    story_ids: list[int],
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    story_service: StoryService = Depends(get_story_service)
):
    """批量获取需求工时信息"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_story_effort，需求IDs: {story_ids}")
        response = await story_service.get_story_info(story_ids[0])  # 需要服务层支持批量
        logger.info(f"成功获取需求工时信息，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"批量获取需求工时信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量获取需求工时信息失败: {str(e)}"
        )


@router.post("/zentao_get_story_completed_effort", response_model=Dict[str, Any])
async def zentao_get_story_completed_effort(
    story_ids: list[int],
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    story_service: StoryService = Depends(get_story_service)
):
    """获取已完成需求任务工时"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_story_completed_effort，需求IDs: {story_ids}")
        response = await story_service.get_stories_end_info(story_ids)
        logger.info(f"成功获取已完成需求工时，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"获取已完成需求任务工时失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取已完成需求任务工时失败: {str(e)}"
        )


@router.post("/zentao_check_story_exists", response_model=Dict[str, Any])
async def zentao_check_story_exists(
    story_ids: list[int],
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    story_service: StoryService = Depends(get_story_service)
):
    """批量检查需求是否存在"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_check_story_exists，需求IDs: {story_ids}")
        response = await story_service.check_stories_exist(story_ids)
        logger.info(f"成功检查需求存在性，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"批量检查需求是否存在失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量检查需求是否存在失败: {str(e)}"
        )


@router.post("/zentao_get_stories_by_time", response_model=Dict[str, Any])
async def zentao_get_stories_by_time(
    start_date: str,
    end_date: str,
    project_id: Optional[int] = None,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    story_service: StoryService = Depends(get_story_service)
):
    """根据时间段查询需求"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_stories_by_time")
        response = await story_service.get_stories_by_time_range(start_date, end_date, project_id)
        logger.info(f"成功根据时间段查询需求，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"根据时间段查询需求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据时间段查询需求失败: {str(e)}"
        )


# 任务查询相关工具
@router.post("/zentao_get_tasks_by_account", response_model=Dict[str, Any])
async def zentao_get_tasks_by_account(
    account: str,
    start_date: str,
    end_date: str,
    is_doing: bool = False,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    task_service: TaskService = Depends(get_task_service)
):
    """根据域账号查询任务"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_tasks_by_account，账号: {account}")
        response = await task_service.get_tasks_by_account(account, start_date, end_date, is_doing)
        logger.info(f"成功根据账号查询任务，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"根据域账号查询任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据域账号查询任务失败: {str(e)}"
        )


@router.post("/zentao_get_tasks_by_dept", response_model=Dict[str, Any])
async def zentao_get_tasks_by_dept(
    dept_id: int,
    start_date: str,
    end_date: str,
    is_doing: bool = False,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    task_service: TaskService = Depends(get_task_service)
):
    """根据部门查询任务"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_tasks_by_dept，部门ID: {dept_id}")
        response = await task_service.get_tasks_by_dept(dept_id, start_date, end_date, is_doing)
        logger.info(f"成功根据部门查询任务，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"根据部门查询任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据部门查询任务失败: {str(e)}"
        )


# Bug查询相关工具
@router.post("/zentao_get_bugs_by_time_and_dept", response_model=Dict[str, Any])
async def zentao_get_bugs_by_time_and_dept(
    start_date: str,
    end_date: str,
    dept_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    bug_service: BugService = Depends(get_bug_service)
):
    """根据时间段和部门查询Bug"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_bugs_by_time_and_dept")
        response = await bug_service.get_bugs_by_time_and_dept(start_date, end_date, dept_id)
        logger.info(f"成功根据时间段和部门查询Bug，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"根据时间段和部门查询Bug失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据时间段和部门查询Bug失败: {str(e)}"
        )


# 数据加工接口层 - 基于直接接口的二次封装

@router.post("/analyze_story_workload", response_model=Dict[str, Any])
async def analyze_story_workload(
    story_ids: Optional[list[int]] = None,
    project_id: Optional[int] = None,
    include_completed: bool = True,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    story_service: StoryService = Depends(get_story_service)
):
    """分析需求工时统计"""
    try:
        logger.info(f"用户 {user_identifier} 调用 analyze_story_workload")
        response = await story_service.analyze_story_workload(story_ids, project_id, include_completed)
        logger.info(f"成功分析需求工时统计，响应码: {response.get('success')}")
        return response
    except Exception as e:
        logger.error(f"分析需求工时统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分析需求工时统计失败: {str(e)}"
        )


@router.post("/analyze_bugs_by_dept_and_time", response_model=Dict[str, Any])
async def analyze_bugs_by_dept_and_time(
    start_date: str,
    end_date: str,
    dept_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    bug_service: BugService = Depends(get_bug_service)
):
    """按部门和时间段统计Bug分析"""
    try:
        logger.info(f"用户 {user_identifier} 调用 analyze_bugs_by_dept_and_time")
        response = await bug_service.analyze_bugs_by_dept_and_time(start_date, end_date, dept_id)
        logger.info(f"成功分析部门Bug统计，响应码: {response.get('success')}")
        return response
    except Exception as e:
        logger.error(f"按部门和时间段统计Bug分析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"按部门和时间段统计Bug分析失败: {str(e)}"
        )


@router.post("/project_summary_analysis", response_model=Dict[str, Any])
async def project_summary_analysis(
    project_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """项目整体统计分析"""
    try:
        logger.info(f"用户 {user_identifier} 调用 project_summary_analysis")
        response = await analysis_service.project_summary_analysis(project_id)
        logger.info(f"成功分析项目整体统计，响应码: {response.get('success')}")
        return response
    except Exception as e:
        logger.error(f"项目整体统计分析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"项目整体统计分析失败: {str(e)}"
        )


@router.post("/personnel_workload_analysis", response_model=Dict[str, Any])
async def personnel_workload_analysis(
    user_accounts: Optional[list[str]] = None,
    project_id: Optional[int] = None,
    dept_id: Optional[int] = None,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """人员工作量统计和分析"""
    try:
        logger.info(f"用户 {user_identifier} 调用 personnel_workload_analysis")
        response = await analysis_service.personnel_workload_analysis(user_accounts, project_id, dept_id)
        logger.info(f"成功分析人员工作量，响应码: {response.get('success')}")
        return response
    except Exception as e:
        logger.error(f"人员工作量统计和分析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"人员工作量统计和分析失败: {str(e)}"
        )


@router.post("/story_task_relation_query", response_model=Dict[str, Any])
async def story_task_relation_query(
    story_ids: Optional[list[int]] = None,
    project_id: Optional[int] = None,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """需求-任务关联查询"""
    try:
        logger.info(f"用户 {user_identifier} 调用 story_task_relation_query")
        response = await analysis_service.story_task_relation_query(story_ids, project_id)
        logger.info(f"成功查询需求任务关联，响应码: {response.get('success')}")
        return response
    except Exception as e:
        logger.error(f"需求-任务关联查询失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"需求-任务关联查询失败: {str(e)}"
        )


@router.post("/bug_to_story_tracking", response_model=Dict[str, Any])
async def bug_to_story_tracking(
    bug_ids: Optional[list[int]] = None,
    project_id: Optional[int] = None,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """Bug转需求追踪"""
    try:
        logger.info(f"用户 {user_identifier} 调用 bug_to_story_tracking")
        response = await analysis_service.bug_to_story_tracking(bug_ids, project_id)
        logger.info(f"成功追踪Bug转需求，响应码: {response.get('success')}")
        return response
    except Exception as e:
        logger.error(f"Bug转需求追踪失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Bug转需求追踪失败: {str(e)}"
        )


@router.post("/filter_bugs_by_criteria", response_model=Dict[str, Any])
async def filter_bugs_by_criteria(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    status_filter: Optional[list[str]] = None,
    severity_filter: Optional[list[int]] = None,
    project_filter: Optional[list[str]] = None,
    assignee_filter: Optional[list[str]] = None,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    bug_service: BugService = Depends(get_bug_service)
):
    """根据多种条件过滤Bug"""
    try:
        logger.info(f"用户 {user_identifier} 调用 filter_bugs_by_criteria")
        response = await bug_service.filter_bugs_by_criteria(
            start_date, end_date, status_filter, severity_filter, project_filter, assignee_filter
        )
        logger.info(f"成功过滤Bug，响应码: {response.get('success')}")
        return response
    except Exception as e:
        logger.error(f"根据多种条件过滤Bug失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据多种条件过滤Bug失败: {str(e)}"
        )


@router.post("/batch_query_stories", response_model=Dict[str, Any])
async def batch_query_stories(
    story_ids: list[int],
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    story_service: StoryService = Depends(get_story_service)
):
    """批量查询需求信息"""
    try:
        logger.info(f"用户 {user_identifier} 调用 batch_query_stories")
        response = await story_service.batch_query_stories(story_ids)
        logger.info(f"成功批量查询需求，响应码: {response.get('success')}")
        return response
    except Exception as e:
        logger.error(f"批量查询需求信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量查询需求信息失败: {str(e)}"
        )


@router.post("/validate_story_existence", response_model=Dict[str, Any])
async def validate_story_existence(
    story_ids: list[int],
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    story_service: StoryService = Depends(get_story_service)
):
    """验证需求是否存在"""
    try:
        logger.info(f"用户 {user_identifier} 调用 validate_story_existence")
        response = await story_service.validate_story_existence(story_ids)
        logger.info(f"成功验证需求存在性，响应码: {response.get('success')}")
        return response
    except Exception as e:
        logger.error(f"验证需求是否存在失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"验证需求是否存在失败: {str(e)}"
        )


# 系统相关工具
@router.post("/zentao_get_system_info", response_model=Dict[str, Any])
async def zentao_get_system_info(
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    system_service: SystemService = Depends(get_system_service)
):
    """获取系统信息"""
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_system_info")
        response = await system_service.get_system_info()
        logger.info(f"成功获取系统信息，响应码: {response.get('rsCode')}")
        return response
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统信息失败: {str(e)}"
        )


@router.post("/mcp_get_health_status", response_model=Dict[str, Any])
async def mcp_get_health_status(
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    system_service: SystemService = Depends(get_system_service)
):
    """获取服务健康状态"""
    try:
        logger.info(f"用户 {user_identifier} 调用 mcp_get_health_status")
        response = await system_service.health_check()
        logger.info(f"成功获取健康状态，响应码: {response.get('success')}")
        return response
    except Exception as e:
        logger.error(f"获取服务健康状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取服务健康状态失败: {str(e)}"
        )


@router.post("/mcp_get_performance_metrics", response_model=Dict[str, Any])
async def mcp_get_performance_metrics(
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    system_service: SystemService = Depends(get_system_service)
):
    """获取性能指标"""
    try:
        logger.info(f"用户 {user_identifier} 调用 mcp_get_performance_metrics")
        response = await system_service.get_performance_metrics()
        logger.info(f"成功获取性能指标，响应码: {response.get('success')}")
        return response
    except Exception as e:
        logger.error(f"获取性能指标失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取性能指标失败: {str(e)}"
        )