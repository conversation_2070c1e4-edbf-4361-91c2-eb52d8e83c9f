"""
Bug管理API端点
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import verify_api_key
from app.core.dependencies import get_bug_service
from app.services.bug_service import BugService
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/by-time-range", response_model=Dict[str, Any])
async def get_bugs_by_time_range(
    start_date: str = Query(..., description="开始日期 (YYYY-MM-DD HH:MM:SS格式)"),
    end_date: str = Query(..., description="结束日期 (YYYY-MM-DD HH:MM:SS格式)"),
    dept_id: Optional[int] = Query(None, description="部门ID（可选）"),
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """
    根据时间段查询Bug列表，可选择按部门过滤
    
    RESTful API: /apiData/getBugListByTimeRange 或 /apiData/getBugListByTimeRangeAndDeptId
    调用服务层获取数据
    """
    try:
        logger.info(f"用户 {user_identifier} 请求获取时间范围 {start_date} 到 {end_date} 的Bug列表")
        if dept_id is not None:
            logger.info(f"按部门 {dept_id} 过滤Bug")
            response = await bug_service.get_bugs_by_time_range_and_dept(start_date, end_date, dept_id)
        else:
            response = await bug_service.get_bugs_by_time_range(start_date, end_date)
        
        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )
        
        logger.info(f"成功获取Bug列表，响应码: {response.get('rsCode')}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"根据时间范围获取Bug列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据时间范围获取Bug列表失败: {str(e)}"
        )


@router.get("/{bug_id}", response_model=Dict[str, Any])
async def get_bug_detail(
    bug_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    bug_service: BugService = Depends(get_bug_service)
):
    """
    根据Bug ID查询Bug详情
    
    RESTful API: /api/getBugDetail
    调用服务层获取数据
    """
    try:
        logger.info(f"用户 {user_identifier} 请求获取Bug {bug_id} 的详情")
        response = await bug_service.get_bug_detail(bug_id)
        
        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )
        
        logger.info(f"成功获取Bug {bug_id} 详情，响应码: {response.get('rsCode')}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取Bug详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取Bug详情失败: {str(e)}"
        )


@router.get("/personal/{account}", response_model=Dict[str, Any])
async def get_personal_bugs(
    account: str,
    status: str = Query(..., description="Bug状态 (active/resolved/released/closed)"),
    start_date: str = Query(..., description="开始日期 (YYYY-MM-DD HH:MM:SS格式)"),
    end_date: str = Query(..., description="结束日期 (YYYY-MM-DD HH:MM:SS格式)"),
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """
    查询个人Bug
    
    RESTful API: /api/getPersonalBugs
    调用服务层获取数据
    """
    try:
        logger.info(f"用户 {user_identifier} 请求获取账号 {account} 的个人Bug")
        response = await bug_service.get_personal_bugs(account, status, start_date, end_date)
        
        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )
        
        logger.info(f"成功获取账号 {account} 个人Bug，响应码: {response.get('rsCode')}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取个人Bug失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取个人Bug失败: {str(e)}"
        )
