"""
数据分析API端点

提供Bug分析、项目分析、需求分析等增强功能
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import verify_api_key
from app.core.dependencies import get_analysis_service
from app.services.analysis_service import AnalysisService
from typing import Any, Dict
from datetime import datetime

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/bugs/by-dept-time", response_model=Dict[str, Any])
async def analyze_bugs_by_dept_and_time(
    start_date: str = Query(..., description="开始日期 (YYYY-MM-DD格式)"),
    end_date: str = Query(..., description="结束日期 (YYYY-MM-DD格式)"),
    dept_id: int = Query(..., description="部门ID"),
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """
    按部门和时间段统计Bug分析
    
    基于直接接口层实现数据聚合和分析功能
    """
    try:
        logger.info(f"用户 {user_identifier} 请求分析部门 {dept_id} 在 {start_date} 到 {end_date} 的Bug")
        
        # 验证日期格式
        try:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")
            
            if start_dt > end_dt:
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail="开始日期不能晚于结束日期"
                )
                
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=f"日期格式错误，应为YYYY-MM-DD格式: {str(e)}"
            )        # 获取部门时间范围内的Bug列表
        response = await analysis_service.get_bugs_by_time_range_and_dept(start_date, end_date, dept_id)
        
        if not response or response.get("rsCode") != "00000000":
            logger.warning(f"获取部门时间范围Bug列表失败: {response}")
            return {
                "success": False,
                "message": "获取部门时间范围Bug列表失败",
                "start_date": start_date,
                "end_date": end_date,
                "dept_id": dept_id,
                "analysis": {}
            }
        
        bugs_data = response.get("body", [])
        
        if not isinstance(bugs_data, list):
            logger.warning(f"Bug数据格式错误: {bugs_data}")
            return {
                "success": False,
                "message": "Bug数据格式错误",
                "start_date": start_date,
                "end_date": end_date,
                "dept_id": dept_id,
                "analysis": {}
            }
        
        # 统计分析
        bugs = []
        status_stats = {}
        severity_stats = {}
        project_stats = {}
        assignee_stats = {}
        daily_stats = {}
        
        for bug_data in bugs_data:
            try:
                bug = _format_bug_data(bug_data)
                bugs.append(bug)
                
                # 状态统计
                status = bug.get("status", "unknown")
                status_stats[status] = status_stats.get(status, 0) + 1
                
                # 严重程度统计
                severity = bug.get("severity", 2)
                severity_name = _get_severity_name(severity)
                severity_stats[severity_name] = severity_stats.get(severity_name, 0) + 1
                
                # 项目统计
                project_name = bug.get("projectname", "未知项目")
                project_stats[project_name] = project_stats.get(project_name, 0) + 1
                
                # 指派人统计
                assignee = bug.get("assignedTo", "未指派")
                assignee_stats[assignee] = assignee_stats.get(assignee, 0) + 1
                
                # 按日统计
                opened_date = bug.get("openedDate", "")
                if opened_date:
                    try:
                        if "T" in opened_date:
                            date_part = opened_date.split("T")[0]
                        else:
                            date_part = opened_date.split(" ")[0]
                        daily_stats[date_part] = daily_stats.get(date_part, 0) + 1
                    except Exception:
                        pass
                
            except Exception as e:
                logger.warning(f"Bug数据格式化失败: {bug_data}, 错误: {e}")
                continue
        
        # 计算统计指标
        total_bugs = len(bugs)
        
        # 计算解决率
        resolved_count = status_stats.get("resolved", 0) + status_stats.get("closed", 0)
        resolution_rate = (resolved_count / total_bugs * 100) if total_bugs > 0 else 0
        
        # 计算严重Bug比例
        critical_count = severity_stats.get("严重", 0) + severity_stats.get("冒烟", 0)
        critical_rate = (critical_count / total_bugs * 100) if total_bugs > 0 else 0
        
        # 获取部门信息
        dept_name = "未知部门"
        try:
            dept_response = await analysis_service.get_all_departments()
            if dept_response and dept_response.get("rsCode") == "00000000":
                departments = dept_response.get("body", [])
                for dept in departments:
                    if dept.get("id") == dept_id:
                        dept_name = dept.get("name", "未知部门")
                        break
        except Exception as e:
            logger.warning(f"获取部门信息失败: {e}")
        
        result = {
            "success": True,
            "message": f"部门 {dept_name} 在 {start_date} 到 {end_date} 期间共有 {total_bugs} 个Bug",
            "start_date": start_date,
            "end_date": end_date,
            "dept_id": dept_id,
            "dept_name": dept_name,
            "analysis": {
                "summary": {
                    "total_bugs": total_bugs,
                    "resolved_bugs": resolved_count,
                    "resolution_rate": round(resolution_rate, 2),
                    "critical_bugs": critical_count,
                    "critical_rate": round(critical_rate, 2)
                },
                "status_distribution": {
                    status: {
                        "count": count,
                        "percentage": round((count / total_bugs * 100), 2) if total_bugs > 0 else 0
                    }
                    for status, count in status_stats.items()
                },
                "severity_distribution": {
                    severity: {
                        "count": count,
                        "percentage": round((count / total_bugs * 100), 2) if total_bugs > 0 else 0
                    }
                    for severity, count in severity_stats.items()
                },
                "project_distribution": dict(sorted(project_stats.items(), key=lambda x: x[1], reverse=True)[:10]),
                "assignee_distribution": dict(sorted(assignee_stats.items(), key=lambda x: x[1], reverse=True)[:10]),
                "daily_trend": dict(sorted(daily_stats.items()))
            },
            "bugs": bugs
        }
        
        logger.info(f"部门Bug统计分析完成: {result['message']}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"按部门和时间段统计Bug失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"按部门和时间段统计Bug失败: {str(e)}"
        )


@router.get("/projects/{project_id}/health", response_model=Dict[str, Any])
async def analyze_project_health(
    project_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """
    分析项目健康度
    
    基于直接接口层实现数据聚合和分析功能
    """
    try:
        logger.info(f"用户 {user_identifier} 请求分析项目 {project_id} 健康度")        # 获取项目相关数据
        tasks_response = await analysis_service.get_tasks_by_project(project_id)
        stories_response = await analysis_service.get_stories_by_project(project_id)
        bugs_response = await analysis_service.get_bugs_by_project(project_id)
        
        # 统计分析
        tasks_count = len(tasks_response.get("body", [])) if tasks_response.get("rsCode") == "00000000" else 0
        stories_count = len(stories_response.get("body", [])) if stories_response.get("rsCode") == "00000000" else 0
        bugs_count = len(bugs_response.get("body", [])) if bugs_response.get("rsCode") == "00000000" else 0
        
        # 计算健康度指标
        total_items = tasks_count + stories_count
        bug_ratio = (bugs_count / total_items * 100) if total_items > 0 else 0
        
        # 健康度评级
        if bug_ratio < 5:
            health_grade = "优秀"
        elif bug_ratio < 15:
            health_grade = "良好"
        elif bug_ratio < 30:
            health_grade = "一般"
        else:
            health_grade = "需要关注"
        
        result = {
            "success": True,
            "message": f"项目 {project_id} 健康度评级: {health_grade}",
            "project_id": project_id,
            "analysis": {
                "summary": {
                    "tasks_count": tasks_count,
                    "stories_count": stories_count,
                    "bugs_count": bugs_count,
                    "bug_ratio": round(bug_ratio, 2),
                    "health_grade": health_grade
                }
            }
        }
        
        logger.info(f"项目健康度分析完成: {result['message']}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分析项目健康度失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分析项目健康度失败: {str(e)}"
        )


@router.get("/stories/completion-rate/{project_id}", response_model=Dict[str, Any])
async def analyze_story_completion_rate(
    project_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """
    分析项目需求完成率
    
    基于直接接口层实现数据聚合和分析功能
    """
    try:
        logger.info(f"用户 {user_identifier} 请求分析项目 {project_id} 需求完成率")        # 获取项目需求列表
        response = await analysis_service.get_stories_by_project(project_id)
        
        if not response or response.get("rsCode") != "00000000":
            logger.warning(f"获取项目需求列表失败: {response}")
            return {
                "success": False,
                "message": "获取项目需求列表失败",
                "project_id": project_id,
                "analysis": {}
            }
        
        stories_data = response.get("body", [])
        
        if not isinstance(stories_data, list):
            logger.warning(f"需求数据格式错误: {stories_data}")
            return {
                "success": False,
                "message": "需求数据格式错误",
                "project_id": project_id,
                "analysis": {}
            }
        
        # 统计分析
        total_stories = len(stories_data)
        status_stats = {}
        stage_stats = {}
        
        for story in stories_data:
            status = story.get("status", "unknown")
            stage = story.get("stage", "unknown")
            
            status_stats[status] = status_stats.get(status, 0) + 1
            stage_stats[stage] = stage_stats.get(stage, 0) + 1
        
        # 计算完成率
        closed_count = status_stats.get("closed", 0)
        completion_rate = (closed_count / total_stories * 100) if total_stories > 0 else 0
        
        result = {
            "success": True,
            "message": f"项目 {project_id} 共有 {total_stories} 个需求，完成率 {completion_rate:.2f}%",
            "project_id": project_id,
            "analysis": {
                "summary": {
                    "total_stories": total_stories,
                    "closed_stories": closed_count,
                    "completion_rate": round(completion_rate, 2)
                },
                "status_distribution": status_stats,
                "stage_distribution": stage_stats
            },
            "stories": stories_data
        }
        
        logger.info(f"需求完成率分析完成: {result['message']}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分析项目需求完成率失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分析项目需求完成率失败: {str(e)}"
        )


def _get_severity_name(severity: int) -> str:
    """
    获取严重程度名称
    
    Args:
        severity: 严重程度数值
        
    Returns:
        str: 严重程度名称
    """
    severity_names = {
        0: "冒烟",
        1: "严重", 
        2: "一般",
        3: "次要",
        4: "低级"
    }
    return severity_names.get(severity, "未知")


def _format_bug_data(bug_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    格式化Bug数据
    
    Args:
        bug_data: 原始Bug数据
        
    Returns:
        Dict: 格式化后的Bug数据
    """
    return {
        "id": int(bug_data.get("id", 0)),
        "title": str(bug_data.get("title", "")),
        "status": str(bug_data.get("status", "active")),
        "severity": int(bug_data.get("severity", 2)),
        "project": int(bug_data.get("project", 0)),
        "projectname": str(bug_data.get("projectname", "")),
        "assignedTo": str(bug_data.get("assignedTo", "")),
        "openedBy": str(bug_data.get("openedBy", "")),
        "openedDate": str(bug_data.get("openedDate", "")),
        "resolution": str(bug_data.get("resolution", "")),
        "environment": str(bug_data.get("environment", "")),
        "steps": str(bug_data.get("steps", ""))
    }