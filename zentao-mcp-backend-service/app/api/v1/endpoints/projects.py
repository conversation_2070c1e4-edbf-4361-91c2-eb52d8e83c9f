"""
项目管理API端点
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import verify_api_key
from app.core.dependencies import get_project_service
from app.services.project_service import ProjectService
from typing import Any, Dict, List

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=Dict[str, Any])
async def get_all_projects(
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    project_service: ProjectService = Depends(get_project_service)
):
    """
    获取所有项目列表
    
    RESTful API: GET /api/v1/projects/
    调用服务层获取项目数据
    """
    try:
        logger.info(f"用户 {user_identifier} 请求获取所有项目列表")
        
        response = await project_service.get_all_projects()
        
        logger.info(f"成功获取项目列表，响应码: {response.get('rsCode')}")
        return response
        
    except Exception as e:
        logger.error(f"获取所有项目列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取所有项目列表失败: {str(e)}"
        )


@router.get("/{project_id}/tasks", response_model=Dict[str, Any])
async def get_project_tasks(
    project_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    project_service: ProjectService = Depends(get_project_service)
):
    """
    根据项目ID查询任务列表
    
    RESTful API: GET /api/v1/projects/{project_id}/tasks
    调用服务层获取项目任务数据
    """
    try:
        logger.info(f"用户 {user_identifier} 请求获取项目 {project_id} 的任务列表")
        
        response = await project_service.get_tasks_by_project(project_id)
        
        logger.info(f"成功获取项目 {project_id} 任务列表，响应码: {response.get('rsCode')}")
        return response
        
    except Exception as e:
        logger.error(f"根据项目ID查询任务列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据项目ID查询任务列表失败: {str(e)}"
        )


@router.get("/{project_id}/stories", response_model=Dict[str, Any])
async def get_project_stories(
    project_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    project_service: ProjectService = Depends(get_project_service)
):
    """
    根据项目ID查询需求列表
    
    RESTful API: GET /api/v1/projects/{project_id}/stories
    调用服务层获取项目需求数据
    """
    try:
        logger.info(f"用户 {user_identifier} 请求获取项目 {project_id} 的需求列表")
        
        response = await project_service.get_stories_by_project(project_id)
        
        logger.info(f"成功获取项目 {project_id} 需求列表，响应码: {response.get('rsCode')}")
        return response
        
    except Exception as e:
        logger.error(f"根据项目ID查询需求列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据项目ID查询需求列表失败: {str(e)}"
        )


@router.get("/{project_id}/bugs", response_model=Dict[str, Any])
async def get_project_bugs(
    project_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    project_service: ProjectService = Depends(get_project_service)
):
    """
    根据项目ID查询Bug列表
    
    RESTful API: GET /api/v1/projects/{project_id}/bugs
    调用服务层获取项目Bug数据
    """
    try:
        logger.info(f"用户 {user_identifier} 请求获取项目 {project_id} 的Bug列表")
        
        response = await project_service.get_bugs_by_project(project_id)
        
        logger.info(f"成功获取项目 {project_id} Bug列表，响应码: {response.get('rsCode')}")
        return response
        
    except Exception as e:
        logger.error(f"根据项目ID查询Bug列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据项目ID查询Bug列表失败: {str(e)}"
        )
