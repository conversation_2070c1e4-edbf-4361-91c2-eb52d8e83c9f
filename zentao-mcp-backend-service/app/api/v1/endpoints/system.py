"""
系统管理API端点

提供系统健康检查和状态监控功能
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import verify_api_key
from app.core.dependencies import get_system_service
from app.services.system_service import SystemService
from typing import Any, Dict
from datetime import datetime

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/health", response_model=Dict[str, Any])
async def health_check(
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    system_service: SystemService = Depends(get_system_service)
):
    """
    系统健康检查
    
    检查禅道连接状态和系统基本信息
    """
    try:
        logger.info(f"用户 {user_identifier} 请求系统健康检查")        # 尝试获取部门列表来测试连接
        response = await system_service.get_all_departments()
        
        if response and response.get("rsCode") == "00000000":
            connection_status = "正常"
            connection_message = "禅道API连接正常"
        else:
            connection_status = "异常"
            connection_message = f"禅道API连接异常: {response}"
        
        # 获取性能统计（如果客户端支持）
        performance_stats = {}
        try:
            if hasattr(zentao_service, 'get_performance_stats'):
                performance_stats = system_service.get_performance_stats()
        except Exception as e:
            logger.warning(f"获取性能统计失败: {e}")
            performance_stats = {"error": "性能统计不可用"}
        
        result = {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "system_status": {
                "connection_status": connection_status,
                "connection_message": connection_message,
                "performance_stats": performance_stats
            }
        }
        
        logger.info(f"系统健康检查完成: {connection_status}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"系统健康检查失败: {e}")
        return {
            "success": False,
            "timestamp": datetime.now().isoformat(),
            "error": f"系统健康检查失败: {str(e)}"
        }