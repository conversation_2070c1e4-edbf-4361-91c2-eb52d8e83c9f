"""
MCP资源API端点 - 按设计文档要求的格式
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import verify_api_key
from typing import Any, Dict

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/project_list", response_model=Dict[str, Any])
async def get_project_list_resource(
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """获取项目列表资源"""
    try:
        logger.info(f"用户 {user_identifier} 请求项目列表资源")
        # 这里可以返回项目列表的元数据或缓存数据
        return {
            "resource_type": "project_list",
            "description": "禅道项目列表资源",
            "last_updated": "2025-09-01T01:30:00Z",
            "status": "available"
        }
    except Exception as e:
        logger.error(f"获取项目列表资源失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目列表资源失败: {str(e)}"
        )


@router.get("/bug_list", response_model=Dict[str, Any])
async def get_bug_list_resource(
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """获取Bug列表资源"""
    try:
        logger.info(f"用户 {user_identifier} 请求Bug列表资源")
        return {
            "resource_type": "bug_list",
            "description": "禅道Bug列表资源",
            "last_updated": "2025-09-01T01:30:00Z",
            "status": "available"
        }
    except Exception as e:
        logger.error(f"获取Bug列表资源失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取Bug列表资源失败: {str(e)}"
        )


@router.get("/task_list", response_model=Dict[str, Any])
async def get_task_list_resource(
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """获取任务列表资源"""
    try:
        logger.info(f"用户 {user_identifier} 请求任务列表资源")
        return {
            "resource_type": "task_list",
            "description": "禅道任务列表资源",
            "last_updated": "2025-09-01T01:30:00Z",
            "status": "available"
        }
    except Exception as e:
        logger.error(f"获取任务列表资源失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务列表资源失败: {str(e)}"
        )


@router.get("/story_list", response_model=Dict[str, Any])
async def get_story_list_resource(
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """获取需求列表资源"""
    try:
        logger.info(f"用户 {user_identifier} 请求需求列表资源")
        return {
            "resource_type": "story_list",
            "description": "禅道需求列表资源",
            "last_updated": "2025-09-01T01:30:00Z",
            "status": "available"
        }
    except Exception as e:
        logger.error(f"获取需求列表资源失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取需求列表资源失败: {str(e)}"
        )