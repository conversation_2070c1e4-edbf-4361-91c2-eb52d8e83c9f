"""
用户管理服务

封装用户相关的业务逻辑
"""

import logging
from typing import Dict, Any, List
from .base import BaseService
from app.zentao_engine.exceptions import ZentaoEngineError

logger = logging.getLogger(__name__)


class UserService(BaseService):
    """用户管理服务类"""
    
    async def get_user_info(self, user_account: str) -> Dict[str, Any]:
        """
        根据用户账号获取用户信息
        
        直接调用禅道API: /api/getUserInfo
        保持原始数据结构
        
        Args:
            user_account: 用户账号
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API根据用户账号 {user_account} 获取用户信息")
            
            response = await self.zentao_service.get_user_info(user_account)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功获取用户 {user_account} 信息，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"根据用户账号获取用户信息失败: {e}")
            raise ZentaoEngineError(f"根据用户账号获取用户信息失败: {str(e)}")
    
    async def get_users_by_accounts(self, accounts: List[str]) -> Dict[str, Any]:
        """
        根据用户账号列表获取用户信息
        
        直接调用禅道API: /api/getUsersByAccounts
        保持原始数据结构
        
        Args:
            accounts: 用户账号列表
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API根据用户账号列表获取用户信息: {accounts}")
            
            response = await self.zentao_service.get_users_by_accounts(accounts)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功获取用户信息，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"根据用户账号列表获取用户信息失败: {e}")
            raise ZentaoEngineError(f"根据用户账号列表获取用户信息失败: {str(e)}")