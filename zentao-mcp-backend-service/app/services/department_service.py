"""
部门管理服务

封装部门相关的业务逻辑
"""

import logging
from typing import Dict, Any
from .base import BaseService
from app.zentao_engine.exceptions import ZentaoEngineError

logger = logging.getLogger(__name__)


class DepartmentService(BaseService):
    """部门管理服务类"""
    
    async def get_all_departments(self) -> Dict[str, Any]:
        """
        获取所有部门列表
        
        直接调用禅道API: /apiData/getAllDept
        保持原始数据结构
        
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info("调用禅道API获取所有部门列表")
            
            response = await self.zentao_service.get_all_departments()
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功获取部门列表，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"获取所有部门列表失败: {e}")
            raise ZentaoEngineError(f"获取所有部门列表失败: {str(e)}")
    
    async def get_users_by_dept(self, dept_id: int) -> Dict[str, Any]:
        """
        根据部门查询用户列表
        
        直接调用禅道API: /api/getUserByDept
        保持原始数据结构
        
        Args:
            dept_id: 部门ID
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API根据部门ID {dept_id} 查询用户列表")
            
            response = await self.zentao_service.get_users_by_department(dept_id)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功获取部门 {dept_id} 用户列表，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"根据部门查询用户列表失败: {e}")
            raise ZentaoEngineError(f"根据部门查询用户列表失败: {str(e)}")
