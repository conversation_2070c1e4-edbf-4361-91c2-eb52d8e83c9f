"""
分析服务

封装数据分析相关的业务逻辑
"""

import logging
from typing import Dict, Any
from datetime import datetime, timedelta
from .base import BaseService
from app.zentao_engine.exceptions import ZentaoEngineError

logger = logging.getLogger(__name__)


class AnalysisService(BaseService):
    """分析服务类"""
    
    async def analyze_bugs_by_dept_time(self, dept_id: int, start_time: str, end_time: str) -> Dict[str, Any]:
        """
        按部门和时间分析Bug统计
        
        Args:
            dept_id: 部门ID
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            Dict: Bug分析统计数据
        """
        try:
            self.logger.info(f"分析部门 {dept_id} 在 {start_time} - {end_time} 期间的Bug统计")
            
            # 获取部门信息
            dept_response = await self.zentao_service.get_department_info(dept_id)
            if not dept_response or dept_response.get("rsCode") != "********":
                raise ZentaoEngineError(f"获取部门信息失败: {dept_response}")
            
            dept_info = dept_response.get("data", {})
            
            # 获取部门相关的Bug（这里需要根据实际API调整）
            # 假设有按部门和时间查询Bug的API
            bugs_response = await self.zentao_service.get_bugs_by_dept_time(dept_id, start_time, end_time)
            
            if not bugs_response:
                raise ZentaoEngineError("获取Bug数据失败")
            
            bugs = bugs_response.get("data", [])
            
            # 统计分析
            total_bugs = len(bugs)
            resolved_bugs = 0
            active_bugs = 0
            severity_distribution = {}
            priority_distribution = {}
            
            for bug in bugs:
                status = bug.get("status", "")
                severity = bug.get("severity", "")
                priority = bug.get("pri", "")
                
                if status in ["resolved", "closed"]:
                    resolved_bugs += 1
                elif status == "active":
                    active_bugs += 1
                
                severity_distribution[severity] = severity_distribution.get(severity, 0) + 1
                priority_distribution[priority] = priority_distribution.get(priority, 0) + 1
            
            resolution_rate = (resolved_bugs / total_bugs * 100) if total_bugs > 0 else 0
            
            result = {
                "success": True,
                "analysis_period": {
                    "start_time": start_time,
                    "end_time": end_time
                },
                "department": {
                    "id": dept_id,
                    "name": dept_info.get("name", ""),
                    "path": dept_info.get("path", "")
                },
                "bug_statistics": {
                    "total_bugs": total_bugs,
                    "resolved_bugs": resolved_bugs,
                    "active_bugs": active_bugs,
                    "resolution_rate": round(resolution_rate, 2)
                },
                "severity_distribution": severity_distribution,
                "priority_distribution": priority_distribution
            }
            
            self.logger.info(f"部门Bug分析完成，总计 {total_bugs} 个Bug，解决率 {resolution_rate}%")
            return result
            
        except Exception as e:
            self.logger.error(f"部门Bug分析失败: {e}")
            raise ZentaoEngineError(f"部门Bug分析失败: {str(e)}")
    
    async def analyze_project_health(self, project_id: int) -> Dict[str, Any]:
        """
        分析项目健康度
        
        Args:
            project_id: 项目ID
            
        Returns:
            Dict: 项目健康度分析数据
        """
        try:
            self.logger.info(f"分析项目 {project_id} 健康度")
            
            # 获取项目基本信息
            project_response = await self.zentao_service.get_project_info(project_id)
            if not project_response or project_response.get("rsCode") != "********":
                raise ZentaoEngineError(f"获取项目信息失败: {project_response}")
            
            project_info = project_response.get("data", {})
            
            # 获取项目相关数据
            stories_response = await self.zentao_service.get_stories_by_project(project_id)
            tasks_response = await self.zentao_service.get_tasks_by_project(project_id)
            bugs_response = await self.zentao_service.get_bugs_by_project(project_id)
            
            stories = stories_response.get("data", []) if stories_response else []
            tasks = tasks_response.get("data", []) if tasks_response else []
            bugs = bugs_response.get("data", []) if bugs_response else []
            
            # 计算健康度指标
            story_completion_rate = self._calculate_completion_rate(stories, "status", ["closed", "done"])
            task_completion_rate = self._calculate_completion_rate(tasks, "status", ["done", "closed"])
            bug_resolution_rate = self._calculate_completion_rate(bugs, "status", ["resolved", "closed"])
            
            # 综合健康度评分（简单加权平均）
            health_score = (story_completion_rate * 0.4 + task_completion_rate * 0.4 + bug_resolution_rate * 0.2)
            
            # 健康度等级
            if health_score >= 90:
                health_level = "优秀"
            elif health_score >= 75:
                health_level = "良好"
            elif health_score >= 60:
                health_level = "一般"
            else:
                health_level = "需要关注"
            
            result = {
                "success": True,
                "project": {
                    "id": project_id,
                    "name": project_info.get("name", ""),
                    "status": project_info.get("status", "")
                },
                "health_metrics": {
                    "story_completion_rate": round(story_completion_rate, 2),
                    "task_completion_rate": round(task_completion_rate, 2),
                    "bug_resolution_rate": round(bug_resolution_rate, 2),
                    "overall_health_score": round(health_score, 2),
                    "health_level": health_level
                },
                "statistics": {
                    "total_stories": len(stories),
                    "total_tasks": len(tasks),
                    "total_bugs": len(bugs)
                }
            }
            
            self.logger.info(f"项目 {project_id} 健康度分析完成，综合评分: {health_score}")
            return result
            
        except Exception as e:
            self.logger.error(f"项目健康度分析失败: {e}")
            raise ZentaoEngineError(f"项目健康度分析失败: {str(e)}")
    
    def _calculate_completion_rate(self, items: list, status_field: str, completed_statuses: list) -> float:
        """
        计算完成率
        
        Args:
            items: 数据项列表
            status_field: 状态字段名
            completed_statuses: 已完成状态列表
            
        Returns:
            float: 完成率百分比
        """
        if not items:
            return 0.0
        
        completed_count = sum(1 for item in items if item.get(status_field) in completed_statuses)
        return (completed_count / len(items)) * 100
    
    async def project_summary_analysis(self, project_id: int) -> Dict[str, Any]:
        """
        项目整体统计分析
        
        Args:
            project_id: 项目ID
            
        Returns:
            Dict: 项目统计分析结果
        """
        try:
            self.logger.info(f"开始项目 {project_id} 整体统计分析")
            
            # 获取项目相关数据
            stories_response = await self.zentao_service.get_stories_by_project(project_id)
            tasks_response = await self.zentao_service.get_tasks_by_project(project_id)
            bugs_response = await self.zentao_service.get_bugs_by_project(project_id)
            
            stories_data = stories_response.get("body", []) if stories_response and stories_response.get("rsCode") == "********" else []
            tasks_data = tasks_response.get("body", []) if tasks_response and tasks_response.get("rsCode") == "********" else []
            bugs_data = bugs_response.get("body", []) if bugs_response and bugs_response.get("rsCode") == "********" else []
            
            # 统计分析
            analysis = self._analyze_project_data(stories_data, tasks_data, bugs_data)
            
            result = {
                "success": True,
                "message": f"项目 {project_id} 整体统计分析完成",
                "project_id": project_id,
                "analysis": analysis
            }
            
            self.logger.info(f"项目 {project_id} 整体统计分析完成")
            return result
            
        except Exception as e:
            self.logger.error(f"项目整体统计分析失败: {e}")
            raise ZentaoEngineError(f"项目整体统计分析失败: {str(e)}")
    
    async def personnel_workload_analysis(self, user_accounts: list = None, project_id: int = None, dept_id: int = None) -> Dict[str, Any]:
        """
        人员工作量统计和分析
        
        Args:
            user_accounts: 用户账号列表
            project_id: 项目ID
            dept_id: 部门ID
            
        Returns:
            Dict: 人员工作量分析结果
        """
        try:
            self.logger.info(f"开始人员工作量分析，用户: {user_accounts}, 项目ID: {project_id}, 部门ID: {dept_id}")
            
            # 获取分析范围内的数据
            stories_data = []
            tasks_data = []
            bugs_data = []
            
            if project_id:
                stories_response = await self.zentao_service.get_stories_by_project(project_id)
                if stories_response and stories_response.get("rsCode") == "********":
                    stories_data = stories_response.get("body", [])
                
                tasks_response = await self.zentao_service.get_tasks_by_project(project_id)
                if tasks_response and tasks_response.get("rsCode") == "********":
                    tasks_data = tasks_response.get("body", [])
                
                bugs_response = await self.zentao_service.get_bugs_by_project(project_id)
                if bugs_response and bugs_response.get("rsCode") == "********":
                    bugs_data = bugs_response.get("body", [])
            
            # 分析人员工作量
            personnel_stats = self._analyze_personnel_workload(stories_data, tasks_data, bugs_data, user_accounts)
            
            result = {
                "success": True,
                "message": f"人员工作量分析完成，共分析 {len(personnel_stats)} 个人员",
                "analysis_scope": {
                    "project_id": project_id,
                    "dept_id": dept_id,
                    "user_accounts": user_accounts
                },
                "personnel_analysis": personnel_stats
            }
            
            self.logger.info(f"人员工作量分析完成: {result['message']}")
            return result
            
        except Exception as e:
            self.logger.error(f"人员工作量分析失败: {e}")
            raise ZentaoEngineError(f"人员工作量分析失败: {str(e)}")
    
    async def story_task_relation_query(self, story_ids: list = None, project_id: int = None) -> Dict[str, Any]:
        """
        需求-任务关联查询
        
        Args:
            story_ids: 需求ID列表
            project_id: 项目ID
            
        Returns:
            Dict: 需求-任务关联查询结果
        """
        try:
            self.logger.info(f"开始需求-任务关联查询，需求ID: {story_ids}, 项目ID: {project_id}")
            
            # 获取需求数据
            stories_data = []
            if story_ids:
                for story_id in story_ids:
                    story_response = await self.zentao_service.get_story_by_id(story_id)
                    if story_response and story_response.get("rsCode") == "********":
                        story_data = story_response.get("body")
                        if story_data:
                            stories_data.append(story_data)
            elif project_id:
                stories_response = await self.zentao_service.get_stories_by_project(project_id)
                if stories_response and stories_response.get("rsCode") == "********":
                    stories_data = stories_response.get("body", [])
            else:
                raise ZentaoEngineError("必须提供story_ids或project_id参数")
            
            # 获取相关任务数据
            relations = []
            if project_id:
                tasks_response = await self.zentao_service.get_tasks_by_project(project_id)
                if tasks_response and tasks_response.get("rsCode") == "********":
                    tasks_data = tasks_response.get("body", [])
                    relations = self._analyze_story_task_relations(stories_data, tasks_data)
            
            result = {
                "success": True,
                "message": f"需求-任务关联查询完成，共 {len(stories_data)} 个需求",
                "total_stories": len(stories_data),
                "relations": relations
            }
            
            self.logger.info(f"需求-任务关联查询完成: {result['message']}")
            return result
            
        except Exception as e:
            self.logger.error(f"需求-任务关联查询失败: {e}")
            raise ZentaoEngineError(f"需求-任务关联查询失败: {str(e)}")
    
    async def bug_to_story_tracking(self, bug_ids: list = None, project_id: int = None) -> Dict[str, Any]:
        """
        Bug转需求追踪
        
        Args:
            bug_ids: Bug ID列表
            project_id: 项目ID
            
        Returns:
            Dict: Bug转需求追踪结果
        """
        try:
            self.logger.info(f"开始Bug转需求追踪，Bug ID: {bug_ids}, 项目ID: {project_id}")
            
            # 获取Bug数据
            bugs_data = []
            if bug_ids:
                for bug_id in bug_ids:
                    bug_response = await self.zentao_service.get_bug_detail(bug_id)
                    if bug_response and bug_response.get("rsCode") == "********":
                        bug_data = bug_response.get("body")
                        if bug_data:
                            bugs_data.append(bug_data)
            elif project_id:
                bugs_response = await self.zentao_service.get_bugs_by_project(project_id)
                if bugs_response and bugs_response.get("rsCode") == "********":
                    bugs_data = bugs_response.get("body", [])
            else:
                raise ZentaoEngineError("必须提供bug_ids或project_id参数")
            
            # 获取项目需求数据用于关联分析
            stories_data = []
            if project_id:
                stories_response = await self.zentao_service.get_stories_by_project(project_id)
                if stories_response and stories_response.get("rsCode") == "********":
                    stories_data = stories_response.get("body", [])
            
            # 分析Bug转需求的追踪
            tracking_results = self._analyze_bug_story_tracking(bugs_data, stories_data)
            
            result = {
                "success": True,
                "message": f"Bug转需求追踪完成，共 {len(bugs_data)} 个Bug",
                "total_bugs": len(bugs_data),
                "tracking_results": tracking_results
            }
            
            self.logger.info(f"Bug转需求追踪完成: {result['message']}")
            return result
            
        except Exception as e:
            self.logger.error(f"Bug转需求追踪失败: {e}")
            raise ZentaoEngineError(f"Bug转需求追踪失败: {str(e)}")
    
    def _analyze_project_data(self, stories_data: list, tasks_data: list, bugs_data: list) -> Dict[str, Any]:
        """分析项目数据"""
        return {
            "overview": {
                "total_stories": len(stories_data),
                "total_tasks": len(tasks_data),
                "total_bugs": len(bugs_data)
            },
            "story_analysis": self._analyze_stories(stories_data),
            "task_analysis": self._analyze_tasks(tasks_data),
            "bug_analysis": self._analyze_bugs(bugs_data)
        }
    
    def _analyze_stories(self, stories_data: list) -> Dict[str, Any]:
        """分析需求数据"""
        if not stories_data:
            return {"total_count": 0, "status_distribution": {}}
        
        status_count = {}
        for story in stories_data:
            status = story.get("status", "unknown")
            status_count[status] = status_count.get(status, 0) + 1
        
        return {
            "total_count": len(stories_data),
            "status_distribution": status_count
        }
    
    def _analyze_tasks(self, tasks_data: list) -> Dict[str, Any]:
        """分析任务数据"""
        if not tasks_data:
            return {"total_count": 0, "status_distribution": {}}
        
        status_count = {}
        for task in tasks_data:
            status = task.get("status", "unknown")
            status_count[status] = status_count.get(status, 0) + 1
        
        return {
            "total_count": len(tasks_data),
            "status_distribution": status_count
        }
    
    def _analyze_bugs(self, bugs_data: list) -> Dict[str, Any]:
        """分析Bug数据"""
        if not bugs_data:
            return {"total_count": 0, "status_distribution": {}}
        
        status_count = {}
        for bug in bugs_data:
            status = bug.get("status", "unknown")
            status_count[status] = status_count.get(status, 0) + 1
        
        return {
            "total_count": len(bugs_data),
            "status_distribution": status_count
        }
    
    def _analyze_personnel_workload(self, stories_data: list, tasks_data: list, bugs_data: list, user_accounts: list = None) -> list:
        """分析人员工作量"""
        personnel_stats = {}
        
        # 从需求中统计工作量
        for story in stories_data:
            assigned_to = story.get("assignedTo", "")
            if user_accounts and assigned_to not in user_accounts:
                continue
            
            if assigned_to not in personnel_stats:
                personnel_stats[assigned_to] = {"stories": 0, "tasks": 0, "bugs": 0}
            personnel_stats[assigned_to]["stories"] += 1
        
        # 从任务中统计工作量
        for task in tasks_data:
            assigned_to = task.get("assignedTo", "")
            if user_accounts and assigned_to not in user_accounts:
                continue
            
            if assigned_to not in personnel_stats:
                personnel_stats[assigned_to] = {"stories": 0, "tasks": 0, "bugs": 0}
            personnel_stats[assigned_to]["tasks"] += 1
        
        # 从Bug中统计工作量
        for bug in bugs_data:
            assigned_to = bug.get("assignedTo", "")
            if user_accounts and assigned_to not in user_accounts:
                continue
            
            if assigned_to not in personnel_stats:
                personnel_stats[assigned_to] = {"stories": 0, "tasks": 0, "bugs": 0}
            personnel_stats[assigned_to]["bugs"] += 1
        
        return [{"user": user, "workload": stats} for user, stats in personnel_stats.items()]
    
    def _analyze_story_task_relations(self, stories_data: list, tasks_data: list) -> list:
        """分析需求-任务关联"""
        relations = []
        for story in stories_data:
            story_id = story.get("id")
            related_tasks = [task for task in tasks_data if task.get("story") == story_id]
            relations.append({
                "story_id": story_id,
                "story_title": story.get("title", ""),
                "related_tasks": len(related_tasks),
                "tasks": related_tasks
            })
        return relations
    
    def _analyze_bug_story_tracking(self, bugs_data: list, stories_data: list) -> list:
        """分析Bug转需求追踪"""
        tracking_results = []
        for bug in bugs_data:
            bug_id = bug.get("id")
            bug_story_id = bug.get("story", 0)
            
            related_story = None
            if bug_story_id:
                related_story = next((story for story in stories_data if story.get("id") == bug_story_id), None)
            
            tracking_results.append({
                "bug_id": bug_id,
                "bug_title": bug.get("title", ""),
                "has_related_story": related_story is not None,
                "related_story": related_story
            })
        
        return tracking_results