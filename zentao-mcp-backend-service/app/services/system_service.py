"""
系统管理服务

封装系统相关的业务逻辑
"""

import logging
from typing import Dict, Any
from datetime import datetime
from .base import BaseService
from app.zentao_engine.exceptions import ZentaoEngineError

logger = logging.getLogger(__name__)


class SystemService(BaseService):
    """系统管理服务类"""
    
    async def health_check(self) -> Dict[str, Any]:
        """
        系统健康检查
        
        检查禅道连接状态和系统基本信息
        
        Returns:
            Dict: 系统健康状态信息
        """
        try:
            self.logger.info("执行系统健康检查")
            
            # 尝试获取部门列表来测试连接
            response = await self.zentao_service.get_all_departments()
            
            if response and response.get("rsCode") == "00000000":
                connection_status = "正常"
                connection_message = "禅道API连接正常"
            else:
                connection_status = "异常"
                connection_message = f"禅道API连接异常: {response}"
            
            # 获取性能统计（如果客户端支持）
            performance_stats = {}
            try:
                if hasattr(self.zentao_service, 'get_performance_stats'):
                    performance_stats = self.zentao_service.get_performance_stats()
            except Exception as e:
                self.logger.warning(f"获取性能统计失败: {e}")
                performance_stats = {"error": "性能统计不可用"}
            
            result = {
                "success": True,
                "timestamp": datetime.now().isoformat(),
                "system_status": {
                    "connection_status": connection_status,
                    "connection_message": connection_message,
                    "performance_stats": performance_stats
                }
            }
            
            self.logger.info(f"系统健康检查完成: {connection_status}")
            return result
            
        except Exception as e:
            self.logger.error(f"系统健康检查失败: {e}")
            return {
                "success": False,
                "timestamp": datetime.now().isoformat(),
                "error": f"系统健康检查失败: {str(e)}"
            }
    
    async def get_system_info(self) -> Dict[str, Any]:
        """
        获取系统信息
        
        Returns:
            Dict: 系统信息
        """
        try:
            self.logger.info("获取系统信息")
            
            # 执行健康检查
            health_result = await self.health_check()
            
            # 获取性能统计
            performance_stats = {}
            try:
                if hasattr(self.zentao_service, 'get_performance_stats'):
                    performance_stats = self.zentao_service.get_performance_stats()
            except Exception as e:
                self.logger.warning(f"获取性能统计失败: {e}")
                performance_stats = {"error": "性能统计不可用"}
            
            result = {
                "rsCode": "00000000",
                "msg": "系统信息获取成功",
                "body": {
                    "system_status": health_result.get("system_status", {}),
                    "performance_stats": performance_stats,
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            self.logger.info("系统信息获取完成")
            return result
            
        except Exception as e:
            self.logger.error(f"获取系统信息失败: {e}")
            return {
                "rsCode": "99999999",
                "msg": f"获取系统信息失败: {str(e)}",
                "body": None
            }
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """
        获取性能指标
        
        Returns:
            Dict: 性能指标数据
        """
        try:
            self.logger.info("获取性能指标")
            
            # 获取基础性能指标
            performance_stats = {}
            if hasattr(self.zentao_service, 'get_performance_stats'):
                performance_stats = self.zentao_service.get_performance_stats()
            
            # 添加系统级指标
            import psutil
            import time
            
            system_metrics = {
                "cpu_usage": psutil.cpu_percent(interval=1),
                "memory_usage": psutil.virtual_memory().percent,
                "disk_usage": psutil.disk_usage('/').percent,
                "uptime": time.time() - psutil.boot_time()
            }
            
            result = {
                "success": True,
                "message": "性能指标获取成功",
                "timestamp": datetime.now().isoformat(),
                "metrics": {
                    "zentao_client": performance_stats,
                    "system": system_metrics
                }
            }
            
            self.logger.info("性能指标获取完成")
            return result
            
        except ImportError:
            # 如果psutil不可用，只返回基础指标
            performance_stats = {}
            if hasattr(self.zentao_service, 'get_performance_stats'):
                performance_stats = self.zentao_service.get_performance_stats()
            
            result = {
                "success": True,
                "message": "性能指标获取成功（系统指标不可用）",
                "timestamp": datetime.now().isoformat(),
                "metrics": {
                    "zentao_client": performance_stats,
                    "system": {"note": "系统指标需要安装psutil库"}
                }
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"获取性能指标失败: {e}")
            return {
                "success": False,
                "message": f"获取性能指标失败: {str(e)}",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }