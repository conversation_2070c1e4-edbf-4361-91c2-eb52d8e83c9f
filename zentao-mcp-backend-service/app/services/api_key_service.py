"""
API Key管理服务
"""
import secrets
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime, timedelta, timezone

from app.models import APIKey, User
from app.services.audit_service import AuditService
from app.core.exceptions import APIKeyError, UserNotFoundError


class APIKeyService:
    """API Key管理服务类"""
    
    def __init__(self, db: Session, audit_service: Optional[AuditService] = None):
        self.db = db
        self.audit_service = audit_service or AuditService(db)
    
    def create_api_key(
        self,
        user_id: int,
        name: str,
        creator_id: Optional[int] = None
    ) -> APIKey:
        """
        创建API Key
        
        Args:
            user_id: 用户ID
            name: API Key名称
            creator_id: 创建者ID
            
        Returns:
            APIKey: 创建的API Key对象
            
        Raises:
            UserNotFoundError: 用户不存在
            APIKeyError: API Key创建失败
        """
        # 检查用户是否存在
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise UserNotFoundError(f"用户ID {user_id} 不存在")
        
        # 检查同名API Key是否已存在
        existing_key = self.db.query(APIKey).filter(
            APIKey.user_id == user_id,
            APIKey.name == name
        ).first()
        
        if existing_key:
            raise APIKeyError(f"API Key名称 '{name}' 已存在")
        
        # 生成API Key
        key_value = self._generate_api_key()
        
        # 创建API Key
        api_key = APIKey(
            user_id=user_id,
            name=name,
            key=key_value,
            key_value=key_value,
            key_hash=key_value,  # 临时使用相同值
            user_identifier=f"user_{user_id}",  # 保持向后兼容
        )
        
        self.db.add(api_key)
        self.db.commit()
        self.db.refresh(api_key)
        
        # 记录审计日志
        if creator_id:
            self.audit_service.log_action(
                user_id=creator_id,
                action="create_api_key",
                resource_type="api_key",
                resource_id=str(api_key.id),
                details={
                    "name": name,
                    "user_id": user_id,
                }
            )
        
        return api_key
    
    def get_api_key_by_id(self, key_id: int) -> Optional[APIKey]:
        """根据ID获取API Key"""
        return self.db.query(APIKey).filter(APIKey.id == key_id).first()
    
    def get_api_key_by_value(self, key_value: str) -> Optional[APIKey]:
        """根据Key值获取API Key"""
        return self.db.query(APIKey).filter(APIKey.key_value == key_value).first()
    
    def get_api_key_by_key(self, key_value: str) -> Optional[APIKey]:
        """根据key获取API Key (别名方法)"""
        return self.get_api_key_by_value(key_value)
    
    def get_user_api_keys(self, user_id: int) -> List[APIKey]:
        """获取用户的所有API Keys"""
        return self.db.query(APIKey).filter(APIKey.user_id == user_id).all()
    
    def deactivate_api_key(self, key_id: int, operator_id: Optional[int] = None) -> bool:
        """停用API Key"""
        api_key = self.get_api_key_by_id(key_id)
        if not api_key:
            raise APIKeyError(f"API Key ID {key_id} 不存在")
        
        api_key.is_active = False
        api_key.revoked_at = datetime.now(timezone.utc)
        self.db.commit()
        
        # 记录审计日志
        if operator_id:
            self.audit_service.log_action(
                user_id=operator_id,
                action="deactivate_api_key",
                resource_type="api_key",
                resource_id=str(key_id),
                details={"name": api_key.name}
            )
        
        return True
    
    def get_api_keys(
        self,
        user_id: Optional[int] = None,
        is_active: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[APIKey]:
        """
        获取API Key列表
        
        Args:
            user_id: 用户ID筛选
            is_active: 活跃状态筛选
            skip: 跳过的记录数
            limit: 返回的记录数限制
            
        Returns:
            List[APIKey]: API Key列表
        """
        query = self.db.query(APIKey)
        
        if user_id:
            query = query.filter(APIKey.user_id == user_id)
        
        if is_active is not None:
            query = query.filter(APIKey.is_active == is_active)
        
        return query.offset(skip).limit(limit).all()
    
    def update_api_key(
        self,
        key_id: int,
        updates: Dict[str, Any],
        operator_id: Optional[int] = None
    ) -> APIKey:
        """
        更新API Key
        
        Args:
            key_id: API Key ID
            updates: 更新的字段
            operator_id: 操作者ID
            
        Returns:
            APIKey: 更新后的API Key对象
            
        Raises:
            APIKeyError: API Key不存在
        """
        api_key = self.get_api_key_by_id(key_id)
        if not api_key:
            raise APIKeyError(f"API Key ID {key_id} 不存在")
        
        # 记录原始值用于审计
        original_values = {}
        
        # 更新字段
        for field, value in updates.items():
            if hasattr(api_key, field):
                original_values[field] = getattr(api_key, field)
                setattr(api_key, field, value)
        
        self.db.commit()
        self.db.refresh(api_key)
        
        # 记录审计日志
        if operator_id:
            self.audit_service.log_action(
                user_id=operator_id,
                action="update_api_key",
                resource_type="api_key",
                resource_id=str(key_id),
                details={
                    "updates": updates,
                    "original_values": original_values
                }
            )
        
        return api_key
    
    def delete_api_key(self, key_id: int, operator_id: Optional[int] = None) -> bool:
        """
        删除API Key
        
        Args:
            key_id: API Key ID
            operator_id: 操作者ID
            
        Returns:
            bool: 是否删除成功
            
        Raises:
            APIKeyError: API Key不存在
        """
        api_key = self.get_api_key_by_id(key_id)
        if not api_key:
            raise APIKeyError(f"API Key ID {key_id} 不存在")
        
        # 记录API Key信息用于审计
        key_info = {
            "name": api_key.name,
            "user_id": api_key.user_id,
            "permissions": api_key.permissions,
            "created_at": api_key.created_at.isoformat()
        }
        
        # 删除API Key
        self.db.delete(api_key)
        self.db.commit()
        
        # 记录审计日志
        if operator_id:
            self.audit_service.log_action(
                user_id=operator_id,
                action="delete_api_key",
                resource_type="api_key",
                resource_id=str(key_id),
                details=key_info
            )
        
        return True
    
    def regenerate_api_key(self, key_id: int, operator_id: Optional[int] = None) -> str:
        """
        重新生成API Key
        
        Args:
            key_id: API Key ID
            operator_id: 操作者ID
            
        Returns:
            str: 新的API Key值
            
        Raises:
            APIKeyError: API Key不存在
        """
        api_key = self.get_api_key_by_id(key_id)
        if not api_key:
            raise APIKeyError(f"API Key ID {key_id} 不存在")
        
        # 生成新的Key值
        old_key = api_key.key_value
        new_key = self._generate_api_key()
        
        api_key.key_value = new_key
        api_key.last_used = None  # 重置使用时间
        
        self.db.commit()
        
        # 记录审计日志
        if operator_id:
            self.audit_service.log_action(
                user_id=operator_id,
                action="regenerate_api_key",
                resource_type="api_key",
                resource_id=str(key_id),
                details={
                    "name": api_key.name,
                    "user_id": api_key.user_id
                }
            )
        
        return new_key
    
    def validate_api_key(self, key_value: str) -> bool:
        """
        验证API Key有效性
        
        Args:
            key_value: API Key值
            
        Returns:
            bool: API Key是否有效
        """
        api_key = self.get_api_key_by_value(key_value)
        
        if not api_key:
            return False
        
        if not api_key.is_active:
            return False
        
        if api_key.is_expired:
            return False
        
        # 更新最后使用时间
        api_key.last_used_at = datetime.now(timezone.utc)
        self.db.commit()
        
        return True
    
    def deactivate_expired_keys(self) -> int:
        """
        停用过期的API Key
        
        Returns:
            int: 停用的API Key数量
        """
        now = datetime.now(timezone.utc)
        
        expired_keys = self.db.query(APIKey).filter(
            APIKey.expires_at < now,
            APIKey.is_active == True
        ).all()
        
        for key in expired_keys:
            key.is_active = False
        
        self.db.commit()
        
        return len(expired_keys)
    
    def _generate_api_key(self) -> str:
        """生成安全的API Key"""
        # 生成32字节的随机数据，转换为64字符的十六进制字符串
        return secrets.token_hex(32)