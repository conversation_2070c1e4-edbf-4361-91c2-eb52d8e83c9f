"""
任务管理服务

封装任务相关的业务逻辑
"""

import logging
from typing import Dict, Any
from .base import BaseService
from app.zentao_engine.exceptions import ZentaoEngineError

logger = logging.getLogger(__name__)


class TaskService(BaseService):
    """任务管理服务类"""
    
    async def get_tasks_by_project(self, project_id: int) -> Dict[str, Any]:
        """
        根据项目ID获取任务列表
        
        Args:
            project_id: 项目ID
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API根据项目ID {project_id} 获取任务列表")
            
            response = await self.zentao_service.get_tasks_by_project(project_id)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功获取项目 {project_id} 任务列表，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"根据项目ID获取任务列表失败: {e}")
            raise ZentaoEngineError(f"根据项目ID获取任务列表失败: {str(e)}")
    
    async def get_task_info(self, task_id: int) -> Dict[str, Any]:
        """
        根据任务ID获取任务详情
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API根据任务ID {task_id} 获取任务详情")
            
            response = await self.zentao_service.get_task_info(task_id)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功获取任务 {task_id} 详情，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"根据任务ID获取任务详情失败: {e}")
            raise ZentaoEngineError(f"根据任务ID获取任务详情失败: {str(e)}")
    
    async def get_tasks_by_user(self, user_account: str) -> Dict[str, Any]:
        """
        根据用户账号获取任务列表
        
        Args:
            user_account: 用户账号
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API根据用户账号 {user_account} 获取任务列表")
            
            response = await self.zentao_service.get_tasks_by_user(user_account)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功获取用户 {user_account} 任务列表，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"根据用户账号获取任务列表失败: {e}")
            raise ZentaoEngineError(f"根据用户账号获取任务列表失败: {str(e)}")
    
    async def get_tasks_by_account(self, account: str, start_date: str, end_date: str, is_doing: bool = False) -> Dict[str, Any]:
        """
        根据域账号查询任务（带时间和状态过滤）
        
        Args:
            account: 域账号
            start_date: 开始日期
            end_date: 结束日期
            is_doing: 是否只查询进行中的任务
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API根据域账号 {account} 查询任务，时间: {start_date} 到 {end_date}, 进行中: {is_doing}")
            
            response = await self.zentao_service.get_tasks_by_account(account, start_date, end_date, is_doing)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功根据域账号查询任务，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"根据域账号查询任务失败: {e}")
            raise ZentaoEngineError(f"根据域账号查询任务失败: {str(e)}")
    
    async def get_tasks_by_department(self, dept_id: int) -> Dict[str, Any]:
        """
        根据部门ID获取任务列表
        
        Args:
            dept_id: 部门ID
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API根据部门ID {dept_id} 获取任务列表")
            
            response = await self.zentao_service.get_tasks_by_department(dept_id)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功获取部门 {dept_id} 任务列表，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"根据部门ID获取任务列表失败: {e}")
            raise ZentaoEngineError(f"根据部门ID获取任务列表失败: {str(e)}")
    
    async def get_tasks_by_dept(self, dept_id: int, start_date: str, end_date: str, is_doing: bool = False) -> Dict[str, Any]:
        """
        根据部门查询任务（带时间和状态过滤）
        
        Args:
            dept_id: 部门ID
            start_date: 开始日期
            end_date: 结束日期
            is_doing: 是否只查询进行中的任务
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API根据部门ID {dept_id} 查询任务，时间: {start_date} 到 {end_date}, 进行中: {is_doing}")
            
            response = await self.zentao_service.get_tasks_by_dept(dept_id, start_date, end_date, is_doing)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功根据部门查询任务，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"根据部门查询任务失败: {e}")
            raise ZentaoEngineError(f"根据部门查询任务失败: {str(e)}")