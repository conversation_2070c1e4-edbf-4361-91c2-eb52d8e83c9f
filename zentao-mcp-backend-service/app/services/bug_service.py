"""
Bug管理服务

封装Bug相关的业务逻辑
"""

import logging
from typing import Dict, Any
from .base import BaseService
from app.zentao_engine.exceptions import ZentaoEngineError

logger = logging.getLogger(__name__)


class BugService(BaseService):
    """Bug管理服务类"""
    
    async def get_bugs_by_time_range(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """
        根据时间段查询Bug列表
        
        直接调用禅道API: /apiData/getBugListByTimeRange
        保持原始数据结构
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD HH:MM:SS格式)
            end_date: 结束日期 (YYYY-MM-DD HH:MM:SS格式)
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API根据时间段查询Bug列表，时间: {start_date} 到 {end_date}")
            
            response = await self.zentao_service.get_bugs_by_time_range(start_date, end_date)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功根据时间段查询Bug列表，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"根据时间段查询Bug列表失败: {e}")
            raise ZentaoEngineError(f"根据时间段查询Bug列表失败: {str(e)}")
    
    async def get_bugs_by_time_and_dept(self, start_date: str, end_date: str, dept_id: int) -> Dict[str, Any]:
        """
        根据时间段和部门查询Bug
        
        直接调用禅道API: /apiData/getBugListByTimeRangeAndDeptId
        保持原始数据结构
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD HH:MM:SS格式)
            end_date: 结束日期 (YYYY-MM-DD HH:MM:SS格式)
            dept_id: 部门ID
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API根据时间段和部门查询Bug，时间: {start_date} 到 {end_date}, 部门ID: {dept_id}")
            
            response = await self.zentao_service.get_bugs_by_time_range_and_dept(start_date, end_date, dept_id)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功根据时间段和部门查询Bug，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"根据时间段和部门查询Bug失败: {e}")
            raise ZentaoEngineError(f"根据时间段和部门查询Bug失败: {str(e)}")
    
    async def get_bug_detail(self, bug_id: int) -> Dict[str, Any]:
        """
        根据Bug ID获取详情
        
        直接调用禅道API: /api/getBugDetail
        保持原始数据结构
        
        Args:
            bug_id: Bug ID
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API根据Bug ID {bug_id} 获取详情")
            
            response = await self.zentao_service.get_bug_detail(bug_id)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功获取Bug {bug_id} 详情，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"根据Bug ID获取详情失败: {e}")
            raise ZentaoEngineError(f"根据Bug ID获取详情失败: {str(e)}")
    
    async def get_personal_bugs(self, account: str, status: str, start_date: str, end_date: str) -> Dict[str, Any]:
        """
        查询个人Bug
        
        直接调用禅道API: /api/getPersonalBugs
        保持原始数据结构
        
        Args:
            account: 域账号
            status: Bug状态 (active/resolved/released/closed)
            start_date: 开始日期 (YYYY-MM-DD HH:MM:SS格式)
            end_date: 结束日期 (YYYY-MM-DD HH:MM:SS格式)
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API查询个人Bug，账号: {account}, 状态: {status}, 时间: {start_date} 到 {end_date}")
            
            response = await self.zentao_service.get_personal_bugs(account, status, start_date, end_date)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功查询个人Bug，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"查询个人Bug失败: {e}")
            raise ZentaoEngineError(f"查询个人Bug失败: {str(e)}")
    
    async def analyze_bugs_by_dept_and_time(self, start_date: str, end_date: str, dept_id: int) -> Dict[str, Any]:
        """
        按部门和时间段统计Bug分析
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            dept_id: 部门ID
            
        Returns:
            Dict: Bug分析统计结果
        """
        try:
            self.logger.info(f"按部门和时间段统计Bug: 部门 {dept_id}, 时间 {start_date} 到 {end_date}")
            
            # 获取部门时间范围内的Bug列表
            response = await self.zentao_service.get_bugs_by_time_range_and_dept(start_date, end_date, dept_id)
            
            if not response or response.get("rsCode") != "00000000":
                self.logger.warning(f"获取部门时间范围Bug列表失败: {response}")
                return {
                    "success": False,
                    "message": "获取部门时间范围Bug列表失败",
                    "start_date": start_date,
                    "end_date": end_date,
                    "dept_id": dept_id,
                    "analysis": {}
                }
            
            bugs_data = response.get("body", [])
            
            if not isinstance(bugs_data, list):
                self.logger.warning(f"Bug数据格式错误: {bugs_data}")
                return {
                    "success": False,
                    "message": "Bug数据格式错误",
                    "start_date": start_date,
                    "end_date": end_date,
                    "dept_id": dept_id,
                    "analysis": {}
                }
            
            # 统计分析
            total_bugs = len(bugs_data)
            status_stats = {}
            severity_stats = {}
            project_stats = {}
            assignee_stats = {}
            daily_stats = {}
            
            for bug_data in bugs_data:
                try:
                    # 状态统计
                    status = bug_data.get("status", "unknown")
                    status_stats[status] = status_stats.get(status, 0) + 1
                    
                    # 严重程度统计
                    severity = bug_data.get("severity", 2)
                    severity_name = self._get_severity_name(severity)
                    severity_stats[severity_name] = severity_stats.get(severity_name, 0) + 1
                    
                    # 项目统计
                    project_name = bug_data.get("projectname", "未知项目")
                    project_stats[project_name] = project_stats.get(project_name, 0) + 1
                    
                    # 指派人统计
                    assignee = bug_data.get("assignedTo", "未指派")
                    assignee_stats[assignee] = assignee_stats.get(assignee, 0) + 1
                    
                    # 按日统计
                    opened_date = bug_data.get("openedDate", "")
                    if opened_date:
                        try:
                            if "T" in opened_date:
                                date_part = opened_date.split("T")[0]
                            else:
                                date_part = opened_date.split(" ")[0]
                            daily_stats[date_part] = daily_stats.get(date_part, 0) + 1
                        except Exception:
                            pass
                    
                except Exception as e:
                    self.logger.warning(f"Bug数据格式化失败: {bug_data}, 错误: {e}")
                    continue
            
            # 计算统计指标
            resolved_count = status_stats.get("resolved", 0) + status_stats.get("closed", 0)
            resolution_rate = (resolved_count / total_bugs * 100) if total_bugs > 0 else 0
            
            critical_count = severity_stats.get("严重", 0) + severity_stats.get("冒烟", 0)
            critical_rate = (critical_count / total_bugs * 100) if total_bugs > 0 else 0
            
            result = {
                "success": True,
                "message": f"部门Bug统计分析完成，共 {total_bugs} 个Bug",
                "start_date": start_date,
                "end_date": end_date,
                "dept_id": dept_id,
                "analysis": {
                    "summary": {
                        "total_bugs": total_bugs,
                        "resolved_bugs": resolved_count,
                        "resolution_rate": round(resolution_rate, 2),
                        "critical_bugs": critical_count,
                        "critical_rate": round(critical_rate, 2)
                    },
                    "status_distribution": {
                        status: {
                            "count": count,
                            "percentage": round((count / total_bugs * 100), 2) if total_bugs > 0 else 0
                        }
                        for status, count in status_stats.items()
                    },
                    "severity_distribution": {
                        severity: {
                            "count": count,
                            "percentage": round((count / total_bugs * 100), 2) if total_bugs > 0 else 0
                        }
                        for severity, count in severity_stats.items()
                    },
                    "project_distribution": dict(sorted(project_stats.items(), key=lambda x: x[1], reverse=True)[:10]),
                    "assignee_distribution": dict(sorted(assignee_stats.items(), key=lambda x: x[1], reverse=True)[:10]),
                    "daily_trend": dict(sorted(daily_stats.items()))
                }
            }
            
            self.logger.info(f"部门Bug统计分析完成: {result['message']}")
            return result
            
        except Exception as e:
            self.logger.error(f"按部门和时间段统计Bug失败: {e}")
            raise ZentaoEngineError(f"按部门和时间段统计Bug失败: {str(e)}")
    
    async def filter_bugs_by_criteria(self, start_date: str = None, end_date: str = None, 
                                    status_filter: list = None, severity_filter: list = None,
                                    project_filter: list = None, assignee_filter: list = None) -> Dict[str, Any]:
        """
        根据多种条件过滤Bug
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            status_filter: 状态过滤列表
            severity_filter: 严重程度过滤列表
            project_filter: 项目名称过滤列表
            assignee_filter: 指派人过滤列表
            
        Returns:
            Dict: 过滤后Bug列表
        """
        try:
            self.logger.info(f"根据条件过滤Bug: 状态={status_filter}, 严重程度={severity_filter}")
            
            # 获取Bug数据
            bugs_data = []
            if start_date and end_date:
                response = await self.zentao_service.get_bugs_by_time_range(start_date, end_date)
                if response and response.get("rsCode") == "00000000":
                    bugs_data = response.get("body", [])
            else:
                return {
                    "success": False,
                    "message": "必须提供start_date和end_date",
                    "total_input": 0,
                    "total_filtered": 0,
                    "filtered_bugs": []
                }
            
            if not bugs_data:
                return {
                    "success": True,
                    "message": "没有Bug数据需要过滤",
                    "total_input": 0,
                    "total_filtered": 0,
                    "filtered_bugs": []
                }
            
            # 应用过滤条件
            filtered_bugs = []
            
            for bug in bugs_data:
                # 状态过滤
                if status_filter and bug.get("status") not in status_filter:
                    continue
                
                # 严重程度过滤
                if severity_filter and bug.get("severity") not in severity_filter:
                    continue
                
                # 项目过滤
                if project_filter:
                    project_name = bug.get("projectname", "")
                    if not any(project in project_name for project in project_filter):
                        continue
                
                # 指派人过滤
                if assignee_filter and bug.get("assignedTo") not in assignee_filter:
                    continue
                
                filtered_bugs.append(bug)
            
            # 生成过滤统计
            filter_summary = {
                "applied_filters": {
                    "status": status_filter,
                    "severity": severity_filter,
                    "project": project_filter,
                    "assignee": assignee_filter
                },
                "filter_rate": round((len(filtered_bugs) / len(bugs_data) * 100), 2) if bugs_data else 0
            }
            
            result = {
                "success": True,
                "message": f"过滤完成，从 {len(bugs_data)} 个Bug中筛选出 {len(filtered_bugs)} 个符合条件的Bug",
                "total_input": len(bugs_data),
                "total_filtered": len(filtered_bugs),
                "filter_summary": filter_summary,
                "filtered_bugs": filtered_bugs
            }
            
            self.logger.info(f"Bug过滤完成: {result['message']}")
            return result
            
        except Exception as e:
            self.logger.error(f"根据条件过滤Bug失败: {e}")
            raise ZentaoEngineError(f"根据条件过滤Bug失败: {str(e)}")
    
    def _get_severity_name(self, severity: int) -> str:
        """
        获取严重程度名称
        
        Args:
            severity: 严重程度数值
            
        Returns:
            str: 严重程度名称
        """
        severity_names = {
            0: "冒烟",
            1: "严重", 
            2: "一般",
            3: "次要",
            4: "低级"
        }
        return severity_names.get(severity, "未知")