"""
项目管理服务

封装项目相关的业务逻辑
"""

import logging
from typing import Dict, Any
from .base import BaseService
from app.zentao_engine.exceptions import ZentaoEngineError

logger = logging.getLogger(__name__)


class ProjectService(BaseService):
    """项目管理服务类"""
    
    async def get_all_projects(self) -> Dict[str, Any]:
        """
        获取所有项目列表
        
        直接调用禅道API: /apiData/getAllProject
        保持原始数据结构
        
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info("调用禅道API获取所有项目列表")
            
            response = await self.zentao_service.get_all_projects()
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功获取项目列表，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"获取所有项目列表失败: {e}")
            raise ZentaoEngineError(f"获取所有项目列表失败: {str(e)}")
    
    async def get_project_tasks(self, project_id: int) -> Dict[str, Any]:
        """
        根据项目ID查询任务列表
        
        直接调用禅道API: /api/getTaskByProject
        保持原始数据结构
        
        Args:
            project_id: 项目ID
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API根据项目ID {project_id} 查询任务列表")
            
            response = await self.zentao_service.get_tasks_by_project(project_id)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功获取项目 {project_id} 任务列表，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"根据项目ID查询任务列表失败: {e}")
            raise ZentaoEngineError(f"根据项目ID查询任务列表失败: {str(e)}")
    
    async def get_project_stories(self, project_id: int) -> Dict[str, Any]:
        """
        根据项目ID查询需求列表
        
        直接调用禅道API: /api/getStoryByProjectid
        保持原始数据结构
        
        Args:
            project_id: 项目ID
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API根据项目ID {project_id} 查询需求列表")
            
            response = await self.zentao_service.get_stories_by_project(project_id)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功获取项目 {project_id} 需求列表，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"根据项目ID查询需求列表失败: {e}")
            raise ZentaoEngineError(f"根据项目ID查询需求列表失败: {str(e)}")
    
    async def get_project_bugs(self, project_id: int) -> Dict[str, Any]:
        """
        根据项目ID查询Bug列表
        
        直接调用禅道API: /api/getBugByProject
        保持原始数据结构
        
        Args:
            project_id: 项目ID
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API根据项目ID {project_id} 查询Bug列表")
            
            response = await self.zentao_service.get_bugs_by_project(project_id)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功获取项目 {project_id} Bug列表，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"根据项目ID查询Bug列表失败: {e}")
            raise ZentaoEngineError(f"根据项目ID查询Bug列表失败: {str(e)}")