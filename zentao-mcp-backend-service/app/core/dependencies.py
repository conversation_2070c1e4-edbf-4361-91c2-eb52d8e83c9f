"""
依赖注入配置

提供服务层的依赖注入支持
"""

import logging
from typing import AsyncGenerator
from fastapi import Depends
from app.zentao_engine.client import get_zentao_service
from app.services import (
    ProjectService,
    BugService, 
    DepartmentService,
    StoryService,
    TaskService,
    UserService,
    SystemService,
    AnalysisService
)
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.admin import User, UserType
from app.services.session_service import SessionService, SessionExpiredError, InvalidTokenError
from app.services.user_management_service import UserManagementService
from app.services.audit_service import AuditService
from app.core.errors import PermissionDeniedError

logger = logging.getLogger(__name__)

security = HTTPBearer()


async def get_project_service() -> AsyncGenerator[ProjectService, None]:
    """获取项目服务实例"""
    zentao_service = await get_zentao_service()
    async with ProjectService(zentao_service) as service:
        yield service


async def get_bug_service() -> AsyncGenerator[BugService, None]:
    """获取Bug服务实例"""
    zentao_service = await get_zentao_service()
    async with BugService(zentao_service) as service:
        yield service


async def get_department_service() -> AsyncGenerator[DepartmentService, None]:
    """获取部门服务实例"""
    zentao_service = await get_zentao_service()
    async with DepartmentService(zentao_service) as service:
        yield service


async def get_user_service() -> AsyncGenerator[UserService, None]:
    """获取用户服务实例"""
    zentao_service = await get_zentao_service()
    async with UserService(zentao_service) as service:
        yield service


async def get_system_service() -> AsyncGenerator[SystemService, None]:
    """获取系统服务实例"""
    zentao_service = await get_zentao_service()
    async with SystemService(zentao_service) as service:
        yield service


async def get_story_service() -> AsyncGenerator[StoryService, None]:
    """获取需求服务实例"""
    zentao_service = await get_zentao_service()
    async with StoryService(zentao_service) as service:
        yield service


async def get_task_service() -> AsyncGenerator[TaskService, None]:
    """获取任务服务实例"""
    zentao_service = await get_zentao_service()
    async with TaskService(zentao_service) as service:
        yield service


async def get_analysis_service() -> AsyncGenerator[AnalysisService, None]:
    """获取分析服务实例"""
    zentao_service = await get_zentao_service()
    async with AnalysisService(zentao_service) as service:
        yield service


def get_user_management_service(db: Session = Depends(get_db)) -> UserManagementService:
    """获取用户管理服务实例"""
    return UserManagementService(db)


def get_user_session_service(db: Session = Depends(get_db)) -> SessionService:
    """获取用户会话服务实例"""
    return SessionService(db)


def get_audit_service(db: Session = Depends(get_db)) -> AuditService:
    """获取审计服务实例"""
    return AuditService(db)


async def get_current_admin_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """获取当前管理员用户，如果不是管理员则抛出异常"""
    session_service = SessionService(db)
    try:
        session = session_service.validate_session(credentials.credentials)
        user = session.user
        if not user or user.user_type != UserType.ADMIN:
            raise PermissionDeniedError("需要管理员权限")
        return user
    except (SessionExpiredError, InvalidTokenError) as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
