"""
API Key认证和安全相关功能
"""

import hashlib
from fastapi import HTTPException, Depends, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.crud.api_key import get_api_key_by_hash, hash_api_key
from app.core.config import settings
from typing import Optional

security = HTTPBearer()


def verify_api_key(credentials: HTTPAuthorizationCredentials = Depends(security), 
                  db: Session = Depends(get_db)) -> str:
    """
    验证API Key的FastAPI依赖项
    
    Args:
        credentials: HTTP Bearer认证凭据
        db: 数据库会话
        
    Returns:
        str: 用户标识符
        
    Raises:
        HTTPException: 认证失败时抛出401或403错误
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing API Key"
        )
    
    api_key = credentials.credentials
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API Key format"
        )
    
    # 计算API Key的哈希值
    key_hash = hash_api_key(api_key)
    
    # 从数据库查找API Key
    db_api_key = get_api_key_by_hash(db, key_hash)
    
    if not db_api_key:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid API Key"
        )
    
    if not db_api_key.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="API Key has been revoked"
        )
    
    return db_api_key.user_identifier
