#!/usr/bin/env python3

import sys
sys.path.append('.')

from app.core.database import get_db
from app.services.user_management_service import UserManagementService
from app.services.session_service import SessionService
from app.services.audit_service import AuditService

def test_login_logic():
    """测试登录逻辑"""
    
    print("🔍 测试登录逻辑")
    print("=" * 50)
    
    try:
        # 获取数据库连接
        db = next(get_db())
        print("✅ 数据库连接成功")
        
        # 初始化服务
        user_service = UserManagementService(db)
        session_service = SessionService(db)
        audit_service = AuditService(db)
        print("✅ 服务初始化成功")
        
        # 测试查找用户
        username = "admin"
        user = user_service.get_user_by_username(username)
        
        if not user:
            print(f"❌ 用户 {username} 不存在")
            return
            
        print(f"✅ 找到用户: {user.username}")
        print(f"   用户ID: {user.id}")
        print(f"   用户类型: {user.user_type}")
        print(f"   是否激活: {user.is_active}")
        print(f"   是否锁定: {user.is_locked}")
        
        # 测试密码验证
        password = "admin123"
        is_valid = user_service._verify_user_password(user, password)
        print(f"✅ 密码验证: {'通过' if is_valid else '失败'}")
        
        if is_valid:
            # 测试创建会话
            session = session_service.create_session(
                user=user,
                ip_address="127.0.0.1",
                user_agent="Test-Agent",
                remember_me=False
            )
            print(f"✅ 会话创建成功")
            print(f"   会话ID: {session.id}")
            print(f"   Token: {session.session_token[:50]}...")
            print(f"   过期时间: {session.expires_at}")
            
        # 测试审计日志
        audit_service.log_action(
            user_id=user.id,
            action="test_login",
            resource_type="auth",
            details={"test": True},
            ip_address="127.0.0.1",
            user_agent="Test-Agent"
        )
        print("✅ 审计日志记录成功")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_login_logic()