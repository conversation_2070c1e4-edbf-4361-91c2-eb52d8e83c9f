#!/usr/bin/env python3
"""
测试日志中间件修复效果
"""
import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

import httpx
from app.core.logging import setup_logging, api_logger

async def test_login_logging():
    """测试登录API的日志记录"""
    
    # 初始化日志系统
    setup_logging()
    
    print("🧪 测试日志中间件修复效果")
    print("=" * 50)
    
    # 测试数据
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    base_url = "http://localhost:8000"
    
    try:
        async with httpx.AsyncClient() as client:
            print(f"📡 发送登录请求到: {base_url}/api/v1/admin/auth/login")
            
            response = await client.post(
                f"{base_url}/api/v1/admin/auth/login",
                json=login_data,
                headers={"Content-Type": "application/json"}
            )
            
            print(f"📊 响应状态码: {response.status_code}")
            print(f"📋 响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                response_json = response.json()
                print(f"✅ 响应内容: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
                
                # 检查响应是否包含预期字段
                expected_fields = ["access_token", "token_type", "user_id", "username"]
                missing_fields = [field for field in expected_fields if field not in response_json]
                
                if not missing_fields:
                    print("✅ 响应包含所有预期字段")
                else:
                    print(f"❌ 缺少字段: {missing_fields}")
                    
            else:
                print(f"❌ 请求失败: {response.text}")
                
    except httpx.ConnectError:
        print("❌ 无法连接到服务器，请确保服务器正在运行")
        print("💡 运行命令启动服务器: python -m uvicorn main:app --reload")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        return False
    
    print("\n" + "=" * 50)
    print("📝 请检查服务器日志，确认以下内容:")
    print("1. 日志中包含 request_id")
    print("2. 日志中 response_data 不再是 null")
    print("3. 敏感信息（如password）被正确过滤")
    print("4. 响应状态码和处理时间被正确记录")
    
    return True

async def test_multiple_requests():
    """测试多个请求的日志记录"""
    print("\n🔄 测试多个请求的日志记录")
    print("-" * 30)
    
    base_url = "http://localhost:8000"
    
    # 测试不同的API端点
    test_cases = [
        {
            "name": "登录API",
            "method": "POST",
            "url": f"{base_url}/api/v1/admin/auth/login",
            "data": {"username": "admin", "password": "admin123"}
        },
        {
            "name": "健康检查",
            "method": "GET", 
            "url": f"{base_url}/health",
            "data": None
        }
    ]
    
    try:
        async with httpx.AsyncClient() as client:
            for i, test_case in enumerate(test_cases, 1):
                print(f"📡 测试 {i}: {test_case['name']}")
                
                if test_case['method'] == 'POST':
                    response = await client.post(
                        test_case['url'],
                        json=test_case['data'],
                        headers={"Content-Type": "application/json"}
                    )
                else:
                    response = await client.get(test_case['url'])
                
                print(f"   状态码: {response.status_code}")
                print(f"   Request-ID: {response.headers.get('X-Request-ID', 'N/A')}")
                
                await asyncio.sleep(0.5)  # 避免请求过快
                
    except Exception as e:
        print(f"❌ 多请求测试失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 启动日志中间件测试")
    
    try:
        # 运行异步测试
        asyncio.run(test_login_logging())
        asyncio.run(test_multiple_requests())
        
        print("\n✅ 测试完成！")
        print("💡 提示: 查看服务器控制台输出，确认日志格式是否正确")
        
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()