#!/usr/bin/env python3
"""
创建管理员用户脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal, engine, Base
from app.models.admin import User, UserType
from app.services.password_service import PasswordService

def create_admin_user():
    """创建默认管理员用户"""
    print("🔧 创建管理员用户...")
    
    # 创建数据库表
    Base.metadata.create_all(bind=engine)
    
    db: Session = SessionLocal()
    try:
        # 检查是否已存在管理员
        existing_admin = db.query(User).filter(User.username == "admin").first()
        if existing_admin:
            print("✅ 管理员用户已存在")
            print(f"   用户名: {existing_admin.username}")
            print(f"   邮箱: {existing_admin.email}")
            print(f"   用户类型: {existing_admin.user_type.value}")
            return
        
        # 创建新管理员
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            password_hash=PasswordService.hash_password("admin123"),
            user_type=UserType.ADMIN,
            is_active=True
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print("✅ 管理员用户创建成功！")
        print(f"   用户名: admin")
        print(f"   密码: admin123")
        print(f"   邮箱: <EMAIL>")
        print(f"   用户类型: {UserType.ADMIN.value}")
        
    except Exception as e:
        print(f"❌ 创建管理员用户失败: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_admin_user()