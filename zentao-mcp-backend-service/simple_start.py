#!/usr/bin/env python3
"""
简化的服务启动脚本
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.getcwd())

print("🔍 开始启动服务...")

try:
    print("📦 导入FastAPI...")
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.responses import JSONResponse
    
    print("✅ FastAPI导入成功")
    
    # 创建简化的应用
    app = FastAPI(
        title="Zentao MCP Backend Service",
        description="Cloud-based API service for Zentao MCP operations",
        version="0.1.0",
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint"""
        return JSONResponse(
            status_code=200,
            content={
                "status": "healthy",
                "service": "zentao-mcp-backend-service",
                "version": "0.1.0"
            }
        )
    
    @app.get("/")
    async def root():
        """Root endpoint"""
        return JSONResponse(
            content={
                "message": "Zentao MCP Backend Service",
                "version": "0.1.0",
                "docs": "/docs"
            }
        )
    
    print("✅ 基础应用创建成功")
    
    # 尝试导入核心模块
    try:
        print("📦 导入核心模块...")
        from app.core.middleware import RequestLoggingMiddleware
        app.add_middleware(RequestLoggingMiddleware)
        print("✅ 中间件导入成功")
    except ImportError as e:
        print(f"⚠️ 中间件导入失败，跳过: {e}")
    
    # 尝试导入数据库模块
    try:
        print("📦 导入数据库模块...")
        from app.core.database import Base, engine
        print("✅ 数据库模块导入成功")
    except ImportError as e:
        print(f"⚠️ 数据库模块导入失败，跳过: {e}")
    
    # 尝试导入API端点
    api_modules = [
        ("admin", "app.api.v1.endpoints.admin"),
        ("projects", "app.api.v1.endpoints.projects"),
        ("departments", "app.api.v1.endpoints.departments"),
        ("bugs", "app.api.v1.endpoints.bugs"),
        ("stories", "app.api.v1.endpoints.stories"),
        ("tasks", "app.api.v1.endpoints.tasks"),
        ("users", "app.api.v1.endpoints.users"),
        ("analysis", "app.api.v1.endpoints.analysis"),
        ("system", "app.api.v1.endpoints.system"),
    ]
    
    for name, module_path in api_modules:
        try:
            print(f"📦 导入{name}端点...")
            module = __import__(module_path, fromlist=[name])
            if hasattr(module, 'router'):
                app.include_router(
                    module.router, 
                    prefix=f"/api/v1/{name}", 
                    tags=[name.title()]
                )
                print(f"✅ {name}端点导入成功")
            else:
                print(f"⚠️ {name}端点没有router属性")
        except ImportError as e:
            print(f"⚠️ {name}端点导入失败，跳过: {e}")
    
    print("🚀 启动服务...")
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=False)
    
except Exception as e:
    print(f"❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)