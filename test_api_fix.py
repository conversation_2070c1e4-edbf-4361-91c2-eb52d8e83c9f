#!/usr/bin/env python3
"""
测试API修复的脚本
"""
import requests
import json
import sys

def test_api_endpoints():
    """测试API端点是否正常工作"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试API端点修复...")
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查端点正常")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("💡 请先启动后端服务: cd zentao-mcp-backend-service && uv run python main.py")
        return False
    
    # 测试API文档
    try:
        response = requests.get(f"{base_url}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ API文档端点正常")
        else:
            print(f"❌ API文档访问失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ API文档访问异常: {e}")
    
    # 测试管理员API路径
    admin_endpoints = [
        "/api/v1/admin/auth/me",
        "/api/v1/admin/users/",
        "/api/v1/admin/api-keys/"
    ]
    
    for endpoint in admin_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            # 401是预期的，因为没有认证
            if response.status_code in [401, 422]:
                print(f"✅ {endpoint} 端点存在（需要认证）")
            else:
                print(f"⚠️  {endpoint} 返回状态码: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ {endpoint} 访问异常: {e}")
    
    return True

def test_model_imports():
    """测试数据模型导入"""
    print("\n🔍 测试数据模型修复...")
    
    try:
        # 测试新的User模型
        from zentao_mcp_backend_service.app.models import User, UserType
        print("✅ User模型导入成功")
        
        # 测试向后兼容的AdminUser
        from zentao_mcp_backend_service.app.models import AdminUser
        print("✅ AdminUser向后兼容导入成功")
        
        # 验证它们是同一个类
        if User is AdminUser:
            print("✅ User和AdminUser是同一个类（向后兼容）")
        else:
            print("❌ User和AdminUser不是同一个类")
            return False
            
        return True
        
    except ImportError as e:
        print(f"❌ 模型导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试API修复...")
    
    # 测试数据模型
    model_ok = test_model_imports()
    
    # 测试API端点
    api_ok = test_api_endpoints()
    
    print("\n📊 测试结果:")
    print(f"数据模型: {'✅ 通过' if model_ok else '❌ 失败'}")
    print(f"API端点: {'✅ 通过' if api_ok else '❌ 失败'}")
    
    if model_ok and api_ok:
        print("\n🎉 所有测试通过！前后端API路径已修复")
        return 0
    else:
        print("\n⚠️  部分测试失败，请检查相关配置")
        return 1

if __name__ == "__main__":
    sys.exit(main())