#!/usr/bin/env python3
"""
验证API修复的简单脚本
"""
import sys
import os

def verify_frontend_config():
    """验证前端配置修复"""
    print("🔍 验证前端API配置...")
    
    api_file = "zentao-token-web/src/services/api.ts"
    if not os.path.exists(api_file):
        print(f"❌ 文件不存在: {api_file}")
        return False
    
    with open(api_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    if '/api/v1/admin' in content:
        print("✅ 前端API baseURL已更新为 /api/v1/admin")
        return True
    else:
        print("❌ 前端API baseURL未正确更新")
        return False

def verify_backend_models():
    """验证后端模型修复"""
    print("\n🔍 验证后端数据模型...")
    
    # 检查admin.py文件
    admin_file = "zentao-mcp-backend-service/app/models/admin.py"
    if not os.path.exists(admin_file):
        print(f"❌ 文件不存在: {admin_file}")
        return False
    
    with open(admin_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    if 'class User(Base):' in content:
        print("✅ AdminUser类已重命名为User")
    else:
        print("❌ AdminUser类未正确重命名")
        return False
    
    # 检查__init__.py文件
    init_file = "zentao-mcp-backend-service/app/models/__init__.py"
    if not os.path.exists(init_file):
        print(f"❌ 文件不存在: {init_file}")
        return False
    
    with open(init_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    if 'AdminUser = User' in content and '"User"' in content:
        print("✅ 向后兼容性已配置")
        return True
    else:
        print("❌ 向后兼容性配置不正确")
        return False

def verify_model_relationships():
    """验证模型关系修复"""
    print("\n🔍 验证模型关系...")
    
    files_to_check = [
        "zentao-mcp-backend-service/app/models/api_key.py",
        "zentao-mcp-backend-service/app/models/audit_log.py", 
        "zentao-mcp-backend-service/app/models/user_session.py"
    ]
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return False
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'relationship("User"' in content:
            print(f"✅ {os.path.basename(file_path)} 关系已更新")
        else:
            print(f"❌ {os.path.basename(file_path)} 关系未正确更新")
            return False
    
    return True

def main():
    """主函数"""
    print("🚀 验证API修复...")
    
    frontend_ok = verify_frontend_config()
    backend_ok = verify_backend_models()
    relations_ok = verify_model_relationships()
    
    print("\n📊 验证结果:")
    print(f"前端配置: {'✅ 通过' if frontend_ok else '❌ 失败'}")
    print(f"后端模型: {'✅ 通过' if backend_ok else '❌ 失败'}")
    print(f"模型关系: {'✅ 通过' if relations_ok else '❌ 失败'}")
    
    if frontend_ok and backend_ok and relations_ok:
        print("\n🎉 所有修复验证通过！")
        print("\n📝 修复内容:")
        print("1. ✅ 前端API baseURL从 /api/v1 改为 /api/v1/admin")
        print("2. ✅ AdminUser类重命名为User，保持向后兼容")
        print("3. ✅ 所有模型关系引用已更新为User")
        print("4. ✅ 数据库表名保持为 'users'")
        
        print("\n🚀 下一步操作:")
        print("1. 启动后端服务: cd zentao-mcp-backend-service && uv run python main.py")
        print("2. 启动前端服务: cd zentao-token-web && bun run dev")
        print("3. 访问 http://localhost:5173 测试Web界面")
        
        return 0
    else:
        print("\n⚠️  部分验证失败，请检查修复内容")
        return 1

if __name__ == "__main__":
    sys.exit(main())