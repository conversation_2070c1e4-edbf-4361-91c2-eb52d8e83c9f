#!/usr/bin/env node

/**
 * 登录问题修复测试脚本
 * 用于验证登录流程的各个环节
 */

const axios = require('axios');

// 测试配置
const TEST_CONFIG = {
  baseURL: 'http://localhost:8000',
  credentials: {
    username: 'admin',
    password: 'admin123'
  }
};

async function testLoginAPI() {
  console.log('🧪 开始测试登录API...\n');
  
  try {
    // 1. 测试登录API
    console.log('1️⃣ 测试登录API调用...');
    const response = await axios.post(
      `${TEST_CONFIG.baseURL}/api/v1/admin/auth/login`,
      TEST_CONFIG.credentials,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('✅ 登录API响应成功');
    console.log('📋 响应数据:', JSON.stringify(response.data, null, 2));
    
    // 2. 验证响应格式
    console.log('\n2️⃣ 验证响应数据格式...');
    const data = response.data;
    
    const requiredFields = ['access_token', 'user_id', 'username', 'user_type'];
    const missingFields = requiredFields.filter(field => !data[field]);
    
    if (missingFields.length === 0) {
      console.log('✅ 响应数据格式正确');
      console.log(`🔑 Token: ${data.access_token.substring(0, 20)}...`);
      console.log(`👤 用户: ${data.username} (ID: ${data.user_id})`);
      console.log(`🏷️ 类型: ${data.user_type}`);
    } else {
      console.log('❌ 响应数据格式不完整');
      console.log('缺少字段:', missingFields);
      return false;
    }
    
    // 3. 测试token有效性
    console.log('\n3️⃣ 测试token有效性...');
    try {
      const meResponse = await axios.get(
        `${TEST_CONFIG.baseURL}/api/v1/admin/auth/me`,
        {
          headers: {
            'Authorization': `Bearer ${data.access_token}`
          }
        }
      );
      console.log('✅ Token有效，用户信息获取成功');
      console.log('👤 当前用户:', meResponse.data);
    } catch (tokenError) {
      console.log('❌ Token验证失败:', tokenError.response?.data || tokenError.message);
      return false;
    }
    
    console.log('\n🎉 所有测试通过！登录API工作正常。');
    return true;
    
  } catch (error) {
    console.log('❌ 登录API测试失败');
    
    if (error.response) {
      console.log('📋 错误响应:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
    } else if (error.request) {
      console.log('📋 网络错误: 无法连接到服务器');
      console.log('请确保后端服务正在运行在', TEST_CONFIG.baseURL);
    } else {
      console.log('📋 其他错误:', error.message);
    }
    
    return false;
  }
}

async function testLocalStorageSimulation() {
  console.log('\n🧪 模拟前端localStorage处理...\n');
  
  // 模拟localStorage
  const mockLocalStorage = {};
  
  // 模拟登录响应数据
  const mockLoginResponse = {
    access_token: "eC_CsQPozrB3RbskIwRG37Ha4VSuSRHlj4hrWmSS3NA",
    token_type: "bearer",
    user_id: 1,
    username: "admin",
    user_type: "admin",
    expires_at: "2025-09-03T02:14:22.359889"
  };
  
  console.log('📋 模拟登录响应:', JSON.stringify(mockLoginResponse, null, 2));
  
  // 模拟前端处理逻辑
  if (mockLoginResponse.access_token && mockLoginResponse.user_id && mockLoginResponse.username) {
    // 构造用户对象
    const user = {
      id: mockLoginResponse.user_id,
      username: mockLoginResponse.username,
      email: mockLoginResponse.email || '',
      role: mockLoginResponse.user_type || 'user',
      active: true,
      created_at: new Date().toISOString(),
      last_login: mockLoginResponse.expires_at || new Date().toISOString()
    };
    
    // 保存到模拟localStorage
    mockLocalStorage['auth_token'] = mockLoginResponse.access_token;
    mockLocalStorage['user_info'] = JSON.stringify(user);
    
    console.log('✅ 数据处理成功');
    console.log('🔑 保存的token:', mockLocalStorage['auth_token'].substring(0, 20) + '...');
    console.log('👤 保存的用户信息:', JSON.stringify(user, null, 2));
    
    // 验证登录状态
    const isLoggedIn = !!mockLocalStorage['auth_token'];
    console.log('🔍 登录状态检查:', isLoggedIn ? '✅ 已登录' : '❌ 未登录');
    
    return true;
  } else {
    console.log('❌ 响应数据不完整，无法处理');
    return false;
  }
}

async function main() {
  console.log('🚀 登录问题修复测试开始\n');
  console.log('=' .repeat(50));
  
  // 测试后端API
  const apiTest = await testLoginAPI();
  
  console.log('\n' + '=' .repeat(50));
  
  // 测试前端处理逻辑
  const frontendTest = await testLocalStorageSimulation();
  
  console.log('\n' + '=' .repeat(50));
  console.log('📊 测试总结:');
  console.log(`🔧 后端API测试: ${apiTest ? '✅ 通过' : '❌ 失败'}`);
  console.log(`🎨 前端处理测试: ${frontendTest ? '✅ 通过' : '❌ 失败'}`);
  
  if (apiTest && frontendTest) {
    console.log('\n🎉 所有测试通过！登录问题应该已经修复。');
    console.log('\n📝 修复要点:');
    console.log('1. 修正了前端对API响应格式的处理');
    console.log('2. 统一了token存储的键名');
    console.log('3. 增加了详细的调试日志');
    console.log('4. 改进了错误处理逻辑');
  } else {
    console.log('\n⚠️ 部分测试失败，请检查相关配置。');
  }
}

// 运行测试
main().catch(console.error);