#!/bin/bash

echo "🧪 开始测试视图组件"
echo "===================="

# 进入项目目录
cd "$(dirname "$0")"

# 检查依赖
echo "📦 检查依赖安装..."
if [ ! -d "node_modules" ]; then
    echo "安装依赖..."
    bun install
fi

# 检查视图文件是否存在
echo "🔍 检查视图文件..."
VIEWS_DIR="./src/views"
REQUIRED_VIEWS=("ApiKeyManagement.vue" "AuditLogs.vue" "UserProfile.vue")
MISSING_VIEWS=0

for view in "${REQUIRED_VIEWS[@]}"; do
    if [ -f "$VIEWS_DIR/$view" ]; then
        echo "✅ $view 存在"
    else
        echo "❌ $view 不存在"
        MISSING_VIEWS=$((MISSING_VIEWS+1))
    fi
done

if [ $MISSING_VIEWS -gt 0 ]; then
    echo "❌ 有 $MISSING_VIEWS 个视图文件缺失"
    exit 1
fi

# 检查store文件是否存在
echo "🔍 检查store文件..."
STORES_DIR="./src/stores"
REQUIRED_STORES=("apiKeys.ts" "auditLogs.ts" "user.ts")
MISSING_STORES=0

for store in "${REQUIRED_STORES[@]}"; do
    if [ -f "$STORES_DIR/$store" ]; then
        echo "✅ $store 存在"
    else
        echo "❌ $store 不存在"
        MISSING_STORES=$((MISSING_STORES+1))
    fi
done

if [ $MISSING_STORES -gt 0 ]; then
    echo "❌ 有 $MISSING_STORES 个store文件缺失"
    exit 1
fi

# 检查composables文件是否存在
echo "🔍 检查composables文件..."
COMPOSABLES_DIR="./src/composables"
REQUIRED_COMPOSABLES=("useToast.ts")
MISSING_COMPOSABLES=0

for composable in "${REQUIRED_COMPOSABLES[@]}"; do
    if [ -f "$COMPOSABLES_DIR/$composable" ]; then
        echo "✅ $composable 存在"
    else
        echo "❌ $composable 不存在"
        MISSING_COMPOSABLES=$((MISSING_COMPOSABLES+1))
    fi
done

if [ $MISSING_COMPOSABLES -gt 0 ]; then
    echo "❌ 有 $MISSING_COMPOSABLES 个composable文件缺失"
    exit 1
fi

# 运行开发服务器测试
echo "🌐 运行开发服务器测试..."
if ./dev-server-test.sh; then
    echo "✅ 开发服务器测试通过"
else
    echo "❌ 开发服务器测试失败"
    exit 1
fi

# 运行构建测试
echo "🏗️ 运行构建测试..."
if bun run build; then
    echo "✅ 构建测试通过"
else
    echo "❌ 构建测试失败"
    exit 1
fi

echo "===================="
echo "🎉 所有测试通过！"