#!/usr/bin/env node

/**
 * API配置修复验证脚本
 * 验证API路径配置修复是否成功
 */

import axios from 'axios';

console.log('🔍 API配置修复验证');
console.log('==================');

// 测试配置
const testConfig = {
  baseUrl: 'http://localhost:8000',
  version: 'v1',
  timeout: 5000
};

const adminApiUrl = `${testConfig.baseUrl}/api/${testConfig.version}/admin`;

console.log(`✅ 测试目标: ${adminApiUrl}`);

// 测试API端点可达性
async function testApiEndpoints() {
  const endpoints = [
    { path: '/auth/login', method: 'POST', description: '登录端点' },
    { path: '/users', method: 'GET', description: '用户列表端点' },
    { path: '/api-keys', method: 'GET', description: 'API Key列表端点' }
  ];

  console.log('\n📡 测试API端点可达性:');
  console.log('====================');

  for (const endpoint of endpoints) {
    const fullUrl = `${adminApiUrl}${endpoint.path}`;
    
    try {
      const response = await axios({
        method: endpoint.method,
        url: fullUrl,
        timeout: testConfig.timeout,
        validateStatus: () => true // 接受所有状态码
      });
      
      const status = response.status;
      const statusText = getStatusDescription(status);
      
      console.log(`${endpoint.description.padEnd(20)} | ${endpoint.method.padEnd(4)} | ${status} ${statusText}`);
      
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`${endpoint.description.padEnd(20)} | ${endpoint.method.padEnd(4)} | ❌ 服务器未启动`);
      } else {
        console.log(`${endpoint.description.padEnd(20)} | ${endpoint.method.padEnd(4)} | ❌ ${error.message}`);
      }
    }
  }
}

function getStatusDescription(status) {
  if (status === 401) return '✅ 未授权 (正常，需要登录)';
  if (status === 404) return '❌ 路径不存在';
  if (status === 405) return '⚠️  方法不允许';
  if (status >= 200 && status < 300) return '✅ 成功';
  if (status >= 400 && status < 500) return '⚠️  客户端错误';
  if (status >= 500) return '❌ 服务器错误';
  return '❓ 未知状态';
}

// 验证配置逻辑
function verifyConfigLogic() {
  console.log('\n🔧 配置逻辑验证:');
  console.log('================');
  
  // 模拟前端配置逻辑
  const mockEnv = {
    VITE_API_BASE_URL: 'http://localhost:8000',
    VITE_API_VERSION: 'v1'
  };
  
  const baseUrl = mockEnv.VITE_API_BASE_URL || 'http://localhost:8000';
  const version = mockEnv.VITE_API_VERSION || 'v1';
  const adminEndpoint = `${baseUrl}/api/${version}/admin`;
  
  console.log(`基础URL: ${baseUrl}`);
  console.log(`API版本: ${version}`);
  console.log(`管理员端点: ${adminEndpoint}`);
  
  // 验证路径拼接
  const loginPath = '/auth/login';
  const fullLoginUrl = `${adminEndpoint}${loginPath}`;
  
  console.log(`\n登录请求路径: ${loginPath}`);
  console.log(`完整登录URL: ${fullLoginUrl}`);
  
  const expectedUrl = 'http://localhost:8000/api/v1/admin/auth/login';
  const isCorrect = fullLoginUrl === expectedUrl;
  
  console.log(`预期URL: ${expectedUrl}`);
  console.log(`配置正确: ${isCorrect ? '✅ 是' : '❌ 否'}`);
  
  return isCorrect;
}

// 主函数
async function main() {
  const configCorrect = verifyConfigLogic();
  
  if (configCorrect) {
    console.log('\n🎯 开始测试API端点...');
    await testApiEndpoints();
  } else {
    console.log('\n❌ 配置验证失败，请检查配置逻辑');
    process.exit(1);
  }
  
  console.log('\n📋 修复总结:');
  console.log('============');
  console.log('✅ 环境变量配置: VITE_API_BASE_URL + VITE_API_VERSION');
  console.log('✅ API服务配置: 动态拼接版本路径');
  console.log('✅ 统一配置管理: src/config/api.ts');
  console.log('✅ 版本控制支持: 可通过环境变量切换版本');
  
  console.log('\n🚀 下一步:');
  console.log('=========');
  console.log('1. 启动后端服务: cd zentao-mcp-backend-service && python main.py');
  console.log('2. 启动前端服务: cd zentao-token-web && bun dev');
  console.log('3. 测试登录功能验证修复效果');
}

main().catch(console.error);