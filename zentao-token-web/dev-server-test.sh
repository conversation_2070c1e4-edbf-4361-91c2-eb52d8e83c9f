#!/bin/bash

echo "🌐 开发服务器测试"
echo "=================="

cd "$(dirname "$0")"

# 启动开发服务器
echo "启动开发服务器..."
bun run dev &
DEV_PID=$!

# 等待服务器启动
echo "等待服务器启动..."
sleep 5

# 检查服务器是否运行
if kill -0 $DEV_PID 2>/dev/null; then
    echo "✅ 开发服务器启动成功 (PID: $DEV_PID)"
    
    # 测试服务器响应
    if curl -s http://localhost:3000 > /dev/null; then
        echo "✅ 服务器响应正常"
    else
        echo "⚠️  服务器无响应，可能还在启动中"
    fi
    
    # 停止服务器
    echo "停止开发服务器..."
    kill $DEV_PID
    echo "✅ 开发服务器已停止"
else
    echo "❌ 开发服务器启动失败"
fi

echo "=================="
echo "开发服务器测试完成"