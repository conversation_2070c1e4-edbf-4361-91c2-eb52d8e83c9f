#!/bin/bash

echo "🚀 开始测试 zentao-token-web 项目"
echo "=================================="

# 进入项目目录
cd "$(dirname "$0")"

# 检查依赖
echo "📦 检查依赖安装..."
if [ ! -d "node_modules" ]; then
    echo "安装依赖..."
    bun install
fi

# 类型检查
echo "🔍 TypeScript 类型检查..."
if bun run type-check; then
    echo "✅ 类型检查通过"
else
    echo "❌ 类型检查失败"
    exit 1
fi

# ESLint 检查
echo "🔧 ESLint 代码检查..."
if bun run lint; then
    echo "✅ 代码检查通过"
else
    echo "⚠️  代码检查有警告，但继续执行"
fi

# 构建测试
echo "🏗️  构建测试..."
if bun run build; then
    echo "✅ 构建成功"
    echo "📊 构建文件大小:"
    ls -lh dist/
else
    echo "❌ 构建失败"
    exit 1
fi

# 开发服务器测试
echo "🌐 开发服务器启动测试..."
timeout 5s bun run dev &
DEV_PID=$!
sleep 3

if kill -0 $DEV_PID 2>/dev/null; then
    echo "✅ 开发服务器启动成功"
    kill $DEV_PID
else
    echo "❌ 开发服务器启动失败"
fi

echo "=================================="
echo "🎉 测试完成！"