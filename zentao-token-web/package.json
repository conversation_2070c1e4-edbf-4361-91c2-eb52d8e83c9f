{"name": "zentao-token-web", "version": "0.1.0", "description": "Zentao MCP Admin Web Interface", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit", "test": "npm run type-check && npm run build", "test:dev": "timeout 5s npm run dev || echo 'Dev server test completed'"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "axios": "^1.6.0", "@headlessui/vue": "^1.7.0", "@heroicons/vue": "^2.0.0", "tailwindcss": "^3.4.0"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.0", "autoprefixer": "^10.4.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.0", "postcss": "^8.4.0", "prettier": "^3.1.0", "typescript": "~5.3.0", "vite": "^5.0.0", "vue-tsc": "^1.8.0", "@rushstack/eslint-patch": "^1.10.0"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}}