/**
 * API配置管理
 * 统一管理API版本、路径和环境配置
 */

export interface ApiConfig {
  baseURL: string
  version: string
  timeout: number
  endpoints: {
    admin: string
    mcp: {
      tools: string
      resources: string
    }
  }
}

/**
 * 获取API配置
 */
export function getApiConfig(): ApiConfig {
  const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'
  const version = import.meta.env.VITE_API_VERSION || 'v1'
  
  return {
    baseURL: baseUrl,
    version: version,
    timeout: parseInt(import.meta.env.VITE_API_TIMEOUT || '10000'),
    endpoints: {
      admin: `${baseUrl}/api/${version}/admin`,
      mcp: {
        tools: `${baseUrl}/api/${version}/mcp/tools`,
        resources: `${baseUrl}/api/${version}/mcp/resources`
      }
    }
  }
}

/**
 * API版本管理
 */
export const API_VERSIONS = {
  V1: 'v1',
  V2: 'v2'  // 为将来的版本预留
} as const

/**
 * 环境配置
 */
export const ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  STAGING: 'staging'
} as const

/**
 * 获取当前环境
 */
export function getCurrentEnvironment(): string {
  return import.meta.env.MODE || 'development'
}

/**
 * 调试信息
 */
export function logApiConfig(): void {
  if (getCurrentEnvironment() === 'development') {
    const config = getApiConfig()
    console.group('🔧 API Configuration')
    console.log('Base URL:', config.baseURL)
    console.log('Version:', config.version)
    console.log('Admin Endpoint:', config.endpoints.admin)
    console.log('MCP Tools Endpoint:', config.endpoints.mcp.tools)
    console.log('MCP Resources Endpoint:', config.endpoints.mcp.resources)
    console.log('Timeout:', config.timeout + 'ms')
    console.groupEnd()
  }
}