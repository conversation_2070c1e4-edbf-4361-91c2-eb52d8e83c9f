import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import type { ApiKey, CreateApiKeyRequest, CreateApiKeyResponse, ApiResponse, PaginatedResponse, UsageStats } from '@/types/api'
import type { User, CreateUserRequest, UpdateUserRequest, ChangePasswordRequest, ResetPasswordRequest } from '@/types/user'
import { getApiConfig, logApiConfig } from '@/config/api'

class ApiService {
  private api: AxiosInstance
  private config = getApiConfig()

  constructor() {
    // 使用统一的配置管理
    this.api = axios.create({
      baseURL: this.config.endpoints.admin,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    // 开发环境下输出配置信息
    logApiConfig()

    // 请求拦截器
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
          console.log('添加Authorization头:', `Bearer ${token.substring(0, 20)}...`)
        } else {
          console.log('未找到auth_token，跳过Authorization头')
        }
        return config
      },
      (error) => {
        console.error('请求拦截器错误:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response
      },
      (error) => {
        if (error.response?.status === 401) {
          // Token过期或无效，清除本地存储并跳转到登录页
          localStorage.removeItem('auth_token')
          localStorage.removeItem('user_info')
          window.location.href = '/login'
        }
        return Promise.reject(error)
      }
    )
  }

  // GET请求
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.get(url, config)
  }

  // POST请求
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.post(url, data, config)
  }

  // PUT请求
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.put(url, data, config)
  }

  // PATCH请求
  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.patch(url, data, config)
  }

  // DELETE请求
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.delete(url, config)
  }

  // 获取API配置信息
  getApiConfig() {
    return {
      baseURL: this.api.defaults.baseURL,
      version: this.config.version,
      timeout: this.api.defaults.timeout,
      fullConfig: this.config
    }
  }

  // 用户相关API
  users = {
    // 获取用户列表
    getList: (page: number = 1, size: number = 10) => 
      this.get<ApiResponse<PaginatedResponse<User>>>('/users', { params: { page, size } }),
    
    // 获取用户详情
    getById: (id: number) => this.get<ApiResponse<User>>(`/users/${id}`),
    
    // 创建用户
    create: (userData: CreateUserRequest) => this.post<ApiResponse<User>>('/users', userData),
    
    // 更新用户
    update: (id: number, userData: UpdateUserRequest) => this.put<ApiResponse<User>>(`/users/${id}`, userData),
    
    // 删除用户
    delete: (id: number) => this.delete<ApiResponse<void>>(`/users/${id}`),
    
    // 切换用户状态
    toggleStatus: (id: number) => this.patch<ApiResponse<User>>(`/users/${id}/toggle-status`),
    
    // 重置用户密码
    resetPassword: (id: number, data: ResetPasswordRequest) => this.post<ApiResponse<void>>(`/users/${id}/reset-password`, data)
  }

  // 认证相关API
  auth = {
    // 登录 - 后端直接返回登录响应，不包装在ApiResponse中
    login: (credentials: { username: string; password: string }) => 
      this.post<{
        access_token: string;
        token_type: string;
        user_id: number;
        username: string;
        user_type: string;
        expires_at: string;
      }>('/auth/login', credentials),
    
    // 登出
    logout: () => this.post<ApiResponse<void>>('/auth/logout'),
    
    // 修改密码
    changePassword: (data: ChangePasswordRequest) => 
      this.post<ApiResponse<void>>('/auth/change-password', data),
    
    // 获取当前用户信息
    getCurrentUser: () => this.get<ApiResponse<User>>('/auth/me'),

    // 更新个人资料
    updateProfile: (data: Partial<User>) => this.put<ApiResponse<User>>('/auth/profile', data)
  }

  // API Key相关API
  apiKeys = {
    // 获取API Key列表（后端直接返回 APIKeyListResponse: { api_keys, total, skip, limit }）
    getList: (page: number = 1, size: number = 10) => 
      this.get<any>('/api-keys', { params: { page, size } }),
    
    // 创建API Key
    create: (keyData: CreateApiKeyRequest) => this.post<ApiResponse<CreateApiKeyResponse>>('/api-keys', keyData),
    
    // 删除API Key
    delete: (id: number) => this.delete<{ message: string }>(`/api-keys/${id}`),
    
    // 激活API Key
    activate: (id: number) => this.put<ApiKey>(`/api-keys/${id}/activate`),

    // 撤销API Key
    revoke: (id: number) => this.put<ApiKey>(`/api-keys/${id}/revoke`),
    
    // 重置API Key
    reset: (id: string) => this.post<ApiResponse<CreateApiKeyResponse>>(`/api-keys/${id}/reset`),

    // 获取使用统计
    getStats: () => this.get<ApiResponse<UsageStats>>('/api-keys/stats')
  }

  // 审计日志相关API
  auditLogs = {
    // 获取审计日志列表
    getList: (params?: any) => this.get<ApiResponse<PaginatedResponse<any>>>('/audit-logs', { params }),
    
    // 获取日志详情
    getById: (id: string) => this.get<ApiResponse<any>>(`/audit-logs/${id}`),
    
    // 导出日志
    export: (params?: any) => this.get<Blob>('/audit-logs/export', { params, responseType: 'blob' })
  }
}

// 实现apiService的方法
class ApiServiceImpl extends ApiService {
  // API Keys方法
  async getApiKeys(page: number = 1, pageSize: number = 10) {
    try {
      const response = await this.apiKeys.getList(page, pageSize)
      return response.data
    } catch (error: any) {
      return { status: 'error', error: error.message || '获取API Keys失败' }
    }
  }

  async createApiKey(data: CreateApiKeyRequest) {
    try {
      const response = await this.apiKeys.create(data)
      return response.data
    } catch (error: any) {
      return { status: 'error', error: error.message || '创建API Key失败' }
    }
  }

  async deleteApiKey(keyId: number | string) {
    try {
      const idNum = typeof keyId === 'string' ? parseInt(keyId, 10) : keyId
      const response = await this.apiKeys.delete(idNum)
      return response.data
    } catch (error: any) {
      return { status: 'error', error: error.message || '删除API Key失败' }
    }
  }

  async toggleApiKey(keyId: number | string, isActive: boolean) {
    try {
      const idNum = typeof keyId === 'string' ? parseInt(keyId, 10) : keyId
      const response = isActive
        ? await this.apiKeys.revoke(idNum)
        : await this.apiKeys.activate(idNum)
      return response.data
    } catch (error: any) {
      return { status: 'error', error: error.message || '切换API Key状态失败' }
    }
  }

  async getUsageStats() {
    try {
      const response = await this.apiKeys.getStats()
      return response.data
    } catch (error: any) {
      return { status: 'error', error: error.message || '获取使用统计失败' }
    }
  }
}

export const apiService = new ApiServiceImpl()