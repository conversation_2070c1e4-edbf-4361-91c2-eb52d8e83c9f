import { apiService } from './api'

interface LoginCredentials {
  username: string
  password: string
}

import type { User } from '../types/user'

class AuthService {
  private currentUser: User | null = null

  // 登录
  async login(credentials: LoginCredentials) {
    try {
      console.log('开始登录请求:', credentials.username)
      
      const response = await apiService.auth.login(credentials)
      console.log('登录API原始响应:', response.data)
      
      // 处理API响应 - 后端直接返回登录数据，不是包装在ApiResponse中
      const loginData = response.data as any
      
      if (loginData.access_token && loginData.user_id && loginData.username) {
        // 构造用户对象
        const user = {
          id: loginData.user_id,
          username: loginData.username,
          email: loginData.email || '',
          role: loginData.user_type || 'user',
          active: true,
          created_at: new Date().toISOString(),
          last_login: loginData.expires_at || new Date().toISOString()
        }
        
        console.log('构造的用户对象:', user)
        
        // 保存token和用户信息 - 使用正确的键名
        localStorage.setItem('auth_token', loginData.access_token)
        localStorage.setItem('user_info', JSON.stringify(user))
        this.currentUser = user
        
        console.log('登录成功，已保存到localStorage')
        
        return { token: loginData.access_token, user }
      }
      
      console.error('登录响应数据不完整:', loginData)
      throw new Error('登录失败：响应数据不完整')
    } catch (error) {
      console.error('登录过程中发生错误:', error)
      
      // 清理可能的部分状态
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_info')
      this.currentUser = null
      
      throw error
    }
  }

  // 登出
  async logout() {
    try {
      await apiService.auth.logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地存储
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_info')
      this.currentUser = null
    }
  }

  // 检查是否已登录
  isLoggedIn(): boolean {
    const token = localStorage.getItem('auth_token')
    return !!token
  }

  // 获取当前用户
  getCurrentUser(): User | null {
    if (this.currentUser) {
      return this.currentUser
    }
    
    const userInfo = localStorage.getItem('user_info')
    if (userInfo) {
      this.currentUser = JSON.parse(userInfo)
      return this.currentUser
    }
    
    return null
  }

  // 获取token
  getToken(): string | null {
    return localStorage.getItem('auth_token')
  }

  // 检查用户权限（统一大小写）
  hasPermission(requiredRole: string): boolean {
    const user = this.getCurrentUser()
    if (!user) return false

    const roleHierarchy = {
      'USER': 1,
      'ADMIN': 2
    } as const

    const userRole = (user.role || '').toString().toUpperCase()
    const needRole = (requiredRole || '').toString().toUpperCase()

    const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0
    const requiredLevel = roleHierarchy[needRole as keyof typeof roleHierarchy] || 0

    return userLevel >= requiredLevel
  }

  // 修改密码
  async changePassword(oldPassword: string, newPassword: string) {
    try {
      const response = await apiService.auth.changePassword({
        currentPassword: oldPassword,
        newPassword: newPassword
      })
      return response.data
    } catch (error) {
      throw error
    }
  }

  // 刷新用户信息
  async refreshUserInfo() {
    try {
      const response = await apiService.auth.getCurrentUser()
      if (response.data.data) {
        const user = response.data.data
        localStorage.setItem('user_info', JSON.stringify(user))
        this.currentUser = user
        return user
      }
      return null
    } catch (error) {
      throw error
    }
  }
}

export const authService = new AuthService()