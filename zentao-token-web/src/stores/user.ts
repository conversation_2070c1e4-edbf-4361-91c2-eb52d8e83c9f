import { defineStore } from 'pinia'
import { ref } from 'vue'
import { apiService } from '@/services/api'

interface User {
  id: number
  username: string
  email: string
  role: string
  active: boolean
  created_at: string
  last_login: string
  phone?: string
  department?: string
  bio?: string
}

export const userStore = defineStore('user', () => {
  // 状态
  const users = ref<User[]>([])
  const currentUser = ref<User | null>(null)
  const loading = ref(false)
  const error = ref(null)

  // 获取用户列表
  const fetchUsers = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await apiService.get('/users')
      if (response.data && Array.isArray(response.data.users)) {
        // The API returns { users: [], ... } and user objects have user_type and is_active
        // We need to map them to the frontend's User model (role, active)
        users.value = response.data.users.map((user: any) => ({
          ...user,
          role: user.user_type,
          active: user.is_active,
        }))
      } else {
        console.error('Invalid users data from API:', response.data)
        users.value = []
      }
    } catch (err: any) {
      error.value = err.message || '获取用户列表失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 创建用户
  const createUser = async (userData: any) => {
    loading.value = true
    error.value = null
    try {
      const response = await apiService.post('/users', userData)
      return response.data
    } catch (err: any) {
      error.value = err.message || '创建用户失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 更新用户
  const updateUser = async (userId: number, userData: any) => {
    loading.value = true
    error.value = null
    try {
      const response = await apiService.put(`/users/${userId}`, userData)
      return response.data
    } catch (err: any) {
      error.value = err.message || '更新用户失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 删除用户
  const deleteUser = async (userId: number) => {
    loading.value = true
    error.value = null
    try {
      await apiService.delete(`/users/${userId}`)
      users.value = users.value.filter((user: any) => user.id !== userId)
    } catch (err: any) {
      error.value = err.message || '删除用户失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 切换用户状态
  const toggleUserStatus = async (userId: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await apiService.patch(`/users/${userId}/toggle-status`)
      return response.data
    } catch (err: any) {
      error.value = err.message || '切换用户状态失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 重置用户密码
  const resetUserPassword = async (userId: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await apiService.post(`/users/${userId}/reset-password`)
      return response.data
    } catch (err: any) {
      error.value = err.message || '重置密码失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 获取用户详情
  const getUserById = async (userId: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await apiService.get(`/users/${userId}`)
      return response.data
    } catch (err: any) {
      error.value = err.message || '获取用户详情失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 获取当前用户信息
  const fetchCurrentUser = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await apiService.get('/users/me')
      currentUser.value = response.data
      return response.data
    } catch (err: any) {
      error.value = err.message || '获取当前用户信息失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 更新个人资料
  const updateProfile = async (profileData: any) => {
    loading.value = true
    error.value = null
    try {
      const response = await apiService.put('/users/me/profile', profileData)
      currentUser.value = { ...currentUser.value, ...response.data }
      return response.data
    } catch (err: any) {
      error.value = err.message || '更新个人资料失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 修改密码
  const changePassword = async (passwordData: { currentPassword: string; newPassword: string }) => {
    loading.value = true
    error.value = null
    try {
      const response = await apiService.post('/users/me/change-password', passwordData)
      return response.data
    } catch (err: any) {
      error.value = err.message || '修改密码失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    // 状态
    users,
    currentUser,
    loading,
    error,
    
    // 方法
    fetchUsers,
    createUser,
    updateUser,
    deleteUser,
    toggleUserStatus,
    resetUserPassword,
    getUserById,
    fetchCurrentUser,
    updateProfile,
    changePassword
  }
})