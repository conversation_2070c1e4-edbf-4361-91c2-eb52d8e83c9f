import { defineStore } from 'pinia'
import { ref } from 'vue'
import { apiService } from '@/services/api'
import type { PaginatedResponse } from '@/types/api'

export interface AuditLog {
  id: string
  user_id: string
  username: string
  action: string
  resource_type: string
  resource_id: string
  timestamp: string
  ip_address: string
  user_agent: string
  details?: Record<string, any>
}

export interface LogsFilter {
  page: number
  size: number
  user_id?: string
  action?: string
  resource_type?: string
  start_date?: string
  end_date?: string
  search?: string
}

export const useAuditLogsStore = defineStore('auditLogs', () => {
  // 状态
  const logs = ref<AuditLog[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const totalItems = ref(0)
  const totalPages = ref(0)

  // 获取审计日志（统一通过 apiService，自动带上 Authorization，并走 /api/v1/admin 前缀）
  const fetchLogs = async (params: LogsFilter): Promise<PaginatedResponse<AuditLog>> => {
    loading.value = true
    error.value = null

    try {
      const response = await apiService.auditLogs.getList(params)
      const payload: any = response.data

      // 兼容两种返回结构：
      // A) { status: 'success', data: { items, total, total_pages } }
      // B) { items, totalItems, totalPages } 旧式直返
      const data = payload?.status === 'success' && payload?.data ? payload.data : payload

      const items: AuditLog[] = data?.items ?? data?.logs ?? []
      const total: number = data?.total ?? data?.totalItems ?? items.length
      const pages: number = data?.total_pages ?? data?.totalPages ?? Math.ceil(total / (params.size || 10))

      logs.value = items
      totalItems.value = total
      totalPages.value = pages

      return {
        items,
        total: total,
        page: params.page,
        size: params.size,
        pages: pages,
      }
    } catch (err: any) {
      console.error('Failed to fetch audit logs:', err)
      error.value = err?.message || 'Failed to fetch audit logs'
      logs.value = []
      throw err
    } finally {
      loading.value = false
    }
  }

  // 获取特定日志详情
  const fetchLogDetails = async (logId: string): Promise<AuditLog | null> => {
    loading.value = true
    error.value = null

    try {
      const response = await apiService.auditLogs.getById(logId)
      const payload: any = response.data
      const data: AuditLog = payload?.status === 'success' && payload?.data ? payload.data : payload
      return data ?? null
    } catch (err: any) {
      console.error(`Failed to fetch log details for ID ${logId}:`, err)
      error.value = err?.message || 'Failed to fetch log details'
      return null
    } finally {
      loading.value = false
    }
  }

  // 导出日志
  const exportLogs = async (filters: Partial<LogsFilter> = {}): Promise<boolean> => {
    loading.value = true
    error.value = null

    try {
      const response = await apiService.auditLogs.export(filters)
      const blob = response.data as Blob

      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `audit-logs-${new Date().toISOString().split('T')[0]}.csv`)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      return true
    } catch (err: any) {
      console.error('Failed to export audit logs:', err)
      error.value = err?.message || 'Failed to export audit logs'
      return false
    } finally {
      loading.value = false
    }
  }

  return {
    logs,
    loading,
    error,
    totalItems,
    totalPages,
    fetchLogs,
    fetchLogDetails,
    exportLogs
  }
})