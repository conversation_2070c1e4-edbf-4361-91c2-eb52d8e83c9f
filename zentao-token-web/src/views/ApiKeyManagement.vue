<template>
  <div class="p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">API Key管理</h1>
      <p class="text-gray-600">管理系统API密钥</p>
    </div>

    <!-- 操作栏 -->
    <div class="mb-4 flex justify-between items-center">
      <div class="flex space-x-2">
        <button 
          @click="() => { store.resetError(); showCreateModal = true; }"
          class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
        >
          新建API Key
        </button>
        <button 
          @click="refreshApiKeys"
          class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
        >
          刷新
        </button>
      </div>
      
      <div class="flex items-center space-x-2">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索API Key..."
          class="border border-gray-300 rounded-lg px-3 py-2"
        />
      </div>
    </div>

    <!-- API Key列表 -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              名称
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              密钥预览
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              状态
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              创建时间
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              操作
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="apiKey in filteredApiKeys" :key="apiKey.id">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ apiKey.name }}</div>
                  <div class="text-sm text-gray-500">{{ apiKey.description }}</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="text-sm text-gray-500 truncate max-w-xs">
                  {{ apiKey.key_value }}
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span :class="apiKey.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" 
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full">
                {{ apiKey.is_active ? '活跃' : '禁用' }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ formatDate(apiKey.created_at) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex space-x-2">
                <button 
                  @click="toggleApiKeyStatus(apiKey)" 
                  :class="apiKey.is_active ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'"
                >
                  {{ apiKey.is_active ? '禁用' : '启用' }}
                </button>
                <button 
                  @click="deleteApiKey(apiKey)" 
                  class="text-red-600 hover:text-red-900"
                >
                  删除
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 新建成功后展示一次性Key -->
    <div v-if="showCreatedModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div class="bg-white rounded-lg shadow-lg w-full max-w-lg p-6">
        <h2 class="text-xl font-semibold mb-2">请立即复制此API Key</h2>
        <p class="text-sm text-red-600 mb-4">出于安全考虑，此密钥仅在创建时显示一次，关闭窗口后将无法再次查看。</p>
        <div class="mb-4">
          <label class="block text-sm text-gray-600 mb-1">名称</label>
          <div class="px-3 py-2 bg-gray-50 rounded border">{{ createdKey?.name }}</div>
        </div>
        <div class="mb-4">
          <label class="block text-sm text-gray-600 mb-1">API Key</label>
          <div class="px-3 py-2 bg-gray-50 rounded border font-mono text-sm break-all">{{ createdKey?.api_key }}</div>
        </div>
        <div class="flex justify-end space-x-2">
          <button @click="copyCreatedKey" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">复制</button>
          <button @click="closeCreatedModal" class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded">我已复制</button>
        </div>
      </div>
    </div>

    <!-- 创建API Key模态框 -->
    <CreateApiKeyModal
      :visible="showCreateModal"
      :loading="store.loading"
      :error="store.error"
      @close="showCreateModal = false"
      @submit="createApiKey"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useApiKeysStore } from '@/stores/apiKeys'
import type { ApiKey, CreateApiKeyRequest, CreateApiKeyResponse } from '@/types/api'
import CreateApiKeyModal from '@/components/CreateApiKeyModal.vue'

const store = useApiKeysStore()

// 响应式数据
const showCreateModal = ref(false)
const searchQuery = ref('')
const showCreatedModal = ref(false)
const createdKey = ref<CreateApiKeyResponse | null>(null)

// 计算属性
const filteredApiKeys = computed(() => {
  if (!searchQuery.value) return store.apiKeys
  return store.apiKeys.filter(apiKey => 
    apiKey.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    apiKey.description?.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 方法
const refreshApiKeys = async () => {
  await store.fetchApiKeys()
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const createApiKey = async (data: Pick<CreateApiKeyRequest, 'name'>) => {
  const result = await store.createApiKey(data)
  if (result && !store.error) {
    createdKey.value = result
    showCreateModal.value = false
    showCreatedModal.value = true
  }
}

const copyCreatedKey = async () => {
  try {
    if (createdKey.value?.api_key) {
      await navigator.clipboard.writeText(createdKey.value.api_key)
      alert('已复制到剪贴板')
    }
  } catch {
    alert('复制失败，请手动选择并复制')
  }
}

const closeCreatedModal = () => {
  showCreatedModal.value = false
  createdKey.value = null
}

const toggleApiKeyStatus = async (apiKey: ApiKey) => {
  try {
    await store.toggleApiKey(apiKey.id)
  } catch (error) {
    console.error('切换API Key状态失败:', error)
    alert('切换API Key状态失败')
  }
}

const deleteApiKey = async (apiKey: ApiKey) => {
  if (!confirm(`确定要删除API Key "${apiKey.name}"吗？`)) return
  try {
    const message = await store.deleteApiKey(apiKey.id)
    alert(message)
  } catch (error: any) {
    console.error('删除API Key失败:', error)
    alert(error.message || '删除API Key失败')
  }
}

// 生命周期钩子
onMounted(() => {
  refreshApiKeys()
})
</script>