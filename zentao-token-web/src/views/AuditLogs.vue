<template>
  <div class="p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">审计日志</h1>
      <p class="text-gray-600">查看系统操作记录</p>
    </div>

    <!-- 操作栏 -->
    <div class="mb-4 flex justify-between items-center">
      <div class="flex space-x-2">
        <button 
          @click="refreshLogs"
          class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
        >
          刷新
        </button>
        <button 
          @click="exportLogs"
          class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg"
        >
          导出
        </button>
      </div>
      
      <div class="flex items-center space-x-2">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索日志..."
          class="border border-gray-300 rounded-lg px-3 py-2"
        />
      </div>
    </div>

    <!-- 筛选条件 -->
    <div class="mb-4 bg-gray-50 p-4 rounded-lg">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">操作类型</label>
          <select 
            v-model="filters.actionType" 
            class="w-full border border-gray-300 rounded-lg px-3 py-2"
          >
            <option value="">全部</option>
            <option value="LOGIN">登录</option>
            <option value="LOGOUT">登出</option>
            <option value="CREATE">创建</option>
            <option value="UPDATE">更新</option>
            <option value="DELETE">删除</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">用户</label>
          <select 
            v-model="filters.userId" 
            class="w-full border border-gray-300 rounded-lg px-3 py-2"
          >
            <option value="">全部</option>
            <option v-for="user in users" :key="user.id" :value="user.id">
              {{ user.username }}
            </option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">时间范围</label>
          <div class="flex space-x-2">
            <input 
              v-model="filters.startDate" 
              type="date" 
              class="w-full border border-gray-300 rounded-lg px-3 py-2"
            />
            <input 
              v-model="filters.endDate" 
              type="date" 
              class="w-full border border-gray-300 rounded-lg px-3 py-2"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 日志列表 -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              时间
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              用户
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              操作类型
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              资源类型
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              详情
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="log in filteredLogs" :key="log.id">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ formatDate(log.timestamp) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900">{{ log.username }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span :class="getActionTypeClass(log.action)" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full">
                {{ getActionTypeText(log.action) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ log.resource_type }}
            </td>
            <td class="px-6 py-4 text-sm text-gray-500">
              <button 
                @click="showLogDetails(log)" 
                class="text-blue-600 hover:text-blue-900"
              >
                查看详情
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <div class="mt-4 flex justify-between items-center">
      <div class="text-sm text-gray-700">
        显示 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalItems) }} 条，共 {{ totalItems }} 条
      </div>
      <div class="flex space-x-2">
        <button 
          @click="prevPage" 
          :disabled="currentPage === 1"
          :class="currentPage === 1 ? 'bg-gray-300 cursor-not-allowed' : 'bg-gray-500 hover:bg-gray-600'"
          class="text-white px-4 py-2 rounded-lg"
        >
          上一页
        </button>
        <button 
          @click="nextPage" 
          :disabled="currentPage === totalPages"
          :class="currentPage === totalPages ? 'bg-gray-300 cursor-not-allowed' : 'bg-gray-500 hover:bg-gray-600'"
          class="text-white px-4 py-2 rounded-lg"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 日志详情模态框 -->
    <div v-if="showDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">日志详情</h3>
            <button @click="showDetailsModal = false" class="text-gray-500 hover:text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div>
              <p class="text-sm font-medium text-gray-700">时间</p>
              <p class="text-sm text-gray-900">{{ formatDate(selectedLog?.timestamp) }}</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-700">用户</p>
              <p class="text-sm text-gray-900">{{ selectedLog?.username }}</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-700">操作类型</p>
              <p class="text-sm text-gray-900">{{ getActionTypeText(selectedLog?.action) }}</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-700">资源类型</p>
              <p class="text-sm text-gray-900">{{ selectedLog?.resource_type }}</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-700">资源ID</p>
              <p class="text-sm text-gray-900">{{ selectedLog?.resource_id }}</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-700">IP地址</p>
              <p class="text-sm text-gray-900">{{ selectedLog?.ip_address }}</p>
            </div>
          </div>
          <div class="mb-4">
            <p class="text-sm font-medium text-gray-700 mb-2">详细信息</p>
            <pre class="bg-gray-50 p-4 rounded-lg text-sm overflow-auto max-h-60">{{ JSON.stringify(selectedLog?.details, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useAuditLogsStore, type AuditLog } from '@/stores/auditLogs'
import { userStore } from '@/stores/user'
import type { User } from '@/types/user'

const logsStore = useAuditLogsStore()
const usersStore = userStore()

// 响应式数据
const searchQuery = ref('')
const showDetailsModal = ref(false)
const selectedLog = ref<AuditLog | null>(null)
const currentPage = ref(1)
const pageSize = ref(10)

const filters = ref({
  actionType: '',
  userId: '',
  startDate: '',
  endDate: ''
})

// 计算属性
const users = computed(() => usersStore.users)
const totalItems = computed(() => logsStore.totalItems)
const totalPages = computed(() => logsStore.totalPages)

const filteredLogs = computed(() => logsStore.logs)

// 方法
const refreshLogs = async () => {
  await logsStore.fetchLogs({
    page: currentPage.value,
    size: pageSize.value,
    user_id: filters.value.userId || undefined,
    action: filters.value.actionType || undefined,
    start_date: filters.value.startDate || undefined,
    end_date: filters.value.endDate || undefined,
    search: searchQuery.value || undefined
  })
}

const exportLogs = () => {
  // 实现导出功能
  alert('导出功能待实现')
}

const formatDate = (dateString: string | undefined): string => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const getActionTypeText = (type: string | undefined): string => {
  if (!type) return ''
  const types: Record<string, string> = {
    LOGIN: '登录',
    LOGOUT: '登出',
    CREATE: '创建',
    UPDATE: '更新',
    DELETE: '删除'
  }
  return types[type] || type
}

const getActionTypeClass = (type: string | undefined): string => {
  if (!type) return 'bg-gray-100 text-gray-800'
  const classes: Record<string, string> = {
    LOGIN: 'bg-blue-100 text-blue-800',
    LOGOUT: 'bg-gray-100 text-gray-800',
    CREATE: 'bg-green-100 text-green-800',
    UPDATE: 'bg-yellow-100 text-yellow-800',
    DELETE: 'bg-red-100 text-red-800'
  }
  return classes[type] || 'bg-gray-100 text-gray-800'
}

const showLogDetails = (log: AuditLog): void => {
  selectedLog.value = log
  showDetailsModal.value = true
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

// 生命周期钩子
onMounted(async () => {
  await usersStore.fetchUsers()
  await refreshLogs()
})

watch([currentPage, filters, searchQuery], refreshLogs, { deep: true })
</script>