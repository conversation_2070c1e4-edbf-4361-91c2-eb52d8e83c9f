<template>
  <div class="p-6">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">仪表板</h1>
      <p class="text-gray-600">欢迎使用禅道MCP管理系统</p>
    </div>

    <!-- 内容区域 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 快速操作 -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">快速操作</h3>
        <div class="space-y-3">
          <router-link
            v-if="canManageUsers"
            to="/users"
            class="flex items-center p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
          >
            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
              <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
              </svg>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-900">用户管理</div>
              <div class="text-xs text-gray-500">管理系统用户和权限</div>
            </div>
          </router-link>

          <router-link
            to="/api-keys"
            class="flex items-center p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
          >
            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
              <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-900">API Key管理</div>
              <div class="text-xs text-gray-500">管理API访问密钥</div>
            </div>
          </router-link>

          <!-- 临时隐藏审计日志入口 -->
          <router-link
            v-if="false"
            to="/audit-logs"
            class="flex items-center p-3 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors"
          >
            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center mr-3">
              <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-900">审计日志</div>
              <div class="text-xs text-gray-500">查看系统操作记录</div>
            </div>
          </router-link>
        </div>
      </div>

      <!-- 系统信息 -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">系统信息</h3>
        <div class="space-y-4">
          <div class="flex justify-between">
            <span class="text-sm text-gray-500">当前用户</span>
            <span class="text-sm font-medium">{{ currentUser?.username }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-500">用户类型</span>
            <span class="text-sm font-medium">{{ getUserTypeText(currentUser?.role) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-gray-500">登录时间</span>
            <span class="text-sm font-medium">{{ formatDate(new Date()) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { authService } from '@/services/auth'

// 计算属性
const currentUser = computed(() => authService.getCurrentUser())
const canManageUsers = computed(() => authService.hasPermission('ADMIN'))
const canViewAuditLogs = computed(() => authService.hasPermission('ADMIN'))

// 方法
const getUserTypeText = (type?: string) => {
  switch (type) {
    case 'ADMIN': return '管理员'
    case 'USER': return '普通用户'
    default: return '未知'
  }
}

const formatDate = (date: Date) => {
  return date.toLocaleString('zh-CN')
}

// 生命周期
</script>