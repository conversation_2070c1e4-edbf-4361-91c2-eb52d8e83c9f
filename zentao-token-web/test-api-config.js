#!/usr/bin/env node

/**
 * API配置测试脚本
 * 用于验证API路径配置是否正确
 */

// 模拟环境变量
process.env.VITE_API_BASE_URL = 'http://localhost:8000';
process.env.VITE_API_VERSION = 'v1';

// 模拟import.meta.env
global.import = {
  meta: {
    env: {
      VITE_API_BASE_URL: process.env.VITE_API_BASE_URL,
      VITE_API_VERSION: process.env.VITE_API_VERSION
    }
  }
};

console.log('🔧 API配置测试');
console.log('================');

// 测试配置逻辑
const baseUrl = process.env.VITE_API_BASE_URL || 'http://localhost:8000';
const apiVersion = process.env.VITE_API_VERSION || 'v1';
const apiBaseUrl = `${baseUrl}/api/${apiVersion}/admin`;

console.log(`✅ 基础URL: ${baseUrl}`);
console.log(`✅ API版本: ${apiVersion}`);
console.log(`✅ 完整API URL: ${apiBaseUrl}`);

// 测试各种API路径
const testPaths = [
  '/auth/login',
  '/auth/logout', 
  '/users',
  '/api-keys',
  '/audit-logs'
];

console.log('\n📡 API路径测试:');
console.log('================');

testPaths.forEach(path => {
  const fullUrl = `${apiBaseUrl}${path}`;
  console.log(`${path.padEnd(15)} -> ${fullUrl}`);
});

console.log('\n🎯 预期结果:');
console.log('================');
console.log('登录请求应该发送到: http://localhost:8000/api/v1/admin/auth/login');
console.log('这与后端路由配置匹配: /api/v1/admin + /auth/login');

console.log('\n✨ 配置修复完成！');