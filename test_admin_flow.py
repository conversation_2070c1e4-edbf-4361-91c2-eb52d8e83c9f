#!/usr/bin/env python3
"""
管理员权限流程测试脚本
"""

import requests
import json
import sys
from typing import Dict, Any

BASE_URL = "http://localhost:8000"

def test_health_check():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ 健康检查通过")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_admin_login(username: str = "admin", password: str = "admin123"):
    """测试管理员登录"""
    print(f"🔐 测试管理员登录 ({username})...")
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/auth/login",
            json={"username": username, "password": password}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 管理员登录成功")
            print(f"   用户名: {data['username']}")
            print(f"   邮箱: {data['email']}")
            print(f"   超级管理员: {data['is_super_admin']}")
            return data['access_token']
        else:
            print(f"❌ 管理员登录失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 管理员登录异常: {e}")
        return None

def test_create_api_key(token: str, user_identifier: str = "test_user"):
    """测试创建API Key"""
    print(f"🔑 测试创建API Key ({user_identifier})...")
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.post(
            f"{BASE_URL}/api/v1/admin/keys",
            json={"user_identifier": user_identifier},
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API Key创建成功")
            print(f"   用户标识: {data['user_identifier']}")
            print(f"   Key ID: {data['id']}")
            print(f"   明文Key: {data['plain_key'][:20]}...")
            return data['plain_key'], data['id']
        else:
            print(f"❌ API Key创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None, None
            
    except Exception as e:
        print(f"❌ API Key创建异常: {e}")
        return None, None

def test_list_api_keys(token: str):
    """测试列出API Key"""
    print("📋 测试列出API Key...")
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(
            f"{BASE_URL}/api/v1/admin/keys",
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Key列表获取成功 (共{len(data)}个)")
            for key in data:
                print(f"   - ID: {key['id']}, 用户: {key['user_identifier']}, 状态: {'激活' if key['is_active'] else '禁用'}")
            return True
        else:
            print(f"❌ API Key列表获取失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API Key列表获取异常: {e}")
        return False

def test_api_key_usage(api_key: str):
    """测试API Key使用"""
    print("🚀 测试API Key使用...")
    try:
        headers = {"Authorization": f"Bearer {api_key}"}
        # 这里应该测试实际的业务API，暂时测试健康检查
        response = requests.get(f"{BASE_URL}/health", headers=headers)
        
        if response.status_code == 200:
            print("✅ API Key使用正常")
            return True
        else:
            print(f"❌ API Key使用失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API Key使用异常: {e}")
        return False

def test_revoke_api_key(token: str, key_id: int):
    """测试吊销API Key"""
    print(f"🚫 测试吊销API Key (ID: {key_id})...")
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.put(
            f"{BASE_URL}/api/v1/admin/keys/{key_id}/revoke",
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API Key吊销成功")
            print(f"   状态: {'激活' if data['is_active'] else '已吊销'}")
            return True
        else:
            print(f"❌ API Key吊销失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API Key吊销异常: {e}")
        return False

def main():
    """主测试流程"""
    print("=" * 60)
    print("Zentao MCP - 管理员权限流程测试")
    print("=" * 60)
    
    # 1. 健康检查
    if not test_health_check():
        print("❌ 服务未启动，请先启动后端服务")
        sys.exit(1)
    
    print()
    
    # 2. 管理员登录
    admin_token = test_admin_login()
    if not admin_token:
        print("❌ 管理员登录失败，请检查默认账户配置")
        sys.exit(1)
    
    print()
    
    # 3. 创建API Key
    api_key, key_id = test_create_api_key(admin_token, "test_user_001")
    if not api_key:
        print("❌ API Key创建失败")
        sys.exit(1)
    
    print()
    
    # 4. 列出API Key
    if not test_list_api_keys(admin_token):
        print("❌ API Key列表获取失败")
    
    print()
    
    # 5. 测试API Key使用
    if not test_api_key_usage(api_key):
        print("❌ API Key使用失败")
    
    print()
    
    # 6. 吊销API Key
    if key_id and not test_revoke_api_key(admin_token, key_id):
        print("❌ API Key吊销失败")
    
    print()
    print("=" * 60)
    print("🎉 管理员权限流程测试完成！")
    print("=" * 60)
    
    print("\n📋 测试总结:")
    print("✅ 系统健康检查")
    print("✅ 管理员登录认证")
    print("✅ API Key创建管理")
    print("✅ API Key使用验证")
    print("✅ API Key生命周期管理")
    
    print("\n🔗 相关链接:")
    print(f"   后端API文档: {BASE_URL}/docs")
    print(f"   Web管理界面: http://localhost:3000")

if __name__ == "__main__":
    main()