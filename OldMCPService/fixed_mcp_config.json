{"mcpServers": {"zentao-mcp": {"command": "/Users/<USER>/SourceCode/Git/feiniu/Other/zentaomcpserver/.venv/bin/python", "args": ["-m", "zentao_mcp.main"], "cwd": "/Users/<USER>/SourceCode/Git/feiniu/Other/zentaomcpserver", "env": {"MCP_TOKEN": "your-secret-token", "MCP_TRANSPORT": "stdio", "LOG_LEVEL": "INFO", "LOG_DIR": "/var/folders/lk/xqjrq_gn23nf4lr398j4b8q40000gn/T/zentao_mcp_logs", "ENVIRONMENT": "beta", "BETA_DOMAIN": "newzentao-api.beta1.fn", "PREVIEW_DOMAIN": "stage-newzentao-api.idc1.fn", "ONLINE_DOMAIN": "newzentao-api.idc1.fn", "ENABLE_CACHE": "true", "CACHE_TTL": "300", "CACHE_MAX_SIZE": "1000", "CACHE_STRATEGY": "LRU", "REQUEST_TIMEOUT": "30", "MAX_CONCURRENT_REQUESTS": "100", "CONNECTION_POOL_SIZE": "20", "ENABLE_AUTH": "false", "TOKEN_MIN_LENGTH": "16", "LOG_FORMAT": "text", "LOG_MAX_SIZE": "10MB", "LOG_BACKUP_COUNT": "5", "ENABLE_DIRECT_TOOLS": "true", "ENABLE_ANALYSIS_TOOLS": "true", "ENABLE_BUSINESS_TOOLS": "false", "ENABLE_LEGACY_TOOLS": "false", "ENABLE_EXPERIMENTAL_FEATURES": "false", "ENABLE_DEBUG_MODE": "false"}, "disabled": false, "autoApprove": ["batch_query_stories", "validate_story_existence", "analyze_story_workload", "query_bugs_by_time_range", "analyze_bugs_by_dept_and_time", "filter_bugs_by_criteria", "project_summary_analysis", "story_task_relation_query", "bug_to_story_tracking", "personnel_workload_analysis", "zentao_get_departments", "zentao_get_projects", "zentao_get_stories", "zentao_get_tasks", "zentao_get_bugs", "zentao_get_users", "mcp_analyze_project_progress", "mcp_analyze_bug_trends", "mcp_analyze_story_progress", "health_check"]}}}