# HTTP连通性测试用例说明

## 概述

本文档介绍了为禅道MCP服务器创建的HTTP连通性测试用例，这些测试用例直接调用`ZentaoClient`来验证各个API接口的连通性和功能。

## 测试文件说明

### 1. test_connectivity_runner.py - 快速连通性检查脚本

**目的**: 提供一个独立的脚本，可以快速验证禅道API的基本连通性。

**特点**:
- 可独立运行，无需pytest框架
- 提供详细的日志输出
- 支持环境参数指定
- 100%成功率的连通性验证

**主要测试项**:
- ✅ 部门列表获取 (96个部门)
- ✅ 项目列表获取 (2421个项目)  
- ✅ 项目任务查询
- ✅ 项目需求查询
- ✅ 项目Bug查询
- ✅ 需求存在性检查
- ✅ 需求工时查询
- ✅ 性能测试 (并发请求)

**使用方法**:
```bash
# 使用默认环境
python tests/test_connectivity_runner.py

# 指定环境
python tests/test_connectivity_runner.py beta
python tests/test_connectivity_runner.py online
```

### 2. test_http_connectivity.py - 完整HTTP连通性测试套件

**目的**: 基于pytest框架的完整HTTP连通性测试，包含各种测试场景。

**测试类**:

#### TestHTTPConnectivity
- 测试基本API连通性
- 验证响应格式和数据结构
- 支持客户端初始化验证

#### TestBatchRequestConnectivity  
- 测试批量请求功能
- 验证时间范围查询
- 测试需求批量操作

#### TestErrorHandlingConnectivity
- 测试错误处理机制
- 验证无效参数处理
- 模拟网络超时场景

#### TestPerformanceConnectivity
- 测试并发请求性能
- 验证缓存机制效果
- 性能指标收集

**使用方法**:
```bash
# 运行所有HTTP连通性测试
python -m pytest tests/test_http_connectivity.py -v

# 运行特定测试类
python -m pytest tests/test_http_connectivity.py::TestHTTPConnectivity -v
```

### 3. test_direct_client_calls.py - 直接客户端调用测试

**目的**: 测试ZentaoClient的底层HTTP请求功能，不依赖MCP框架。

**测试类**:

#### TestDirectClientCalls
- 原始HTTP请求测试
- HTTP客户端属性验证  
- 各API端点直接调用
- 数据格式验证

#### TestClientErrorHandling
- 无效端点处理
- 格式错误数据处理
- 大响应数据处理

#### TestClientPerformance
- 并发请求性能测试
- 串行vs并发性能对比
- 成功率统计

#### TestClientConnectionPool
- 连接复用测试
- 连接池限制验证
- 资源管理验证

**使用方法**:
```bash
# 运行所有直接客户端测试
python -m pytest tests/test_direct_client_calls.py -v

# 运行特定测试
python -m pytest tests/test_direct_client_calls.py::TestDirectClientCalls::test_department_direct_call -v
```

## 关键配置说明

### 环境配置
测试使用的环境配置来自`zentao_mcp/config.py`:
- `beta`: http://newzentao-api.beta1.fn (默认)
- `online`: 生产环境

### 数据格式处理
由于客户端方法默认返回模型对象，测试中需要使用`parse_model=False`参数获取原始字典格式:

```python
# 获取原始字典格式响应
response = await client.get_all_departments_async(parse_model=False)
assert isinstance(response, dict)
assert "rsCode" in response

# 获取模型对象格式响应  
departments = await client.get_all_departments_async(parse_model=True)
assert isinstance(departments, list)
```

## 测试结果总结

### 快速连通性测试 (test_connectivity_runner.py)
- **总体成功率**: 100% (7/7)
- **平均响应时间**: < 50ms
- **并发成功率**: 100%
- **测试环境**: beta

### HTTP连通性测试套件
- **基础连通性**: 部分通过 (需要修复parse_model参数)
- **批量请求**: 基本功能正常
- **错误处理**: 正常工作
- **性能测试**: 部分通过

### 直接客户端测试
- **基础功能**: 8/14 通过
- **HTTP属性**: 部分失败 (httpx版本差异)
- **错误处理**: 正常工作
- **性能测试**: 基本通过

## 发现的问题和修复

### 1. 方法名不匹配
- `get_users_by_dept_async` → `get_users_by_department_async`
- `get_story_effort_async` → `get_story_info_async`

### 2. 数据格式处理
- 需要使用`parse_model=False`获取原始响应
- 模型对象不支持`.get()`方法

### 3. API方法缺失
- `get_users_by_account_async`方法不存在
- 需要使用`get_user_info_async`替代

### 4. httpx版本差异
- `AsyncClient.limits`属性可能不存在
- 需要适配不同httpx版本

## 使用建议

### 1. 快速验证连通性
推荐使用`test_connectivity_runner.py`进行快速验证:
```bash
python tests/test_connectivity_runner.py
```

### 2. 完整功能测试  
使用pytest运行完整测试套件:
```bash
python -m pytest tests/test_http_connectivity.py tests/test_direct_client_calls.py -v
```

### 3. CI/CD集成
可以将快速连通性测试集成到CI/CD流程中:
```yaml
- name: Test API Connectivity
  run: python tests/test_connectivity_runner.py
```

### 4. 调试API问题
当API出现问题时，可以使用直接客户端测试定位具体问题:
```bash
python -m pytest tests/test_direct_client_calls.py::TestDirectClientCalls::test_raw_http_request -v -s
```

## 总结

通过创建这些测试用例，我们实现了:

1. **全面的HTTP连通性验证** - 覆盖了所有主要API端点
2. **多层次测试策略** - 从快速检查到详细测试
3. **实用的调试工具** - 便于定位和解决API问题  
4. **性能基准测试** - 监控API响应性能
5. **错误处理验证** - 确保系统健壮性

这些测试用例为禅道MCP服务器的API连通性提供了可靠的验证机制，有助于确保系统的稳定运行。