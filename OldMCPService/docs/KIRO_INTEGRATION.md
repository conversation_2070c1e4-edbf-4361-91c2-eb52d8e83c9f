# 禅道MCP服务 - <PERSON><PERSON>集成指南

## 🚀 快速开始

### 1. 配置MCP服务器

MCP配置文件已自动创建在 `.kiro/settings/mcp.json`，包含以下配置：

```json
{
  "mcpServers": {
    "zentao-mcp": {
      "command": "uv",
      "args": ["run", "python", "-m", "zentao_mcp.main"],
      "env": {
        "MCP_TOKEN": "your-secret-token",
        "MCP_TRANSPORT": "stdio",
        "LOG_LEVEL": "INFO",
        "ENVIRONMENT": "beta"
      },
      "disabled": false,
      "autoApprove": ["batch_query_stories", "validate_story_existence", ...]
    }
  }
}
```

### 2. 更新配置

请修改 `.kiro/settings/mcp.json` 中的以下字段：

### 3. 重启Kiro MCP服务

在Kiro中：
1. 打开命令面板 (Cmd/Ctrl + Shift + P)
2. 搜索 "MCP" 相关命令
3. 重启或重新连接MCP服务器

## 📋 可用功能

### 🔧 MCP工具 (10个)

1. **batch_query_stories** - 批量查询需求信息
2. **validate_story_existence** - 验证需求是否存在
3. **analyze_story_workload** - 分析需求工时统计
4. **query_bugs_by_time_range** - 根据时间范围查询Bug列表
5. **analyze_bugs_by_dept_and_time** - 按部门和时间段统计Bug分析
6. **filter_bugs_by_criteria** - 根据多种条件过滤Bug
7. **project_summary_analysis** - 项目整体统计工具
8. **story_task_relation_query** - 需求-任务关联查询工具
9. **bug_to_story_tracking** - Bug转需求的追踪工具
10. **personnel_workload_analysis** - 人员工作量统计和分析

### 📖 MCP资源 (26个)

#### 部门管理
- `zentao://departments/all` - 获取所有部门列表
- `zentao://department/{dept_id}` - 获取指定部门详情
- `zentao://departments/by-parent/{parent_id}` - 根据父级部门ID获取子部门列表
- `zentao://departments/by-grade/{grade}` - 根据层级获取部门列表

#### 用户管理
- `zentao://users/all` - 获取所有用户列表
- `zentao://users/by-department/{dept_id}` - 根据部门ID查询用户列表
- `zentao://user/{user_account}` - 根据用户账号获取用户信息

#### 项目管理
- `zentao://projects/all` - 获取所有项目列表
- `zentao://project/{project_id}` - 获取指定项目详情
- `zentao://projects/by-status/{status}` - 根据项目状态获取项目列表
- `zentao://projects/status/active` - 获取活跃项目列表
- `zentao://projects/summary/all` - 获取项目统计摘要
- `zentao://projects/by-parent/{parent_id}` - 根据父级项目ID获取子项目列表

#### 需求管理
- `zentao://stories/all` - 获取所有需求列表
- `zentao://story/{story_id}` - 获取指定需求详情
- `zentao://project/{project_id}/stories` - 根据项目ID获取需求列表
- `zentao://stories/by-status/{status}` - 根据需求状态获取需求列表
- `zentao://stories/by-stage/{stage}` - 根据需求阶段获取需求列表
- `zentao://stories/by-priority/{priority}` - 根据优先级获取需求列表
- `zentao://stories/workload-summary/all` - 获取需求工时统计摘要

#### 任务管理
- `zentao://tasks/all` - 获取所有任务列表
- `zentao://task/{task_id}` - 获取指定任务详情
- `zentao://project/{project_id}/tasks` - 根据项目ID获取任务列表
- `zentao://story/{story_id}/tasks` - 根据需求ID获取关联任务列表
- `zentao://tasks/by-status/{status}` - 根据任务状态获取任务列表
- `zentao://tasks/by-type/{task_type}` - 根据任务类型获取任务列表
- `zentao://tasks/by-assignee/{assignee}` - 根据指派人获取任务列表
- `zentao://tasks/by-priority/{priority}` - 根据优先级获取任务列表
- `zentao://tasks/status/active` - 获取活跃任务列表
- `zentao://tasks/workload-summary/all` - 获取任务工时统计摘要

#### Bug管理
- `zentao://bug/{bug_id}` - 获取指定Bug详情
- `zentao://bugs/project/{project_id}` - 根据项目ID获取Bug列表
- `zentao://bugs/time-range/{start_date}/{end_date}` - 根据时间范围获取Bug列表
- `zentao://bugs/time-range-dept/{start_date}/{end_date}/{dept_id}` - 根据时间范围和部门获取Bug列表
- `zentao://bugs/by-status/{status}` - 根据Bug状态获取Bug列表
- `zentao://bugs/by-severity/{severity}` - 根据Bug严重程度获取Bug列表

## 💬 在Kiro中的使用示例

### 基础查询

**用户问题**: "获取所有部门信息"
**Kiro会自动**: 调用资源 `zentao://departments/all`

**用户问题**: "查询项目1001的详细信息"
**Kiro会自动**: 调用资源 `zentao://project/1001`

### 数据分析

**用户问题**: "分析需求1001,1002的工时统计"
**Kiro会自动**: 调用工具 `analyze_story_workload({'story_ids': ['1001', '1002']})`

**用户问题**: "查询2024年1月到2月的Bug列表"
**Kiro会自动**: 调用工具 `query_bugs_by_time_range({'start_date': '2024-01-01', 'end_date': '2024-02-29'})`

### 项目管理

**用户问题**: "获取项目1001的统计分析"
**Kiro会自动**: 调用工具 `project_summary_analysis({'project_id': 1001})`

**用户问题**: "查看技术部门的人员工作量"
**Kiro会自动**: 调用工具 `personnel_workload_analysis({'dept_id': 2})`

### 复杂查询

**用户问题**: "帮我分析一下最近一个月严重级别的Bug分布情况"
**Kiro会自动**: 
1. 调用 `query_bugs_by_time_range` 获取最近一个月的Bug
2. 调用 `filter_bugs_by_criteria` 按严重程度过滤
3. 生成分析报告

## 🔧 故障排除

### 1. MCP服务器无法启动

检查以下项目：
- 确保 `uv` 已安装并可用
- 确保项目依赖已安装: `uv sync`
- 检查 `.kiro/settings/mcp.json` 配置是否正确
- 查看Kiro的MCP服务器日志

### 2. 禅道API连接失败

检查以下配置：
- 网络连接: 确保可以访问禅道服务器

### 3. 工具调用失败

可能的原因：
- 参数格式不正确
- 禅道API返回错误
- 权限不足

查看日志文件 `logs/zentao_mcp.log` 获取详细错误信息。

## 📝 开发和调试

### 本地测试

```bash
# 测试MCP服务器
uv run python scripts/debug/debug_server.py test

# 测试Kiro集成
uv run python scripts/integration/test_kiro_integration.py

# 交互式调试
uv run python scripts/debug/debug_server.py
```

### 日志查看

- MCP服务器日志: `logs/zentao_mcp.log`
- 调试日志: `debug_mcp.log`
- Kiro MCP日志: 在Kiro的MCP面板中查看

## 🎯 最佳实践

1. **明确的问题描述**: 在Kiro中提问时，尽量明确具体的需求
2. **使用具体的ID**: 提供具体的项目ID、需求ID等，而不是模糊的描述
3. **时间范围查询**: 使用标准的日期格式 (YYYY-MM-DD)
4. **批量操作**: 对于多个相关项目，可以一次性查询多个ID

## 🔄 更新和维护

### 更新MCP服务

```bash
# 更新依赖
uv sync

# 重启MCP服务器
# 在Kiro中重新连接MCP服务器
```

### 配置更新

修改 `.kiro/settings/mcp.json` 后，需要在Kiro中重启MCP服务器。

---

现在你可以在Kiro中直接询问关于禅道项目管理的问题，Kiro会自动调用相应的MCP工具和资源来为你提供答案！🚀