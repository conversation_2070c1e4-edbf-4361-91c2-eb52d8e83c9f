# 需求管理功能实现文档

## 概述

本文档描述了禅道MCP服务中需求管理功能的实现，包括需求资源和工具的详细说明。

## 实现的功能

### 需求资源 (Story Resources)

需求资源提供了访问禅道系统中需求数据的标准化接口。

#### 资源列表

1. **zentao://stories** - 获取所有需求列表
   - 通过遍历所有项目聚合需求数据
   - 返回包含项目信息的完整需求列表

2. **zentao://story/{story_id}** - 获取指定需求详情
   - 支持获取需求的完整信息，包括规格说明
   - 包含工时估算和状态信息

3. **zentao://project/{project_id}/stories** - 获取项目需求列表
   - 根据项目ID获取该项目下的所有需求
   - 支持项目维度的需求管理

4. **zentao://stories/by-status/{status}** - 根据状态获取需求
   - 支持的状态：active（活跃）、closed（已关闭）
   - 便于按需求状态进行筛选

5. **zentao://stories/by-stage/{stage}** - 根据阶段获取需求
   - 支持的阶段：wait（等待）、planned（已计划）、developing（开发中）、released（已发布）
   - 支持需求生命周期管理

6. **zentao://stories/by-priority/{priority}** - 根据优先级获取需求
   - 支持优先级：1（高）、2（中）、3（普通）、4（低）
   - 便于优先级管理

7. **zentao://stories/workload-summary** - 获取需求工时汇总
   - 提供完整的工时统计分析
   - 包含状态、阶段、优先级分布统计

### 需求工具 (Story Tools)

需求工具提供了复杂的数据查询和分析功能。

#### 工具列表

1. **batch_query_stories** - 批量查询需求信息
   - 参数：story_ids (List[str]) - 需求ID列表
   - 返回：包含成功和失败统计的详细结果
   - 支持大批量需求数据查询

2. **validate_story_existence** - 验证需求存在性
   - 参数：story_ids (List[str]) - 需求ID列表
   - 返回：存在性验证结果，包含存在率统计
   - 用于数据完整性检查

3. **analyze_story_workload** - 分析需求工时统计
   - 参数：
     - story_ids (可选) - 指定需求ID列表
     - project_id (可选) - 指定项目ID
     - include_completed (默认true) - 是否包含已完成需求
   - 返回：详细的工时分析报告
   - 支持多维度工时分析

## 数据结构

### 需求数据模型

```python
{
    "id": int,                    # 需求ID
    "title": str,                 # 需求标题
    "product": int,               # 产品ID
    "productname": str,           # 产品名称
    "module": int,                # 模块ID
    "modulename": str,            # 模块名称
    "pri": int,                   # 优先级 (1-4)
    "estimate": float,            # 工时估算
    "status": str,                # 状态 (active/closed)
    "stage": str,                 # 阶段 (wait/planned/developing/released)
    "openedBy": str,              # 创建人
    "openedByEmpid": str,         # 创建人工号
    "assignedTo": str,            # 指派给
    "assignedToEmpid": str,       # 指派人工号
    "spec": str,                  # 需求规格说明
    "project_id": int,            # 关联项目ID (仅在项目需求中)
    "project_name": str           # 关联项目名称 (仅在项目需求中)
}
```

### 工时分析结果

```python
{
    "success": bool,
    "message": str,
    "total_stories": int,
    "analysis": {
        "total_estimate_hours": float,      # 总估算工时
        "completed_estimate_hours": float,  # 已完成估算工时
        "active_estimate_hours": float,     # 活跃需求工时
        "completed_actual_hours": float,    # 实际完成工时
        "average_estimate_hours": float,    # 平均估算工时
        "completion_rate": float,           # 完成率
        "status_breakdown": {...},          # 状态分布
        "stage_breakdown": {...},           # 阶段分布
        "priority_breakdown": {...}         # 优先级分布
    }
}
```

## 错误处理

- **ResourceNotFoundError**: 需求不存在时抛出
- **ValidationError**: 参数验证失败时抛出
- **ZentaoAPIError**: 禅道API调用失败时抛出

## 使用示例

### 获取需求详情
```python
# 通过MCP资源获取
story = await mcp.get_resource("zentao://story/123")
```

### 批量查询需求
```python
# 通过MCP工具调用
result = await mcp.call_tool("batch_query_stories", {
    "story_ids": ["123", "124", "125"]
})
```

### 工时分析
```python
# 分析指定项目的需求工时
result = await mcp.call_tool("analyze_story_workload", {
    "project_id": 1,
    "include_completed": True
})
```

## 测试覆盖

实现包含完整的单元测试，覆盖：
- 资源注册验证
- 成功场景测试
- 错误场景测试
- 数据格式验证
- API调用验证

测试文件：`tests/test_story_resources.py`

## 集成说明

需求功能已集成到主服务器中：
- 资源通过 `register_story_resources()` 注册
- 工具通过 `register_story_tools()` 注册
- 支持异步操作和错误处理
- 与现有的项目、部门、用户功能协同工作