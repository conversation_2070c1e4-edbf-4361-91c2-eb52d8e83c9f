#!/bin/bash

echo "🚀 Zentao MCP Backend Service 一键启动"
echo "======================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查函数
check_step() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ $1${NC}"
        return 0
    else
        echo -e "${RED}❌ $1${NC}"
        return 1
    fi
}

# 1. 检查Python版本
echo -e "${BLUE}🔍 检查Python版本...${NC}"
python_version=$(python --version 2>&1)
echo "   $python_version"
if python -c "import sys; exit(0 if sys.version_info >= (3, 10) else 1)" 2>/dev/null; then
    check_step "Python版本检查"
else
    echo -e "${RED}❌ Python版本过低，需要3.10+${NC}"
    exit 1
fi

# 2. 检查虚拟环境
echo -e "${BLUE}🔍 检查虚拟环境...${NC}"
if [ -d ".venv" ]; then
    check_step "虚拟环境存在"
else
    echo -e "${RED}❌ 虚拟环境不存在${NC}"
    echo -e "${YELLOW}💡 请先运行: uv venv${NC}"
    exit 1
fi

# 3. 检查项目目录
echo -e "${BLUE}🔍 检查项目目录...${NC}"
if [ -d "zentao-mcp-backend-service" ] && [ -f "zentao-mcp-backend-service/main.py" ]; then
    check_step "项目目录结构"
else
    echo -e "${RED}❌ 项目目录或main.py不存在${NC}"
    exit 1
fi

# 4. 安装依赖
echo -e "${BLUE}📦 检查并安装依赖...${NC}"
cd zentao-mcp-backend-service
if uv pip install -e . >/dev/null 2>&1; then
    check_step "依赖安装"
else
    echo -e "${YELLOW}⚠️ 依赖安装可能有问题，继续尝试启动...${NC}"
fi

# 5. 检查关键依赖
echo -e "${BLUE}🔍 检查关键依赖...${NC}"
if ../.venv/bin/python -c "import fastapi, uvicorn" 2>/dev/null; then
    check_step "关键依赖检查"
else
    echo -e "${RED}❌ 关键依赖缺失${NC}"
    echo -e "${YELLOW}💡 尝试手动安装: uv pip install fastapi uvicorn${NC}"
    exit 1
fi

# 6. 启动服务
echo -e "${BLUE}🚀 启动服务...${NC}"
echo -e "${YELLOW}📋 使用简化启动脚本...${NC}"

if [ -f "simple_start.py" ]; then
    echo -e "${GREEN}✅ 找到简化启动脚本${NC}"
    echo -e "${BLUE}🎯 正在启动服务，请稍候...${NC}"
    echo ""
    echo "========================================"
    echo "🌟 服务启动中，请查看下方输出"
    echo "🌐 服务地址: http://localhost:8000"
    echo "📚 API文档: http://localhost:8000/docs"
    echo "🛑 停止服务: 按 Ctrl+C"
    echo "========================================"
    echo ""
    
    python simple_start.py
else
    echo -e "${YELLOW}⚠️ 简化启动脚本不存在，使用标准方式...${NC}"
    ../.venv/bin/python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
fi