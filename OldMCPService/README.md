# 禅道 MCP 服务器

## 项目描述

这是一个基于 FastMCP 框架的禅道(Zentao)项目管理系统 MCP 服务器，主要功能是使用 Model Context Protocol (MCP) 协议为 AI Agent 提供禅道数据查询和分析功能。

## 项目结构

```
zentao-mcp-server/
├── zentao_mcp/                     # 核心MCP服务模块
│   ├── __init__.py
│   ├── main.py                     # FastMCP应用入口
│   ├── config.py                   # 配置管理
│   ├── client.py                   # 禅道API客户端
│   ├── auth.py                     # 认证和会话管理
│   ├── cache.py                    # 缓存系统
│   ├── models.py                   # 数据模型定义
│   ├── exceptions.py               # 异常处理
│   ├── error_handler.py            # 错误处理器
│   ├── logging_config.py           # 日志配置
│   ├── health_check.py             # 健康检查
│   ├── tools/                      # MCP工具实现
│   │   ├── __init__.py
│   │   ├── client_utils.py         # 客户端工具
│   │   ├── direct/                 # 直接接口层工具
│   │   │   ├── __init__.py
│   │   │   ├── bug.py              # Bug直接接口工具
│   │   │   ├── department.py       # 部门直接接口工具
│   │   │   ├── project.py          # 项目直接接口工具
│   │   │   ├── story.py            # 需求直接接口工具
│   │   │   ├── task.py             # 任务直接接口工具
│   │   │   └── user.py             # 用户直接接口工具
│   │   ├── analysis/               # 分析增强层工具
│   │   │   ├── __init__.py
│   │   │   ├── bug.py              # Bug分析工具
│   │   │   ├── project.py          # 项目分析工具
│   │   │   └── story.py            # 需求分析工具
│   │   └── system/                 # 系统工具
│   │       ├── __init__.py
│   │       └── health.py           # 健康检查工具
│   └── resources/                  # MCP资源实现
│       ├── __init__.py
│       ├── bug.py                  # Bug资源
│       ├── department.py           # 部门资源
│       ├── project.py              # 项目资源
│       ├── story.py                # 需求资源
│       ├── task.py                 # 任务资源
│       └── user.py                 # 用户资源
├── tests/                          # 测试文件
│   ├── __init__.py
│   ├── conftest.py                 # pytest配置
│   ├── README.md                   # 测试说明文档
│   ├── core/                       # 核心功能测试
│   │   ├── test_auth.py            # 认证测试
│   │   ├── test_basic_functionality.py # 基础功能测试
│   │   ├── test_cache.py           # 缓存测试
│   │   ├── test_config_auth.py     # 配置认证测试
│   │   └── test_error_handling.py  # 错误处理测试
│   ├── api/                        # API接口测试
│   │   ├── test_bug_api.py         # Bug API测试
│   │   ├── test_department_api.py  # 部门API测试
│   │   ├── test_project_api.py     # 项目API测试
│   │   ├── test_story_api.py       # 需求API测试
│   │   └── test_task_api.py        # 任务API测试
│   ├── mcp/                        # MCP功能测试
│   │   ├── tools/                  # MCP工具测试
│   │   │   ├── test_analysis_tools.py # 分析工具测试
│   │   │   ├── test_bug_tools.py   # Bug工具测试
│   │   │   ├── test_direct_tools.py # 直接工具测试
│   │   │   └── test_project_tools.py # 项目工具测试
│   │   └── resources/              # MCP资源测试
│   │       ├── test_bug_resources.py # Bug资源测试
│   │       ├── test_project_resources.py # 项目资源测试
│   │       ├── test_story_resources.py # 需求资源测试
│   │       └── test_task_resources.py # 任务资源测试
│   ├── integration/                # 集成测试
│   │   ├── test_client_cache_integration.py # 客户端缓存集成测试
│   │   ├── test_integration.py     # 系统集成测试
│   │   └── test_mcp_client.py      # MCP客户端测试
│   └── system/                     # 系统测试
│       ├── test_connectivity_runner.py # 连通性测试
│       ├── test_health_check.py    # 健康检查测试
│       └── test_performance.py     # 性能测试
├── dev/                            # 开发调试工具
│   ├── README.md                   # 开发工具说明
│   └── debug_server.py             # 交互式调试服务器
├── docs/                           # 项目文档
│   ├── ANALYSIS_TOOLS_IMPLEMENTATION.md # 分析工具实现文档
│   ├── CACHE_PERFORMANCE_IMPLEMENTATION.md # 缓存性能实现文档
│   ├── CONFIG_AUTH_IMPLEMENTATION.md # 配置认证实现文档
│   ├── ERROR_HANDLING_LOGGING_IMPLEMENTATION.md # 错误处理日志实现文档
│   ├── HTTP_CONNECTIVITY_TESTS.md  # HTTP连通性测试文档
│   ├── KIRO_INTEGRATION.md         # Kiro集成文档
│   ├── REFACTOR_SUMMARY.md         # 重构总结文档
│   ├── RESOURCE_OPTIMIZATION_SUMMARY.md # 资源优化总结文档
│   ├── story_implementation.md     # 需求实现文档
│   └── TEST_SUMMARY.md             # 测试总结文档
├── doc/                            # API文档
│   ├── API_MAPPING.md              # API映射文档
│   ├── PRD.md                      # 产品需求文档
│   └── erd/                        # 数据库设计文档
├── .env.example                    # 环境变量示例
├── .gitignore                      # Git忽略文件
├── .python-version                 # Python版本
├── fixed_mcp_config.json           # MCP配置文件
├── pyproject.toml                  # 项目配置
├── uv.lock                         # 依赖锁定文件
└── README.md                       # 项目说明
```

## 技术栈

- **MCP框架**: FastMCP >= 2.11.3
- **HTTP客户端**: httpx (异步)
- **数据验证**: Pydantic >= 2.0.0
- **配置管理**: pydantic-settings + python-dotenv
- **依赖管理**: uv + pyproject.toml
- **测试框架**: pytest + pytest-asyncio
- **Python版本**: >= 3.10

## 功能特性

### 🔧 核心架构

#### 1. MCP服务器 (FastMCP)
- **协议支持**: 完整的Model Context Protocol实现
- **传输模式**: 支持stdio和HTTP两种传输方式
- **生命周期管理**: 优雅的启动和关闭流程
- **信号处理**: 支持SIGINT和SIGTERM信号处理

#### 2. 双层工具架构
- **直接接口层**: 封装禅道原始API，保持数据结构不变
- **分析增强层**: 提供数据聚合、统计分析和业务逻辑处理
- **系统工具层**: 健康检查、性能监控和系统管理

#### 3. 资源管理系统
- **结构化访问**: 通过URI模式访问禅道数据
- **缓存优化**: 智能缓存策略，提升访问性能
- **关联查询**: 支持实体间的关联数据查询

### 📊 数据管理

#### 1. 禅道API客户端
- **异步支持**: 基于httpx的异步HTTP客户端
- **连接池**: 高效的连接池管理
- **重试机制**: 指数退避重试策略
- **错误处理**: 完整的异常处理体系

#### 2. 缓存系统
- **多级缓存**: 内存缓存 + 可扩展的外部缓存
- **缓存策略**: TTL过期 + LRU淘汰
- **缓存键管理**: 统一的缓存键命名规范
- **性能监控**: 缓存命中率和性能指标

#### 3. 数据模型
- **类型安全**: 基于Pydantic的强类型数据模型
- **数据验证**: 输入输出数据的完整性验证
- **字段映射**: 中英文字段映射，便于AI理解
- **向后兼容**: 保持与原始API的兼容性

### 🔐 安全与认证

#### 1. 认证系统
- **Token认证**: 支持Bearer Token认证
- **会话管理**: 禅道系统会话管理
- **权限控制**: 基于用户权限的访问控制
- **配置化**: 可通过配置开启/关闭认证

#### 2. 错误处理
- **异常分类**: 网络、认证、业务逻辑异常分类
- **错误追踪**: 完整的错误堆栈和上下文信息
- **安全日志**: 避免敏感信息泄露的安全日志记录

### 📈 监控与运维

#### 1. 日志系统
- **结构化日志**: JSON格式日志，便于分析
- **日志级别**: 支持DEBUG、INFO、WARNING、ERROR级别
- **请求追踪**: 每个请求的唯一ID追踪
- **性能日志**: API响应时间和性能指标记录

#### 2. 健康检查
- **服务状态**: 实时服务健康状态检查
- **依赖检查**: 禅道API连通性检查
- **资源监控**: 内存、缓存等资源使用监控
- **指标收集**: 性能指标的收集和统计

#### 3. 性能优化
- **并发控制**: 可配置的并发请求限制
- **连接复用**: HTTP连接池和Keep-Alive
- **数据压缩**: 支持gzip压缩传输
- **分页优化**: 大数据集的分页查询优化

### 🛠️ 开发工具

#### 1. 调试工具
- **交互式调试**: 命令行交互式调试界面
- **API测试**: 内置的API连通性测试
- **MCP测试**: MCP协议功能测试
- **性能分析**: 请求性能分析工具

#### 2. 测试框架
- **单元测试**: 完整的单元测试覆盖
- **集成测试**: 与禅道系统的集成测试
- **性能测试**: API性能和压力测试
- **覆盖率报告**: 测试覆盖率统计和报告

## 快速开始

### 1. 环境准备

确保已安装 Python 3.10+ 和 uv 包管理器：

```bash
# 安装uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# 或使用pip安装
pip install uv
```

### 2. 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd zentao-mcp-server

# 安装依赖
uv sync
```

### 3. 配置环境变量

复制并编辑环境配置文件：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置禅道连接信息：

```env
# 环境配置
ENVIRONMENT=beta  # beta/online

# 禅道域名配置
BETA_DOMAIN=your-beta-zentao-domain.com
ONLINE_DOMAIN=your-online-zentao-domain.com

# MCP服务配置
MCP_SERVER_NAME=ZentaoMCP
MCP_TRANSPORT=stdio

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs

# 缓存配置
CACHE_ENABLED=true
CACHE_TTL=300

# 性能配置
REQUEST_TIMEOUT=30
MAX_CONCURRENT_REQUESTS=10
```

### 4. 启动服务

```bash
# 使用uv运行（推荐）
uv run python -m zentao_mcp.main

# 或使用已安装的脚本
zentao-mcp

# 指定传输模式
uv run python -m zentao_mcp.main --transport stdio
uv run python -m zentao_mcp.main --transport http --port 8000
```

### 5. 验证安装

```bash
# 运行基础测试
uv run python -m pytest tests/core/test_basic_functionality.py -v

# 运行API连通性测试
uv run python dev/debug_server.py test

# 运行完整测试套件
uv run python -m pytest tests/ -v
```

## 🧪 测试和调试

### 测试框架

项目提供了完整的测试框架，详细说明请参考 [tests/README.md](tests/README.md)：

```bash
# 运行所有测试
uv run python -m pytest tests/

# 按模块运行测试
uv run python -m pytest tests/core/      # 核心功能测试
uv run python -m pytest tests/api/       # API接口测试
uv run python -m pytest tests/mcp/       # MCP功能测试
uv run python -m pytest tests/integration/ # 集成测试
uv run python -m pytest tests/system/    # 系统测试

# 生成覆盖率报告
uv run python -m pytest tests/ --cov=zentao_mcp --cov-report=html
```

### 开发调试

项目提供了交互式调试工具，详细说明请参考 [dev/README.md](dev/README.md)：

```bash
# 启动交互式调试
uv run python dev/debug_server.py

# 在交互界面中使用命令
> test      # 测试基础API连接
> client    # 测试禅道客户端功能
> mcp       # 启动MCP服务器
> help      # 显示帮助信息
> exit      # 退出程序
```

## 📚 API 使用

### MCP工具调用

服务器提供了丰富的MCP工具，分为三个层次：

#### 1. 直接接口层工具 (zentao_*)
直接封装禅道API，保持原始数据结构：

```python
# 获取所有部门
zentao_get_all_departments()

# 根据部门ID获取用户
zentao_get_users_by_dept(dept_id=1)

# 获取所有项目
zentao_get_all_projects()

# 根据项目ID获取任务
zentao_get_project_tasks(project_id=1)
```

#### 2. 分析增强层工具 (mcp_*)
提供数据聚合和分析功能：

```python
# 分析需求工时统计
mcp_analyze_story_workload(story_ids=["1", "2"], include_completed=True)

# 按部门和时间统计Bug
mcp_analyze_bugs_by_dept_and_time(
    start_date="2024-01-01 00:00:00",
    end_date="2024-12-31 23:59:59",
    dept_id=1
)

# 项目整体统计
mcp_project_summary_analysis(project_id=1)
```

#### 3. 系统工具 (mcp_*)
系统管理和监控功能：

```python
# 获取服务健康状态
mcp_get_health_status()

# 获取性能指标
mcp_get_performance_metrics()

# 重置性能指标
mcp_reset_performance_metrics()
```

### MCP资源访问

通过URI模式访问结构化数据：

```python
# 部门相关资源
zentao://departments                    # 所有部门列表
zentao://department/{dept_id}/users     # 部门用户列表

# 项目相关资源
zentao://projects                       # 所有项目列表
zentao://project/{project_id}           # 项目详情
zentao://project/{project_id}/tasks     # 项目任务列表
zentao://project/{project_id}/bugs      # 项目Bug列表

# 需求相关资源
zentao://story/{story_id}               # 需求详情

# 任务相关资源
zentao://task/{task_id}                 # 任务详情

# Bug相关资源
zentao://bug/{bug_id}                   # Bug详情
```

### 认证

如果启用了认证，所有请求需要在MCP上下文中包含认证信息。

## 📖 文档

- [API映射文档](doc/API_MAPPING.md) - 禅道API接口映射说明
- [产品需求文档](doc/PRD.md) - 项目需求和设计说明
- [测试文档](tests/README.md) - 测试框架和用例说明
- [开发工具文档](dev/README.md) - 开发调试工具使用说明
- [重构总结](docs/REFACTOR_SUMMARY.md) - 项目重构过程总结

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `ENVIRONMENT` | 运行环境 | `beta` | `beta`/`online` |
| `BETA_DOMAIN` | 测试环境域名 | - | `beta-zentao.company.com` |
| `ONLINE_DOMAIN` | 生产环境域名 | - | `zentao.company.com` |
| `MCP_SERVER_NAME` | MCP服务名称 | `ZentaoMCP` | `ZentaoMCP` |
| `MCP_TRANSPORT` | 传输协议 | `stdio` | `stdio`/`http` |
| `LOG_LEVEL` | 日志级别 | `INFO` | `DEBUG`/`INFO`/`WARNING`/`ERROR` |
| `LOG_DIR` | 日志目录 | `logs` | `logs` |
| `CACHE_ENABLED` | 启用缓存 | `true` | `true`/`false` |
| `CACHE_TTL` | 缓存过期时间(秒) | `300` | `300` |
| `REQUEST_TIMEOUT` | 请求超时时间(秒) | `30` | `30` |
| `MAX_CONCURRENT_REQUESTS` | 最大并发请求数 | `10` | `10` |

### 功能开关

通过环境变量控制功能开关：

```env
# 认证开关
AUTH_ENABLED=true

# 缓存开关
CACHE_ENABLED=true

# 重试开关
RETRY_ENABLED=true

# 性能监控开关
PERFORMANCE_MONITORING=true
```

## 🚀 部署

### 开发环境

```bash
# 启动开发服务器
uv run python -m zentao_mcp.main --transport stdio

# 或使用调试模式
uv run python dev/debug_server.py
```

### 生产环境

```bash
# 设置生产环境变量
export ENVIRONMENT=online
export LOG_LEVEL=WARNING
export CACHE_ENABLED=true

# 启动服务
uv run python -m zentao_mcp.main --transport stdio
```

### Docker部署

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY . .

RUN pip install uv && uv sync

CMD ["uv", "run", "python", "-m", "zentao_mcp.main"]
```

## 🤝 贡献

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接和防火墙设置
   - 验证禅道域名配置
   - 查看日志文件：`tail -f logs/zentao_mcp.log`

2. **认证错误**
   - 检查用户名密码配置
   - 验证用户权限
   - 查看认证相关日志

3. **性能问题**
   - 调整缓存配置
   - 优化并发参数
   - 监控资源使用情况

### 获取帮助

- 查看项目文档和API说明
- 使用调试工具进行问题诊断
- 检查日志文件中的错误信息
- 提交Issue或联系开发团队

## 📊 项目统计

- **代码行数**: ~15,000+ 行
- **测试覆盖率**: 85%+
- **支持的API**: 30+ 个禅道API接口
- **MCP工具**: 25+ 个工具
- **MCP资源**: 15+ 个资源类型
- **最后更新**: 2025年8月27日