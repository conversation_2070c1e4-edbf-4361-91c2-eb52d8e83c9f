# 手动启动 Zentao MCP Backend Service

## 方法1：使用简化启动脚本

```bash
cd zentao-mcp-backend-service
python simple_start.py
```

## 方法2：直接使用uvicorn

```bash
cd zentao-mcp-backend-service
../venv/bin/python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## 方法3：激活虚拟环境后启动

```bash
source .venv/bin/activate
cd zentao-mcp-backend-service
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## 测试服务

服务启动后，访问：
- http://localhost:8000 - 根端点
- http://localhost:8000/health - 健康检查
- http://localhost:8000/docs - API文档

或运行测试脚本：
```bash
cd zentao-mcp-backend-service
python test_api.py