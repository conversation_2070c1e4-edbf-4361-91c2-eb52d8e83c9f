# 🚀 Zentao MCP Backend Service 详细启动指南

## 📋 启动前检查清单

### 1. 环境检查
```bash
# 检查Python版本（需要3.10+）
python --version

# 检查虚拟环境
ls -la .venv/

# 检查项目结构
ls -la zentao-mcp-backend-service/
```

### 2. 依赖检查
```bash
# 激活虚拟环境
source .venv/bin/activate

# 检查关键依赖
python -c "import fastapi; print('FastAPI:', fastapi.__version__)"
python -c "import uvicorn; print('Uvicorn:', uvicorn.__version__)"
```

## 🚀 启动方法（按优先级）

### 方法1：简化启动脚本（推荐）
```bash
cd zentao-mcp-backend-service
python simple_start.py
```

**优点：**
- 逐步显示导入状态
- 自动跳过有问题的模块
- 容错处理
- 详细的错误信息

### 方法2：直接使用uvicorn
```bash
cd zentao-mcp-backend-service
../.venv/bin/python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### 方法3：激活虚拟环境后启动
```bash
source .venv/bin/activate
cd zentao-mcp-backend-service
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### 方法4：使用完整路径
```bash
/Users/<USER>/SourceCode/Git/feiniu/Other/zentaomcpserver/.venv/bin/python -m uvicorn zentao-mcp-backend-service/main:app --host 0.0.0.0 --port 8000
```

## 🔍 启动状态检查

### 成功启动的标志
```
INFO:     Will watch for changes in these directories: ['/path/to/zentao-mcp-backend-service']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [xxxxx] using StatReload
INFO:     Started server process [xxxxx]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
```

### 快速验证
```bash
# 新开终端，运行快速测试
cd zentao-mcp-backend-service
python quick_test.py

# 或者使用curl
curl http://localhost:8000/health

# 或者浏览器访问
open http://localhost:8000/docs
```

## ❌ 常见错误及解决方案

### 错误1: "Could not import module 'main'"
**原因：** 工作目录不正确
**解决：**
```bash
# 确保在正确目录
cd zentao-mcp-backend-service
pwd  # 应该显示 .../zentaomcpserver/zentao-mcp-backend-service
```

### 错误2: "ModuleNotFoundError: No module named 'app'"
**原因：** Python路径问题
**解决：**
```bash
# 方法1：使用简化启动脚本
python simple_start.py

# 方法2：设置PYTHONPATH
export PYTHONPATH=$PWD:$PYTHONPATH
python -m uvicorn main:app --host 0.0.0.0 --port 8000
```

### 错误3: "ModuleNotFoundError: No module named 'fastapi'"
**原因：** 依赖未安装或虚拟环境未激活
**解决：**
```bash
# 重新安装依赖
cd zentao-mcp-backend-service
uv pip install -e .

# 或者手动安装关键依赖
uv pip install fastapi uvicorn sqlalchemy
```

### 错误4: "Address already in use"
**原因：** 端口8000被占用
**解决：**
```bash
# 查找占用端口的进程
lsof -i :8000

# 杀死进程（替换PID）
kill -9 <PID>

# 或者使用其他端口
python -m uvicorn main:app --host 0.0.0.0 --port 8001
```

### 错误5: 导入特定模块失败
**原因：** 某些业务模块有问题
**解决：** 使用简化启动脚本，它会跳过有问题的模块

## 🧪 测试服务

### 1. 基础连接测试
```bash
python quick_test.py
```

### 2. 完整API测试
```bash
python test_api.py
```

### 3. 手动测试端点
```bash
# 健康检查
curl http://localhost:8000/health

# 根端点
curl http://localhost:8000/

# API文档（浏览器）
open http://localhost:8000/docs
```

## 🔧 调试模式

### 启用详细日志
```bash
cd zentao-mcp-backend-service
python -c "
import logging
logging.basicConfig(level=logging.DEBUG)
from main import app
import uvicorn
uvicorn.run(app, host='0.0.0.0', port=8000, log_level='debug')
"
```

### 检查导入问题
```bash
cd zentao-mcp-backend-service
python -c "
import sys
sys.path.insert(0, '.')
try:
    from main import app
    print('✅ main.py导入成功')
except Exception as e:
    print(f'❌ main.py导入失败: {e}')
    import traceback
    traceback.print_exc()
"
```

## 📊 性能监控

### 启动时间监控
```bash
time python simple_start.py
```

### 内存使用监控
```bash
# 启动服务后在另一个终端
ps aux | grep uvicorn
```

## 🛑 停止服务

### 正常停止
在服务运行的终端按 `Ctrl+C`

### 强制停止
```bash
# 查找进程
ps aux | grep uvicorn

# 强制杀死
kill -9 <PID>
```

## 📝 日志文件

服务运行时的日志会显示在终端。如需保存日志：

```bash
cd zentao-mcp-backend-service
python simple_start.py 2>&1 | tee service.log
```

## 🆘 获取帮助

如果以上方法都无法解决问题，请提供：

1. 错误信息的完整输出
2. Python版本 (`python --version`)
3. 操作系统信息
4. 虚拟环境状态 (`which python`)
5. 项目目录结构 (`ls -la`)

---

## 🎯 快速启动命令总结

```bash
# 最简单的启动方式
cd zentao-mcp-backend-service && python simple_start.py

# 测试服务
cd zentao-mcp-backend-service && python quick_test.py

# 访问API文档
open http://localhost:8000/docs