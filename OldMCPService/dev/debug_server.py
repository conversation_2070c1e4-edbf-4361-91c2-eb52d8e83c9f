#!/usr/bin/env python3
"""
禅道MCP服务调试启动脚本
用于本地开发和调试MCP服务
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from zentao_mcp.main import ZentaoMCPServer
from zentao_mcp.config import settings
from zentao_mcp.exceptions import ZentaoMCPError


def setup_debug_logging():
    """设置调试日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('debug_mcp.log')
        ]
    )
    
    # 设置特定模块的日志级别
    logging.getLogger('httpx').setLevel(logging.INFO)
    logging.getLogger('httpcore').setLevel(logging.INFO)


def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查.env文件
    env_file = Path('.env')
    if not env_file.exists():
        print("⚠️  .env文件不存在，请复制.env.example并配置")
        return False
    
    # 检查必要的配置
    required_configs = []
    missing_configs = []
    
    for config in required_configs:
        if not getattr(settings, config.lower(), None):
            missing_configs.append(config)
    
    if missing_configs:
        print(f"❌ 缺少必要配置: {', '.join(missing_configs)}")
        return False
    
    print("✅ 环境配置检查通过")
    return True


def print_server_info():
    """打印服务器信息"""
    print("\n" + "="*50)
    print("🚀 禅道MCP服务器调试信息")
    print("="*50)
    print(f"服务器名称: ZentaoMCP")
    print(f"传输协议: {settings.mcp_transport}")
    print(f"禅道URL: {settings.get_zentao_base_url()}")
    print(f"环境: {settings.environment}")
    print(f"日志级别: {settings.log_level}")
    print(f"日志目录: {settings.log_dir}")
    print("="*50)


async def test_server_startup():
    """测试服务器启动"""
    print("\n🧪 测试服务器启动...")
    
    try:
        # 创建服务器实例
        server = ZentaoMCPServer("ZentaoMCP-Debug")
        
        # 测试启动流程
        await server.startup()
        print("✅ 服务器启动测试成功")
        
        # 测试关闭流程
        await server.shutdown()
        print("✅ 服务器关闭测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务器启动测试失败: {e}")
        logging.exception("服务器启动测试异常")
        return False


def run_interactive_mode():
    """交互式调试模式"""
    print("\n🎮 进入交互式调试模式")
    print("可用命令:")
    print("  1 - 测试服务器启动")
    print("  2 - 启动stdio模式服务器")
    print("  3 - 启动HTTP模式服务器")
    print("  4 - 查看配置信息")
    print("  q - 退出")
    
    while True:
        try:
            choice = input("\n请选择操作 (1-4, q): ").strip()
            
            if choice == 'q':
                print("👋 退出调试模式")
                break
            elif choice == '1':
                asyncio.run(test_server_startup())
            elif choice == '2':
                print("🚀 启动stdio模式服务器...")
                server = ZentaoMCPServer("ZentaoMCP-Debug")
                server.run(transport="stdio")
            elif choice == '3':
                print("🚀 启动HTTP模式服务器...")
                server = ZentaoMCPServer("ZentaoMCP-Debug")
                server.run(transport="http", host="127.0.0.1", port=8000)
            elif choice == '4':
                print_server_info()
            else:
                print("❌ 无效选择，请重试")
                
        except KeyboardInterrupt:
            print("\n👋 收到中断信号，退出调试模式")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")
            logging.exception("交互式操作异常")


def main():
    """主函数"""
    print("🔧 禅道MCP服务调试工具")
    
    # 设置调试日志
    setup_debug_logging()
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请修复配置后重试")
        return 1
    
    # 打印服务器信息
    print_server_info()
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        mode = sys.argv[1]
        
        if mode == "test":
            # 测试模式
            print("\n🧪 运行测试模式...")
            success = asyncio.run(test_server_startup())
            return 0 if success else 1
            
        elif mode == "stdio":
            # stdio模式
            print("\n🚀 启动stdio模式服务器...")
            server = ZentaoMCPServer("ZentaoMCP-Debug")
            server.run(transport="stdio")
            
        elif mode == "http":
            # HTTP模式
            port = int(sys.argv[2]) if len(sys.argv) > 2 else 8000
            print(f"\n🚀 启动HTTP模式服务器 (端口: {port})...")
            server = ZentaoMCPServer("ZentaoMCP-Debug")
            server.run(transport="http", host="127.0.0.1", port=port)
            
        else:
            print(f"❌ 未知模式: {mode}")
            print("可用模式: test, stdio, http")
            return 1
    else:
        # 交互式模式
        run_interactive_mode()
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 程序异常退出: {e}")
        logging.exception("程序异常")
        sys.exit(1)