# 开发调试工具目录

本目录包含禅道 MCP 服务器的开发调试工具，用于测试、调试和验证服务功能。

## 📁 目录结构

```
dev/
├── README.md           # 本说明文档
└── debug_server.py     # 交互式调试服务器
```

## 🔧 调试工具

### debug_server.py

**功能**: 交互式禅道 MCP 服务器调试工具
**用途**: 提供多种调试模式，支持直接测试禅道 API 和 MCP 功能
**特性**:
- 支持 stdio 和 HTTP 两种传输模式
- 提供交互式命令行界面
- 集成禅道 API 客户端测试
- 支持 MCP 工具和资源测试

**运行方式**:

```bash
# 交互模式（推荐）
uv run python dev/debug_server.py

# 测试模式
uv run python dev/debug_server.py test

# stdio 模式（用于 MCP 客户端连接）
uv run python dev/debug_server.py stdio

# HTTP 模式
uv run python dev/debug_server.py http [port]
```

**交互命令**:
- `test` - 运行基础 API 测试
- `mcp` - 启动 MCP 服务器
- `client` - 测试禅道客户端连接
- `help` - 显示帮助信息
- `exit` - 退出程序

## 🚀 快速开始

### 1. 环境准备

确保已配置好环境变量：

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件，设置禅道连接信息
# 主要配置项：
# ENVIRONMENT=beta|online
# BETA_DOMAIN=your-beta-domain
# ONLINE_DOMAIN=your-online-domain
```

### 2. 安装依赖

```bash
# 使用 uv 安装依赖
uv sync
```

### 3. 基础调试

```bash
# 启动交互式调试
uv run python dev/debug_server.py

# 在交互界面中输入命令进行测试
> test      # 测试基础 API 连接
> client    # 测试禅道客户端功能
> mcp       # 启动 MCP 服务器
```

## 📊 项目架构概览

### 核心模块

- **zentao_mcp/main.py** - MCP 服务器主入口，基于 FastMCP 框架
- **zentao_mcp/client.py** - 禅道 API 客户端，支持异步调用和缓存
- **zentao_mcp/config.py** - 配置管理，支持多环境配置
- **zentao_mcp/auth.py** - 认证和会话管理
- **zentao_mcp/cache.py** - 缓存系统，支持多种缓存策略

### 工具模块

- **zentao_mcp/tools/direct/** - 直接接口工具，封装禅道原始 API
- **zentao_mcp/tools/analysis/** - 分析增强工具，提供数据聚合和分析
- **zentao_mcp/tools/system/** - 系统工具，健康检查和性能监控

### 资源模块

- **zentao_mcp/resources/** - MCP 资源定义，提供结构化数据访问
  - `department.py` - 部门资源
  - `user.py` - 用户资源
  - `project.py` - 项目资源
  - `story.py` - 需求资源
  - `task.py` - 任务资源
  - `bug.py` - Bug 资源

## 🧪 测试说明

### 基础功能测试

```bash
# 测试禅道 API 连接
uv run python dev/debug_server.py test

# 测试特定功能
uv run python -c "
from zentao_mcp.client import ZentaoClient
import asyncio

async def test():
    client = ZentaoClient()
    await client.initialize()
    
    # 测试获取部门列表
    depts = await client.get_all_departments_async()
    print(f'部门数量: {len(depts)}')
    
    await client.close()

asyncio.run(test())
"
```

### MCP 功能测试

```bash
# 启动 MCP 服务器（stdio 模式）
uv run python dev/debug_server.py stdio

# 在另一个终端测试 MCP 连接
# 使用 MCP 客户端工具连接到 stdio 服务器
```

## 🔍 调试技巧

### 1. 日志查看

```bash
# 查看实时日志
tail -f logs/zentao_mcp.log

# 查看 API 请求日志
tail -f logs/zentao_api_*.log
```

### 2. 配置验证

```bash
# 验证当前配置
uv run python -c "
from zentao_mcp.config import settings
print('环境:', settings.environment)
print('API URL:', settings.get_zentao_base_url())
print('启用功能:', settings.get_enabled_features())
"
```

### 3. 缓存管理

```bash
# 清理缓存
uv run python -c "
from zentao_mcp.cache import get_cache
cache = get_cache()
cache.clear()
print('缓存已清理')
"
```

## ⚠️ 注意事项

### 环境配置

1. **域名配置**: 确保 `BETA_DOMAIN` 和 `ONLINE_DOMAIN` 正确配置
2. **网络访问**: 确保能够访问禅道服务器
3. **日志目录**: 确保 `logs/` 目录有写入权限

### 性能优化

1. **缓存策略**: 根据数据更新频率调整缓存 TTL
2. **并发控制**: 根据服务器性能调整 `max_concurrent_requests`
3. **连接池**: 根据网络环境调整 `connection_pool_size`

### 安全考虑

1. **敏感信息**: 不要在日志中记录敏感信息
2. **访问控制**: 生产环境建议启用认证
3. **网络安全**: 使用 HTTPS 连接生产环境

## 🔗 相关文档

- [项目主文档](../README.md)
- [API 映射文档](../doc/API_MAPPING.md)
- [产品需求文档](../doc/PRD.md)
- [测试文档](../tests/README.md)

## 📝 开发指南

### 添加新的调试功能

1. 在 `debug_server.py` 中添加新的命令处理函数
2. 更新交互式命令列表
3. 添加相应的帮助信息

### 扩展测试用例

1. 在相应的测试模块中添加测试函数
2. 确保测试覆盖异常情况
3. 添加性能测试和边界测试

### 配置新环境

1. 复制 `.env.example` 为新的环境配置文件
2. 更新 `config.py` 中的环境映射
3. 测试新环境的连接性

## 🆘 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 验证域名配置
   - 查看防火墙设置

2. **认证错误**
   - 检查用户名密码
   - 验证用户权限
   - 查看会话状态

3. **性能问题**
   - 检查缓存配置
   - 调整并发参数
   - 监控资源使用

### 调试步骤

1. 启动调试服务器：`uv run python dev/debug_server.py`
2. 运行基础测试：输入 `test` 命令
3. 查看详细日志：`tail -f logs/zentao_mcp.log`
4. 根据错误信息进行针对性调试

### 获取帮助

- 查看项目文档和 API 说明
- 检查日志文件中的错误信息
- 使用调试服务器的交互模式进行测试