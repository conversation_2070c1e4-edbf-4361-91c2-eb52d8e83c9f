[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "zentao-mcp-server"
version = "0.1.0"
description = "A FastMCP server for accessing Zentao data via Model Context Protocol"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "fastmcp>=2.11.3",
    "httpx",
    "pydantic>=2.0.0",
    "pydantic-settings",
    "python-dotenv>=1.0.0",
    "typing-extensions>=4.0.0",

    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pytest-mock>=3.10.0",
    "httpx",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
]

# 直接指明入口，可以直接模块路径运行
[project.scripts]
zentao-mcp = "zentao_mcp.main:main"

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.hatch.build.targets.wheel]
packages = ["zentao_mcp"]
