# 🚀 启动 Zentao MCP Backend Service

## 立即启动服务

请在终端中执行以下命令：

```bash
cd zentao-mcp-backend-service
python simple_start.py
```

## 预期输出

你应该看到类似以下的输出：

```
🔍 开始启动服务...
📦 导入FastAPI...
✅ FastAPI导入成功
✅ 基础应用创建成功
📦 导入核心模块...
✅ 中间件导入成功
📦 导入数据库模块...
✅ 数据库模块导入成功
📦 导入admin端点...
✅ admin端点导入成功
📦 导入projects端点...
✅ projects端点导入成功
...
🚀 启动服务...
INFO:     Started server process [xxxxx]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
```

## 验证服务

服务启动后，打开新终端运行测试：

```bash
cd zentao-mcp-backend-service
python test_api.py
```

或者在浏览器中访问：
- http://localhost:8000 - 根端点
- http://localhost:8000/health - 健康检查
- http://localhost:8000/docs - API文档

## 如果启动失败

如果simple_start.py失败，尝试原始方法：

```bash
cd zentao-mcp-backend-service
../.venv/bin/python -m uvicorn main:app --host 0.0.0.0 --port 8000
```

## 停止服务

在服务运行的终端中按 `Ctrl+C` 停止服务。