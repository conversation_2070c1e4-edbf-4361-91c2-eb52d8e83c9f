"""
统一错误处理模块
提供重试机制、错误分类处理和标准化错误响应
"""

import asyncio
import logging
import time
from typing import Any, Callable, Dict, List, Optional, Type, Union
from functools import wraps
import httpx

from .exceptions import (
    ZentaoMCPError,
    ZentaoAPIError,
    NetworkError,
    TimeoutError,
    AuthenticationError,
    RateLimitError,
    ResourceNotFoundError,
    ValidationError
)


logger = logging.getLogger(__name__)


class RetryConfig:
    """重试配置"""
    
    def __init__(
        self,
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True
    ):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter
    
    def get_delay(self, attempt: int) -> float:
        """计算重试延迟时间（指数退避）"""
        delay = min(
            self.base_delay * (self.exponential_base ** attempt),
            self.max_delay
        )
        
        if self.jitter:
            # 添加随机抖动，避免雷群效应
            import random
            delay *= (0.5 + random.random() * 0.5)
        
        return delay


class ErrorClassifier:
    """错误分类器"""
    
    # 可重试的HTTP状态码
    RETRYABLE_STATUS_CODES = {500, 502, 503, 504, 429}
    
    # 不可重试的HTTP状态码
    NON_RETRYABLE_STATUS_CODES = {400, 401, 403, 404, 422}
    
    @classmethod
    def is_retryable_error(cls, error: Exception) -> bool:
        """判断错误是否可重试"""
        if isinstance(error, (NetworkError, TimeoutError)):
            return True
        
        if isinstance(error, RateLimitError):
            return True
        
        if isinstance(error, ZentaoAPIError):
            if error.status_code in cls.RETRYABLE_STATUS_CODES:
                return True
            if error.status_code in cls.NON_RETRYABLE_STATUS_CODES:
                return False
            # 未知状态码默认可重试
            return True
        
        if isinstance(error, httpx.RequestError):
            return True
        
        if isinstance(error, httpx.TimeoutException):
            return True
        
        # 认证错误、验证错误、资源不存在错误不重试
        if isinstance(error, (AuthenticationError, ValidationError, ResourceNotFoundError)):
            return False
        
        # 其他未知错误默认不重试
        return False
    
    @classmethod
    def classify_http_error(cls, response: httpx.Response) -> ZentaoMCPError:
        """将HTTP响应错误分类为具体的异常类型"""
        status_code = response.status_code
        
        try:
            response_data = response.json()
        except Exception:
            response_data = {"text": response.text}
        
        if status_code == 401:
            return AuthenticationError("认证失败，请检查用户名和密码")
        
        elif status_code == 403:
            return AuthenticationError("权限不足，无法访问该资源")
        
        elif status_code == 404:
            return ResourceNotFoundError("API端点", response.url)
        
        elif status_code == 422:
            return ValidationError("请求参数验证失败", response_data=response_data)
        
        elif status_code == 429:
            return RateLimitError("请求频率过高，请稍后重试")
        
        elif status_code >= 500:
            return ZentaoAPIError(
                f"禅道服务器内部错误 (HTTP {status_code})",
                status_code=status_code,
                response_data=response_data
            )
        
        else:
            return ZentaoAPIError(
                f"禅道API调用失败 (HTTP {status_code})",
                status_code=status_code,
                response_data=response_data
            )
    
    @classmethod
    def classify_request_error(cls, error: Exception) -> ZentaoMCPError:
        """将请求异常分类为具体的异常类型"""
        if isinstance(error, httpx.TimeoutException):
            return TimeoutError(f"请求超时: {str(error)}")
        
        elif isinstance(error, httpx.ConnectError):
            return NetworkError(f"连接失败: {str(error)}")
        
        elif isinstance(error, httpx.RequestError):
            return NetworkError(f"网络请求失败: {str(error)}")
        
        elif isinstance(error, ZentaoMCPError):
            return error
        
        else:
            return ZentaoMCPError(f"未知错误: {str(error)}", error_code="UNKNOWN_ERROR")


class ErrorHandler:
    """统一错误处理器"""
    
    def __init__(self, retry_config: Optional[RetryConfig] = None):
        self.retry_config = retry_config or RetryConfig()
        self.classifier = ErrorClassifier()
    
    async def handle_with_retry(
        self,
        func: Callable,
        *args,
        **kwargs
    ) -> Any:
        """带重试机制的函数执行"""
        last_error = None
        
        for attempt in range(self.retry_config.max_retries + 1):
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)
            
            except Exception as error:
                last_error = self._process_error(error)
                
                # 记录错误日志
                logger.warning(
                    f"函数 {func.__name__} 执行失败 (尝试 {attempt + 1}/{self.retry_config.max_retries + 1}): {last_error}",
                    extra={
                        "function": func.__name__,
                        "attempt": attempt + 1,
                        "max_attempts": self.retry_config.max_retries + 1,
                        "error_type": type(last_error).__name__,
                        "error_code": getattr(last_error, 'error_code', 'UNKNOWN'),
                        "retryable": self.classifier.is_retryable_error(last_error)
                    }
                )
                
                # 判断是否可重试
                if not self.classifier.is_retryable_error(last_error):
                    logger.error(f"不可重试的错误: {last_error}")
                    raise last_error
                
                # 如果是最后一次尝试，直接抛出异常
                if attempt >= self.retry_config.max_retries:
                    logger.error(f"重试次数已达上限，最终失败: {last_error}")
                    raise last_error
                
                # 计算延迟时间并等待
                delay = self.retry_config.get_delay(attempt)
                logger.info(f"等待 {delay:.2f} 秒后重试...")
                await asyncio.sleep(delay)
        
        # 理论上不会到达这里
        raise last_error
    
    def _process_error(self, error: Exception) -> ZentaoMCPError:
        """处理和分类错误"""
        if isinstance(error, ZentaoMCPError):
            return error
        
        elif isinstance(error, httpx.HTTPStatusError):
            return self.classifier.classify_http_error(error.response)
        
        elif isinstance(error, httpx.RequestError):
            return self.classifier.classify_request_error(error)
        
        else:
            return self.classifier.classify_request_error(error)
    
    def handle_sync_error(self, error: Exception) -> ZentaoMCPError:
        """同步错误处理"""
        processed_error = self._process_error(error)
        
        # 记录错误日志
        logger.error(
            f"同步操作发生错误: {processed_error}",
            extra={
                "error_type": type(processed_error).__name__,
                "error_code": getattr(processed_error, 'error_code', 'UNKNOWN'),
                "original_error": str(error)
            }
        )
        
        return processed_error


def with_error_handling(retry_config: Optional[RetryConfig] = None):
    """错误处理装饰器"""
    def decorator(func: Callable):
        handler = ErrorHandler(retry_config)
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            return await handler.handle_with_retry(func, *args, **kwargs)
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as error:
                raise handler.handle_sync_error(error)
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def with_timeout(timeout_seconds: float):
    """超时处理装饰器"""
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await asyncio.wait_for(
                    func(*args, **kwargs),
                    timeout=timeout_seconds
                )
            except asyncio.TimeoutError:
                raise TimeoutError(f"操作超时 ({timeout_seconds}秒)")
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # 同步函数的超时处理比较复杂，这里简化处理
            return func(*args, **kwargs)
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# 全局错误处理器实例
default_error_handler = ErrorHandler()