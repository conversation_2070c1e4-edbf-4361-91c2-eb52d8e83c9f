import httpx
import logging
import json
import os
import datetime
import asyncio
from functools import lru_cache
from typing import List, Dict, Any, Optional, Union, Callable
import time

from .config import settings
from .models import (
    BugModel, ProjectModel, StoryModel, TaskModel, 
    DepartmentModel, UserModel, APIResponse
)
from .exceptions import (
    ZentaoAPIError, NetworkError, TimeoutError, 
    ResourceNotFoundError, ValidationError
)
from .error_handler import (
    ErrorHandler, RetryConfig, with_error_handling, 
    with_timeout, default_error_handler
)
from .auth import get_session_manager, SessionManager
from .cache import (
    cached_call, ZentaoCacheKeys, ZentaoCacheTags, 
    get_cache, cache_key_hash
)

logger = logging.getLogger(__name__)

class ZentaoClient:
    """禅道API客户端 - 兼容原有API的异步版本"""
    
    def __init__(self, domain: Optional[str] = None, log_dir: Optional[str] = None, request_callback=None):
        """
        初始化API客户端
        
        Args:
            domain (str, optional): API域名，如果不提供则使用配置文件中的设置
            log_dir (str, optional): 日志目录，如果不提供则使用配置文件中的设置
            request_callback (callable, optional): API请求回调函数，用于记录API请求信息
        """
        if domain:
            self.base_url = f"http://{domain}"
            self.domain = domain
        else:
            self.base_url = settings.get_zentao_base_url().rstrip('/')
            self.domain = settings.get_zentao_base_url().replace('http://', '').replace('https://', '')
        
        # 禅道API无需认证
        self.log_dir = log_dir or settings.log_dir
        self.request_callback = request_callback
        self.request_counter = 0
        self._session_id = None
        
        # 初始化会话管理器
        self.session_manager = get_session_manager()
        self.session_manager.set_api_client(self)
        
        # 创建HTTP客户端，使用配置的超时时间和连接池
        timeout = httpx.Timeout(
            connect=10.0,  # 连接超时
            read=settings.request_timeout,  # 读取超时
            write=10.0,  # 写入超时
            pool=5.0  # 连接池超时
        )
        
        limits = httpx.Limits(
            max_connections=settings.connection_pool_size,
            max_keepalive_connections=settings.connection_pool_size // 2,
            keepalive_expiry=30.0  # 保持连接30秒
        )
        
        self._client = httpx.AsyncClient(
            base_url=self.base_url, 
            timeout=timeout,
            limits=limits,
            # 启用连接重用
            trust_env=True,
            # 暂时禁用HTTP/2，避免依赖问题
            http2=False,
            # 自动跟随重定向
            follow_redirects=True,
            # 启用重试
            transport=httpx.AsyncHTTPTransport(
                retries=settings.max_retries if settings.enable_retry else 0
            )
        )
        
        # 并发控制
        self._semaphore = asyncio.Semaphore(settings.max_concurrent_requests)
        self._request_queue = asyncio.Queue()
        self._batch_requests = {}  # 批量请求去重
        
        # 确保日志目录存在
        os.makedirs(self.log_dir, exist_ok=True)
        
        self.logger = self._setup_logger()
        
        # 初始化错误处理器
        retry_config = RetryConfig(
            max_retries=settings.max_retries,
            base_delay=1.0,
            max_delay=60.0,
            exponential_base=2.0,
            jitter=True
        )
        self.error_handler = ErrorHandler(retry_config)
        
        self.logger.info(f"初始化禅道API客户端，域名: {self.domain}")
        self.logger.info(f"会话管理: 启用, 重试机制: {settings.enable_retry}, 最大重试: {settings.max_retries}")
        self.logger.info(f"错误处理: 启用, 最大重试: {retry_config.max_retries}, 基础延迟: {retry_config.base_delay}s")
    
    async def initialize(self):
        """异步初始化客户端"""
        try:
            # 测试连接
            self.logger.info("测试禅道API连接...")
            
            # 尝试获取部门列表来测试连接
            try:
                await self.get_all_departments_async()
                self.logger.info("禅道API连接测试成功")
            except Exception as e:
                self.logger.warning(f"禅道API连接测试失败: {e}")
                # 不抛出异常，允许后续请求时再处理
            
            self.logger.info("禅道API客户端初始化完成")
        except Exception as e:
            self.logger.error(f"禅道API客户端初始化失败: {e}")
            raise

    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger("zentao_api")
        
        # 设置日志级别
        log_level = getattr(logging, settings.log_level.upper(), logging.INFO)
        logger.setLevel(log_level)
        
        # 清除已有的处理器
        if logger.handlers:
            for handler in logger.handlers:
                logger.removeHandler(handler)
        
        # 确保日志目录存在
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 创建文件处理器
        log_file = os.path.join(self.log_dir, f"zentao_api_{self.domain.replace('.', '_')}.log")
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        
        # 设置格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        
        # 只添加文件处理器，不添加控制台处理器
        # 这样API请求的详细日志只会输出到文件，不会显示在控制台
        logger.addHandler(file_handler)
        
        # 防止日志传播到根日志器，避免在控制台显示
        logger.propagate = False
        
        return logger

    async def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None, retries: int = None, use_cache: bool = True) -> Dict[str, Any]:
        """
        发送HTTP请求，支持重试机制、会话管理、并发控制和请求去重
        
        Args:
            method (str): HTTP方法 (GET, POST)
            endpoint (str): API端点
            data (dict, optional): 请求数据
            retries (int): 重试次数，如果不提供则使用配置值
            use_cache (bool): 是否使用缓存
            
        Returns:
            dict: API响应
            
        Raises:
            ZentaoAPIError: API调用失败
            NetworkError: 网络连接失败
            TimeoutError: 请求超时
        """
        if retries is None:
            retries = settings.max_retries if settings.enable_retry else 0
        
        # 生成请求唯一标识符用于去重
        request_signature = cache_key_hash(method, endpoint, data or {})
        
        # 请求去重：如果相同请求正在进行，等待其结果
        if settings.enable_batch_optimization and request_signature in self._batch_requests:
            self.logger.debug(f"请求去重，等待现有请求: {endpoint}")
            return await self._batch_requests[request_signature]
        
        # 创建请求Future用于去重
        request_future = asyncio.Future()
        if settings.enable_batch_optimization:
            self._batch_requests[request_signature] = request_future
        
        # 对于新API，直接使用endpoint
        # 禅道API无需认证，直接使用endpoint
        if endpoint.startswith('/'):
            url = endpoint
        else:
            url = f"/{endpoint}"
        
        headers = {"Content-Type": "application/json"}
        
        # 生成请求ID
        self.request_counter += 1
        request_id = f"req_{self.request_counter}"
        
        # 记录请求信息
        self.logger.debug(f"===== 请求开始 [{request_id}] =====")
        self.logger.debug(f"请求方法: {method}")
        self.logger.debug(f"请求URL: {self.base_url}{url}")
        self.logger.debug(f"请求头: {headers}")
        
        # 安全地记录请求数据
        if data and self.logger.isEnabledFor(logging.DEBUG):
            try:
                self.logger.debug(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            except Exception as e:
                self.logger.warning(f"记录请求数据失败: {str(e)}")
                self.logger.debug(f"请求数据: {str(data)}")
        
        last_exception = None
        
        # 使用信号量控制并发
        async with self._semaphore:
            try:
                result = await self._execute_request_with_retries(
                    method, url, headers, data, retries, request_id
                )
                
                # 设置请求Future的结果
                if settings.enable_batch_optimization and not request_future.done():
                    request_future.set_result(result)
                
                return result
                
            except Exception as e:
                # 设置请求Future的异常
                if settings.enable_batch_optimization and not request_future.done():
                    request_future.set_exception(e)
                raise
            finally:
                # 清理请求去重记录
                if settings.enable_batch_optimization:
                    self._batch_requests.pop(request_signature, None)
    
    async def _execute_request_with_retries(self, method: str, url: str, headers: Dict, 
                                          data: Optional[Dict], retries: int, request_id: str) -> Dict[str, Any]:
        """执行HTTP请求，包含重试逻辑"""
        last_exception = None
        
        for attempt in range(retries + 1):
            try:
                # 在每次重试前检查会话是否需要刷新
                if attempt > 0 and not url.startswith(('apiData/', 'api/')):
                    await self.session_manager.refresh_session_if_needed()
                
                start_time = time.time()
                
                if method.upper() == "GET":
                    response = await self._client.get(url, headers=headers)
                elif method.upper() == "POST":
                    response = await self._client.post(url, headers=headers, json=data)
                else:
                    raise ValidationError(f"不支持的HTTP方法: {method}")
                
                response.raise_for_status()
                
                # 记录请求性能
                request_duration = time.time() - start_time
                if request_duration > 2.0:  # 超过2秒的请求记录警告
                    self.logger.warning(f"慢请求: {url}, 耗时: {request_duration:.2f}s")
                
                # 安全地解析响应JSON
                try:
                    result = response.json()
                except ValueError as e:
                    self.logger.warning(f"响应不是有效的JSON格式，状态码: {response.status_code}")
                    self.logger.warning(f"响应内容: {response.text[:500]}")
                    raise ZentaoAPIError(
                        message=f"API响应格式错误，状态码: {response.status_code}",
                        status_code=response.status_code,
                        response_data={"text": response.text[:200] + "..."}
                    )
                
                # 检查API响应中的错误信息
                if isinstance(result, dict):
                    # 检查禅道API特有的错误格式
                    if result.get('status') == 'error' or result.get('error'):
                        error_msg = result.get('message', result.get('error', '未知错误'))
                        
                        # 如果是会话相关错误，使会话无效
                        if any(keyword in error_msg.lower() for keyword in ['session', 'login', '登录', '会话']):
                            await self.session_manager.invalidate_session()
                            
                            # 如果还有重试机会，继续重试
                            if attempt < retries:
                                self.logger.warning(f"会话错误，将重试: {error_msg}")
                                continue
                        
                        raise ZentaoAPIError(
                            message=f"禅道API返回错误: {error_msg}",
                            status_code=response.status_code,
                            response_data=result
                        )
                
                # 记录响应信息
                self.logger.debug(f"响应状态码: {response.status_code}, 耗时: {request_duration:.3f}s")
                
                # 安全地记录响应数据
                if self.logger.isEnabledFor(logging.DEBUG):
                    try:
                        self.logger.debug(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    except Exception as e:
                        self.logger.warning(f"记录响应数据失败: {str(e)}")
                        self.logger.debug(f"响应数据: {str(result)}")
                
                self.logger.debug(f"===== 请求结束 [{request_id}] =====\n")
                
                # 安全地调用回调函数
                if self.request_callback:
                    try:
                        self.request_callback(request_id, method, f"{self.base_url}{url}", headers, data, response.status_code, result)
                    except Exception as e:
                        self.logger.error(f"调用请求回调函数失败: {str(e)}")
                
                return result
                
            except httpx.TimeoutException as e:
                last_exception = TimeoutError(f"请求超时: {str(e)}")
                self.logger.warning(f"请求超时 (尝试 {attempt + 1}/{retries + 1}): {str(e)}")
                
            except httpx.NetworkError as e:
                last_exception = NetworkError(f"网络连接失败: {str(e)}")
                self.logger.warning(f"网络连接失败 (尝试 {attempt + 1}/{retries + 1}): {str(e)}")
                
            except httpx.HTTPStatusError as e:
                error_msg = f"HTTP错误 {e.response.status_code}: {str(e)}"
                
                # 对于4xx错误，不重试
                if 400 <= e.response.status_code < 500:
                    self.logger.error(error_msg)
                    raise ZentaoAPIError(
                        message=error_msg,
                        status_code=e.response.status_code,
                        response_data={"text": e.response.text}
                    )
                
                last_exception = ZentaoAPIError(
                    message=error_msg,
                    status_code=e.response.status_code,
                    response_data={"text": e.response.text}
                )
                self.logger.warning(f"HTTP错误 (尝试 {attempt + 1}/{retries + 1}): {error_msg}")
            
            # 如果不是最后一次尝试，等待一段时间再重试
            if attempt < retries:
                # 指数退避，但有最大延迟限制
                wait_time = min(settings.retry_backoff_factor * (2 ** attempt), settings.retry_max_delay)
                self.logger.info(f"等待 {wait_time} 秒后重试...")
                await asyncio.sleep(wait_time)
        
        # 所有重试都失败了
        self.logger.error(f"请求失败，已重试 {retries} 次: {str(last_exception)}")
        self.logger.debug(f"===== 请求结束(失败) [{request_id}] =====\n")
        
        # 安全地调用回调函数记录失败信息
        if self.request_callback:
            try:
                self.request_callback(request_id, method, f"{self.base_url}{url}", headers, data, None, {"error": str(last_exception)})
            except Exception as callback_error:
                self.logger.error(f"调用请求回调函数失败: {str(callback_error)}")
        
        raise last_exception

    async def _login(self) -> str:
        """禅道API无需认证，直接返回空会话ID
        
        Returns:
            str: 空会话ID
        """
        self.logger.info("禅道API无需认证，跳过登录")
        self._session_id = "no_auth_required"
        return "no_auth_required"

    def _parse_response_data(self, data: Dict[str, Any], model_class) -> Union[List, Dict, Any]:
        """
        解析API响应数据并转换为数据模型
        
        Args:
            data: API响应数据
            model_class: 数据模型类
            
        Returns:
            转换后的数据模型或原始数据
        """
        try:
            if isinstance(data, dict):
                if 'data' in data:
                    # 标准API响应格式
                    response_data = data['data']
                    if isinstance(response_data, list):
                        return [model_class.from_dict(item) for item in response_data]
                    elif isinstance(response_data, dict):
                        return model_class.from_dict(response_data)
                else:
                    # 直接数据格式
                    if isinstance(data, list):
                        return [model_class.from_dict(item) for item in data]
                    else:
                        return model_class.from_dict(data)
            elif isinstance(data, list):
                return [model_class.from_dict(item) for item in data]
            else:
                return data
        except Exception as e:
            self.logger.warning(f"数据模型转换失败: {str(e)}, 返回原始数据")
            return data

    async def close(self):
        """关闭HTTP客户端和清理会话"""
        try:
            # 清理会话
            if self.session_manager:
                await self.session_manager.invalidate_session()
            
            # 关闭HTTP客户端
            if self._client:
                await self._client.aclose()
                
            self.logger.info("禅道API客户端已关闭")
        except Exception as e:
            self.logger.error(f"关闭客户端时发生错误: {e}")
    
    def get_session_info(self) -> Dict[str, Any]:
        """获取会话信息"""
        if self.session_manager:
            return self.session_manager.get_session_info()
        return {"status": "no_session_manager"}
    
    async def batch_request(self, requests: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量处理请求，提高并发性能
        
        Args:
            requests: 请求列表，每个请求包含 method, endpoint, data 等字段
            
        Returns:
            响应列表，与请求列表一一对应
        """
        if not requests:
            return []
        
        self.logger.info(f"开始批量处理 {len(requests)} 个请求")
        
        # 创建并发任务
        tasks = []
        for i, req in enumerate(requests):
            method = req.get('method', 'GET')
            endpoint = req.get('endpoint', '')
            data = req.get('data')
            use_cache = req.get('use_cache', True)
            
            task = asyncio.create_task(
                self._make_request(method, endpoint, data, use_cache=use_cache),
                name=f"batch_request_{i}"
            )
            tasks.append(task)
        
        # 等待所有任务完成
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    self.logger.error(f"批量请求 {i} 失败: {result}")
                    processed_results.append({
                        "error": str(result),
                        "request_index": i
                    })
                else:
                    processed_results.append(result)
            
            self.logger.info(f"批量请求完成，成功: {len([r for r in results if not isinstance(r, Exception)])}, 失败: {len([r for r in results if isinstance(r, Exception)])}")
            return processed_results
            
        except Exception as e:
            self.logger.error(f"批量请求处理失败: {e}")
            raise
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取客户端性能统计"""
        cache_stats = get_cache().get_stats()
        
        return {
            "total_requests": self.request_counter,
            "cache_stats": cache_stats,
            "concurrent_limit": settings.max_concurrent_requests,
            "connection_pool_size": settings.connection_pool_size,
            "request_timeout": settings.request_timeout,
            "retry_enabled": settings.enable_retry,
            "max_retries": settings.max_retries,
            "batch_optimization": settings.enable_batch_optimization
        }

    # 同步方法包装器 - 用于测试和非异步环境
    def _run_async(self, coro):
        """运行异步方法的同步包装器"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果已经在事件循环中，创建新的任务
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, coro)
                    return future.result()
            else:
                return loop.run_until_complete(coro)
        except RuntimeError:
            # 没有事件循环，创建新的
            return asyncio.run(coro)

    # 部门相关API
    async def get_all_departments_async(self, parse_model: bool = True) -> Union[List[DepartmentModel], Dict[str, Any]]:
        """
        获取所有部门 - 异步版本（支持缓存）
        
        Args:
            parse_model: 是否解析为数据模型
            
        Returns:
            部门列表或原始响应数据
        """
        try:
            cache_key = ZentaoCacheKeys.departments()
            ttl = settings.get_cache_ttl_for_resource('departments')
            tags = [ZentaoCacheTags.DEPARTMENTS]
            
            async def fetch_departments():
                return await self._make_request("GET", "apiData/getAllDept", use_cache=False)
            
            response = await cached_call(cache_key, fetch_departments, ttl=ttl, tags=tags)
            
            if parse_model:
                return self._parse_response_data(response, DepartmentModel)
            return response
        except Exception as e:
            self.logger.error(f"获取部门列表失败: {str(e)}")
            raise
    
    def get_all_departments(self, parse_model: bool = True):
        """获取所有部门 - 同步版本"""
        return self._run_async(self.get_all_departments_async(parse_model))
    
    # 项目相关API
    async def get_all_projects_async(self, parse_model: bool = True) -> Union[List[ProjectModel], Dict[str, Any]]:
        """
        获取所有项目 - 异步版本（支持缓存）
        
        Args:
            parse_model: 是否解析为数据模型
            
        Returns:
            项目列表或原始响应数据
        """
        try:
            cache_key = ZentaoCacheKeys.projects()
            ttl = settings.get_cache_ttl_for_resource('projects')
            tags = [ZentaoCacheTags.PROJECTS]
            
            async def fetch_projects():
                return await self._make_request("GET", "apiData/getAllProject", use_cache=False)
            
            response = await cached_call(cache_key, fetch_projects, ttl=ttl, tags=tags)
            
            if parse_model:
                return self._parse_response_data(response, ProjectModel)
            return response
        except Exception as e:
            self.logger.error(f"获取项目列表失败: {str(e)}")
            raise
    
    def get_all_projects(self, parse_model: bool = True):
        """获取所有项目 - 同步版本"""
        return self._run_async(self.get_all_projects_async(parse_model))
    
    # Bug相关API
    async def get_bugs_by_time_range_async(self, start_date: str, end_date: str, parse_model: bool = True) -> Union[List[BugModel], Dict[str, Any]]:
        """
        用时间段查询bug列表 - 异步版本（支持缓存）
        
        Args:
            start_date (str): 开始日期 (YYYY-MM-DD HH:MM:SS格式)
            end_date (str): 结束日期 (YYYY-MM-DD HH:MM:SS格式)
            parse_model: 是否解析为数据模型
            
        Returns:
            Bug列表或原始响应数据
        """
        try:
            cache_key = ZentaoCacheKeys.bugs_by_time_range(start_date, end_date)
            ttl = 600  # Bug数据缓存10分钟
            tags = [ZentaoCacheTags.BUGS, ZentaoCacheTags.time_range_related(start_date, end_date)]
            
            async def fetch_bugs():
                data = {
                    "startDate": start_date,
                    "endDate": end_date
                }
                return await self._make_request("POST", "apiData/getBugListByTimeRange", data, use_cache=False)
            
            response = await cached_call(cache_key, fetch_bugs, ttl=ttl, tags=tags)
            
            if parse_model:
                return self._parse_response_data(response, BugModel)
            return response
        except Exception as e:
            self.logger.error(f"根据时间范围获取Bug列表失败: {str(e)}")
            raise
    
    def get_bugs_by_time_range(self, start_date: str, end_date: str, parse_model: bool = True):
        """用时间段查询bug列表 - 同步版本"""
        return self._run_async(self.get_bugs_by_time_range_async(start_date, end_date, parse_model))
    
    async def get_bugs_by_time_range_and_dept_async(self, start_date: str, end_date: str, dept_id: int):
        """
        根据时间段和部门查询bug - 异步版本
        
        Args:
            start_date (str): 开始日期 (YYYY-MM-DD HH:MM:SS格式)
            end_date (str): 结束日期 (YYYY-MM-DD HH:MM:SS格式)
            dept_id (int): 部门ID
            
        Returns:
            dict: API响应
        """
        data = {
            "startDate": start_date,
            "endDate": end_date,
            "deptId": dept_id
        }
        return await self._make_request("POST", "apiData/getBugListByTimeRangeAndDeptId", data)
    
    def get_bugs_by_time_range_and_dept(self, start_date: str, end_date: str, dept_id: int):
        """根据时间段和部门查询bug - 同步版本"""
        return self._run_async(self.get_bugs_by_time_range_and_dept_async(start_date, end_date, dept_id))
    
    async def get_bug_detail_async(self, bug_id: int):
        """
        根据BUGID查询BUG详情 - 异步版本
        
        Args:
            bug_id (int): BUG ID
            
        Returns:
            dict: API响应
        """
        data = {"detailId": bug_id}
        return await self._make_request("POST", "api/getBugDetail", data)
    
    def get_bug_detail(self, bug_id: int):
        """根据BUGID查询BUG详情 - 同步版本"""
        return self._run_async(self.get_bug_detail_async(bug_id))
    
    async def get_bugs_by_project_async(self, project_id: int):
        """
        根据项目ID查询项目BUG列表 - 异步版本
        
        Args:
            project_id (int): 项目ID
            
        Returns:
            dict: API响应
        """
        data = {"detailId": project_id}
        return await self._make_request("POST", "api/getBugByProject", data)
    
    def get_bugs_by_project(self, project_id: int):
        """根据项目ID查询项目BUG列表 - 同步版本"""
        return self._run_async(self.get_bugs_by_project_async(project_id))
    
    # 需求相关API
    async def get_story_info_async(self, story_ids: List[str]):
        """
        获取产品需求工时 - 异步版本
        
        Args:
            story_ids (list): 需求ID列表
            
        Returns:
            dict: API响应
        """
        data = {"story": story_ids}
        return await self._make_request("POST", "api/getStory", data)
    
    def get_story_info(self, story_ids: List[str]):
        """获取产品需求工时 - 同步版本"""
        return self._run_async(self.get_story_info_async(story_ids))
    
    async def get_story_end_async(self, story_ids: List[str]):
        """
        获取需求任务工时（已完成）- 异步版本
        
        Args:
            story_ids (list): 需求ID列表
            
        Returns:
            dict: API响应
        """
        data = {"story": story_ids}
        return await self._make_request("POST", "api/getStoryEnd", data)
    
    def get_story_end(self, story_ids: List[str]):
        """获取需求任务工时（已完成）- 同步版本"""
        return self._run_async(self.get_story_end_async(story_ids))
    
    async def check_story_exists_async(self, story_ids: List[str]):
        """
        查询需求号是否存在 - 异步版本
        
        Args:
            story_ids (list): 需求ID列表
            
        Returns:
            dict: API响应
        """
        data = {"story": story_ids}
        return await self._make_request("POST", "api/getStoryId", data)
    
    def check_story_exists(self, story_ids: List[str]):
        """查询需求号是否存在 - 同步版本"""
        return self._run_async(self.check_story_exists_async(story_ids))
    
    async def get_story_by_id_async(self, story_id: int):
        """
        根据需求ID查询需求详情 - 异步版本
        
        Args:
            story_id (int): 需求ID
            
        Returns:
            dict: API响应
        """
        data = {"detailId": story_id}
        return await self._make_request("POST", "api/getStoryDetail", data)
    
    def get_story_by_id(self, story_id: int):
        """根据需求ID查询需求详情 - 同步版本"""
        return self._run_async(self.get_story_by_id_async(story_id))
    
    async def get_stories_by_project_async(self, project_id: int):
        """
        根据项目ID查询项目需求列表 - 异步版本（支持缓存）
        
        Args:
            project_id (int): 项目ID
            
        Returns:
            dict: API响应
        """
        cache_key = ZentaoCacheKeys.project_stories(project_id)
        ttl = 900  # 项目需求缓存15分钟
        tags = [ZentaoCacheTags.STORIES, ZentaoCacheTags.project_related(project_id)]
        
        async def fetch_project_stories():
            data = {"detailId": project_id}
            return await self._make_request("POST", "api/getStoryByProjectid", data, use_cache=False)
        
        return await cached_call(cache_key, fetch_project_stories, ttl=ttl, tags=tags)
    
    def get_stories_by_project(self, project_id: int):
        """根据项目ID查询项目需求列表 - 同步版本"""
        return self._run_async(self.get_stories_by_project_async(project_id))
    
    # 任务相关API
    async def get_tasks_by_project_async(self, project_id: int):
        """
        根据项目ID查询项目任务列表 - 异步版本（支持缓存）
        
        Args:
            project_id (int): 项目ID
            
        Returns:
            dict: API响应
        """
        cache_key = ZentaoCacheKeys.project_tasks(project_id)
        ttl = 600  # 项目任务缓存10分钟
        tags = [ZentaoCacheTags.TASKS, ZentaoCacheTags.project_related(project_id)]
        
        async def fetch_project_tasks():
            data = {"detailId": project_id}
            return await self._make_request("POST", "api/getTaskByProject", data, use_cache=False)
        
        return await cached_call(cache_key, fetch_project_tasks, ttl=ttl, tags=tags)
    
    def get_tasks_by_project(self, project_id: int):
        """根据项目ID查询项目任务列表 - 同步版本"""
        return self._run_async(self.get_tasks_by_project_async(project_id))
    
    async def get_task_by_id_async(self, task_id: int):
        """
        根据任务ID查询任务详情 - 异步版本
        
        Args:
            task_id (int): 任务ID
            
        Returns:
            dict: API响应
        """
        data = {"detailId": task_id}
        return await self._make_request("POST", "api/getTaskById", data)
    
    def get_task_by_id(self, task_id: int):
        """根据任务ID查询任务详情 - 同步版本"""
        return self._run_async(self.get_task_by_id_async(task_id))
    
    # 用户相关API
    async def get_users_by_department_async(self, dept_id: int, parse_model: bool = True) -> Union[List, Dict[str, Any]]:
        """
        根据部门ID查询用户列表 - 异步版本
        
        调用禅道API: /api/getUserByDept
        
        Args:
            dept_id (int): 部门ID
            parse_model: 是否解析为数据模型
            
        Returns:
            dict: API响应，包含用户列表
        """
        try:
            # 直接调用禅道API接口  TODO 请检查下面参数是否需要包裹？ 
            data = {"deptId": dept_id}
            response = await self._make_request("POST", "/api/getUserByDept", data)
            
            if parse_model:
                # 这里可以根据需要添加数据模型解析
                # 暂时返回原始响应
                return response
            return response
            
        except Exception as e:
            self.logger.error(f"根据部门ID获取用户列表失败: {e}")
            raise
    
    def get_users_by_department(self, dept_id: int, parse_model: bool = True):
        """根据部门ID查询用户列表 - 同步版本"""
        return self._run_async(self.get_users_by_department_async(dept_id, parse_model))
    
    async def get_user_info_async(self, user_account: str):
        """
        根据用户账号获取用户信息 - 异步版本
        
        调用禅道API: /api/getUserByAccount
        
        Args:
            user_account (str): 用户账号
            
        Returns:
            dict: 用户信息
        """
        try:
            # 调用批量用户查询接口，传入单个用户账号数组
            response = await self._make_request("POST", "/api/getUserByAccount", [user_account])
            
            # 从批量结果中提取单个用户信息
            if response.get("rsCode") == "********":
                users = response.get("body", [])
                if users:
                    # 返回第一个（也是唯一一个）用户的信息
                    user_info = users[0]
                    return {
                        "rsCode": "********",
                        "msg": "正常返回",
                        "body": user_info
                    }
                else:
                    # 用户不存在
                    return {
                        "rsCode": "********",
                        "msg": "用户不存在",
                        "body": None
                    }
            else:
                # API调用失败，直接返回原始响应
                return response
                
        except Exception as e:
            self.logger.error(f"获取用户信息失败: {e}")
            raise
    
    def get_user_info(self, user_account: str):
        """根据用户账号获取用户信息 - 同步版本"""
        return self._run_async(self.get_user_info_async(user_account))
    
    async def get_users_by_accounts_async(self, accounts: List[str]):
        """
        根据域账号批量查询用户信息 - 异步版本
        
        调用禅道API: /api/getUserByAccount
        
        Args:
            accounts (List[str]): 域账号列表
            
        Returns:
            dict: API响应，包含用户信息列表
        """
        try:
            response = await self._make_request("POST", "/api/getUserByAccount", accounts)
            return response
        except Exception as e:
            self.logger.error(f"批量查询用户信息失败: {e}")
            raise
    
    def get_users_by_accounts(self, accounts: List[str]):
        """根据域账号批量查询用户信息 - 同步版本"""
        return self._run_async(self.get_users_by_accounts_async(accounts))
    
    async def get_tasks_by_account_async(self, account: str, start_date: str, end_date: str, is_doing: bool = False):
        """
        根据域账号查询任务 - 异步版本
        
        调用禅道API: /api/getTaskByAccount
        
        Args:
            account (str): 域账号
            start_date (str): 开始日期 (YYYY-MM-DD HH:MM:SS格式)
            end_date (str): 结束日期 (YYYY-MM-DD HH:MM:SS格式)
            is_doing (bool): 是否只查询进行中的任务
            
        Returns:
            dict: API响应
        """
        try:
            data = {
                "account": account,
                "start": start_date,
                "end": end_date,
                "isdo": 1 if is_doing else 0
            }
            response = await self._make_request("POST", "/api/getTaskByAccount", data)
            return response
        except Exception as e:
            self.logger.error(f"根据域账号查询任务失败: {e}")
            raise
    
    def get_tasks_by_account(self, account: str, start_date: str, end_date: str, is_doing: bool = False):
        """根据域账号查询任务 - 同步版本"""
        return self._run_async(self.get_tasks_by_account_async(account, start_date, end_date, is_doing))
    
    async def get_tasks_by_dept_async(self, dept_id: int, start_date: str, end_date: str, is_doing: bool = False):
        """
        根据部门查询任务 - 异步版本
        
        调用禅道API: /api/getTaskByDept
        
        Args:
            dept_id (int): 部门ID
            start_date (str): 开始日期 (YYYY-MM-DD HH:MM:SS格式)
            end_date (str): 结束日期 (YYYY-MM-DD HH:MM:SS格式)
            is_doing (bool): 是否只查询进行中的任务
            
        Returns:
            dict: API响应
        """
        try:
            data = {
                "deptId": dept_id,
                "start": start_date,
                "end": end_date,
                "isdo": 1 if is_doing else 0
            }
            response = await self._make_request("POST", "/api/getTaskByDept", data)
            return response
        except Exception as e:
            self.logger.error(f"根据部门查询任务失败: {e}")
            raise
    
    def get_tasks_by_dept(self, dept_id: int, start_date: str, end_date: str, is_doing: bool = False):
        """根据部门查询任务 - 同步版本"""
        return self._run_async(self.get_tasks_by_dept_async(dept_id, start_date, end_date, is_doing))
    
    async def get_personal_bugs_async(self, account: str, status: str, start_date: str, end_date: str):
        """
        查询个人Bug - 异步版本
        
        调用禅道API: /api/getPersonalBugs
        
        Args:
            account (str): 域账号
            status (str): Bug状态 (active/resolved/released/closed)
            start_date (str): 开始日期 (YYYY-MM-DD HH:MM:SS格式)
            end_date (str): 结束日期 (YYYY-MM-DD HH:MM:SS格式)
            
        Returns:
            dict: API响应
        """
        try:
            data = {
                "account": account,
                "status": status,
                "start": start_date,
                "end": end_date
            }
            response = await self._make_request("POST", "/api/getPersonalBugs", data)
            return response
        except Exception as e:
            self.logger.error(f"查询个人Bug失败: {e}")
            raise
    
    def get_personal_bugs(self, account: str, status: str, start_date: str, end_date: str):
        """查询个人Bug - 同步版本"""
        return self._run_async(self.get_personal_bugs_async(account, status, start_date, end_date))
    
    async def get_stories_by_time_async(self, status: str, start_date: str, end_date: str):
        """
        根据时间段查询需求 - 异步版本
        
        调用禅道API: /api/getStorysByTime
        
        Args:
            status (str): 需求状态 (draft/active/changed/closed)
            start_date (str): 开始日期 (YYYY-MM-DD HH:MM:SS格式)
            end_date (str): 结束日期 (YYYY-MM-DD HH:MM:SS格式)
            
        Returns:
            dict: API响应
        """
        try:
            data = {
                "status": status,
                "start": start_date,
                "end": end_date
            }
            response = await self._make_request("POST", "/api/getStorysByTime", data)
            return response
        except Exception as e:
            self.logger.error(f"根据时间段查询需求失败: {e}")
            raise
    
    def get_stories_by_time(self, status: str, start_date: str, end_date: str):
        """根据时间段查询需求 - 同步版本"""
        return self._run_async(self.get_stories_by_time_async(status, start_date, end_date))

@lru_cache(maxsize=1)
def get_zentao_client() -> ZentaoClient:
    return ZentaoClient()