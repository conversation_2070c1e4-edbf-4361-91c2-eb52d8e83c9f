"""
禅道MCP服务认证模块
支持Token认证、会话管理和安全配置管理
"""
import hashlib
import hmac
import logging
import asyncio
import time
from typing import Optional, Dict, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field

from .config import settings
from .exceptions import AuthenticationError, ConfigurationError, NetworkError


logger = logging.getLogger(__name__)


@dataclass
class SessionInfo:
    """会话信息"""
    session_id: Optional[str] = None
    created_at: float = field(default_factory=time.time)
    last_used: float = field(default_factory=time.time)
    expires_at: Optional[float] = None
    is_valid: bool = True
    retry_count: int = 0
    
    def is_expired(self) -> bool:
        """检查会话是否过期"""
        if self.expires_at is None:
            return False
        return time.time() > self.expires_at
    
    def update_last_used(self):
        """更新最后使用时间"""
        self.last_used = time.time()
    
    def mark_invalid(self):
        """标记会话为无效"""
        self.is_valid = False
    
    def increment_retry(self):
        """增加重试次数"""
        self.retry_count += 1


class SessionManager:
    """会话管理器，负责禅道API会话的管理和自动重连"""
    
    def __init__(self, api_client=None):
        """初始化会话管理器
        
        Args:
            api_client: 禅道API客户端实例
        """
        self._api_client = api_client
        self._session_info = SessionInfo()
        self._login_lock = asyncio.Lock()
        self._session_ttl = 3600  # 会话TTL，默认1小时
        self._max_retry_attempts = settings.max_retries
        self._retry_delay = 1.0
        
    def set_api_client(self, api_client):
        """设置API客户端"""
        self._api_client = api_client
    
    async def get_valid_session(self) -> str:
        """获取有效的会话ID，如果需要则自动登录或重连
        
        Returns:
            str: 有效的会话ID
            
        Raises:
            AuthenticationError: 认证失败
        """
        async with self._login_lock:
            # 检查当前会话是否有效
            if (self._session_info.session_id and 
                self._session_info.is_valid and 
                not self._session_info.is_expired()):
                
                self._session_info.update_last_used()
                return self._session_info.session_id
            
            # 需要重新登录
            return await self._perform_login()
    
    async def _perform_login(self) -> str:
        """执行登录操作，支持重试机制
        
        Returns:
            str: 会话ID
            
        Raises:
            AuthenticationError: 登录失败
        """
        if not self._api_client:
            raise AuthenticationError("API客户端未设置")
        
        last_exception = None
        
        for attempt in range(self._max_retry_attempts + 1):
            try:
                logger.info(f"尝试登录禅道系统 (第{attempt + 1}次)")
                
                # 调用API客户端的登录方法
                session_id = await self._api_client._login()
                
                if session_id:
                    # 更新会话信息
                    self._session_info = SessionInfo(
                        session_id=session_id,
                        expires_at=time.time() + self._session_ttl,
                        is_valid=True,
                        retry_count=attempt
                    )
                    
                    logger.info("禅道系统登录成功")
                    return session_id
                else:
                    raise AuthenticationError("登录返回空会话ID")
                    
            except Exception as e:
                last_exception = e
                logger.warning(f"登录尝试失败 (第{attempt + 1}次): {e}")
                
                if attempt < self._max_retry_attempts:
                    # 计算退避延迟
                    delay = self._retry_delay * (2 ** attempt)
                    delay = min(delay, settings.retry_max_delay)
                    
                    logger.info(f"等待 {delay:.1f} 秒后重试...")
                    await asyncio.sleep(delay)
                else:
                    break
        
        # 所有重试都失败了
        error_msg = f"登录失败，已重试 {self._max_retry_attempts} 次"
        if last_exception:
            error_msg += f": {last_exception}"
        
        logger.error(error_msg)
        raise AuthenticationError(error_msg)
    
    async def invalidate_session(self):
        """使当前会话无效，强制下次重新登录"""
        logger.info("使会话无效，下次请求将重新登录")
        self._session_info.mark_invalid()
    
    async def refresh_session_if_needed(self) -> bool:
        """如果需要则刷新会话
        
        Returns:
            bool: 是否进行了刷新
        """
        # 检查会话是否即将过期（剩余时间少于5分钟）
        if (self._session_info.session_id and 
            self._session_info.expires_at and
            (self._session_info.expires_at - time.time()) < 300):
            
            logger.info("会话即将过期，主动刷新")
            await self.invalidate_session()
            await self.get_valid_session()
            return True
        
        return False
    
    def get_session_info(self) -> Dict[str, Any]:
        """获取会话信息摘要"""
        return {
            "has_session": bool(self._session_info.session_id),
            "is_valid": self._session_info.is_valid,
            "is_expired": self._session_info.is_expired(),
            "created_at": datetime.fromtimestamp(self._session_info.created_at).isoformat(),
            "last_used": datetime.fromtimestamp(self._session_info.last_used).isoformat(),
            "retry_count": self._session_info.retry_count,
        }


class TokenValidator:
    """Token验证器，支持增强的安全功能"""
    
    def __init__(self):
        """初始化Token验证器"""
        self._validate_config()
        self._failed_attempts = {}  # 记录失败尝试，用于防暴力破解
        self._max_failed_attempts = 5
        self._lockout_duration = 300  # 5分钟锁定
    
    def _validate_config(self):
        """验证认证配置"""
        if settings.enable_auth:
            if not settings.mcp_token or settings.mcp_token == "your-secret-token":
                raise ConfigurationError(
                    "启用认证时必须正确配置MCP_TOKEN",
                    config_key="mcp_token"
                )
            
            if len(settings.mcp_token) < settings.token_min_length:
                raise ConfigurationError(
                    f"MCP Token长度不能少于{settings.token_min_length}位",
                    config_key="mcp_token"
                )
        
        # 禅道API无需认证，跳过Token验证
    
    def validate_token(self, token: str, client_ip: Optional[str] = None) -> bool:
        """验证Token是否有效，支持防暴力破解
        
        Args:
            token: 待验证的Token
            client_ip: 客户端IP地址
            
        Returns:
            bool: Token是否有效
        """
        if not settings.enable_auth:
            logger.debug("认证已禁用，跳过Token验证")
            return True
        
        if not token:
            logger.debug("Token为空")
            self._record_failed_attempt(client_ip)
            return False
        
        # 检查是否被锁定
        if self._is_locked_out(client_ip):
            logger.warning(f"客户端 {client_ip} 因多次失败尝试被锁定")
            return False
        
        # 使用安全的字符串比较，防止时序攻击
        expected_token = settings.mcp_token.encode('utf-8')
        provided_token = token.encode('utf-8')
        
        if len(expected_token) != len(provided_token):
            logger.debug("Token长度不匹配")
            self._record_failed_attempt(client_ip)
            return False
        
        # 使用hmac.compare_digest进行安全比较
        is_valid = hmac.compare_digest(expected_token, provided_token)
        
        if not is_valid:
            logger.warning(f"Token验证失败，客户端: {client_ip}, Token前缀: {token[:8]}...")
            self._record_failed_attempt(client_ip)
        else:
            logger.debug("Token验证成功")
            self._clear_failed_attempts(client_ip)
        
        return is_valid
    
    def _record_failed_attempt(self, client_ip: Optional[str]):
        """记录失败尝试"""
        if not client_ip:
            return
        
        current_time = time.time()
        if client_ip not in self._failed_attempts:
            self._failed_attempts[client_ip] = []
        
        # 清理过期的失败记录
        self._failed_attempts[client_ip] = [
            attempt_time for attempt_time in self._failed_attempts[client_ip]
            if current_time - attempt_time < self._lockout_duration
        ]
        
        # 添加新的失败记录
        self._failed_attempts[client_ip].append(current_time)
    
    def _is_locked_out(self, client_ip: Optional[str]) -> bool:
        """检查是否被锁定"""
        if not client_ip or client_ip not in self._failed_attempts:
            return False
        
        current_time = time.time()
        recent_failures = [
            attempt_time for attempt_time in self._failed_attempts[client_ip]
            if current_time - attempt_time < self._lockout_duration
        ]
        
        return len(recent_failures) >= self._max_failed_attempts
    
    def _clear_failed_attempts(self, client_ip: Optional[str]):
        """清除失败尝试记录"""
        if client_ip and client_ip in self._failed_attempts:
            del self._failed_attempts[client_ip]
    
    def extract_token_from_context(self, context_meta: Dict[str, Any]) -> Optional[str]:
        """从上下文中提取Token
        
        Args:
            context_meta: MCP上下文元数据
            
        Returns:
            Optional[str]: 提取的Token，如果未找到则返回None
        """
        # 尝试从Authorization头中提取Bearer Token
        auth_header = context_meta.get("authorization", "")
        if auth_header.startswith("Bearer "):
            return auth_header[7:]  # 移除 "Bearer " 前缀
        
        # 尝试从X-API-Key头中提取
        api_key = context_meta.get("x-api-key", "")
        if api_key:
            return api_key
        
        # 尝试从其他可能的头中提取
        token = context_meta.get("token", "")
        if token:
            return token
        
        return None
    
    def get_client_ip(self, context_meta: Dict[str, Any]) -> Optional[str]:
        """从上下文中提取客户端IP"""
        # 尝试从各种可能的头中提取IP
        ip_headers = [
            "x-forwarded-for",
            "x-real-ip", 
            "x-client-ip",
            "cf-connecting-ip",  # Cloudflare
            "remote-addr"
        ]
        
        for header in ip_headers:
            ip = context_meta.get(header, "")
            if ip:
                # 处理X-Forwarded-For可能包含多个IP的情况
                if "," in ip:
                    ip = ip.split(",")[0].strip()
                return ip
        
        return None


# 全局实例
_token_validator = None
_session_manager = None


def get_token_validator() -> TokenValidator:
    """获取Token验证器实例（单例模式）"""
    global _token_validator
    if _token_validator is None:
        _token_validator = TokenValidator()
    return _token_validator


def get_session_manager() -> SessionManager:
    """获取会话管理器实例（单例模式）"""
    global _session_manager
    if _session_manager is None:
        _session_manager = SessionManager()
    return _session_manager


def validate_token(token: str, client_ip: Optional[str] = None) -> bool:
    """验证Token是否有效
    
    Args:
        token: 待验证的Token
        client_ip: 客户端IP地址
        
    Returns:
        bool: Token是否有效
        
    Raises:
        AuthenticationError: 当Token无效时抛出
    """
    try:
        validator = get_token_validator()
        return validator.validate_token(token, client_ip)
    except ConfigurationError:
        raise
    except Exception as e:
        logger.error(f"Token验证过程中发生错误: {e}")
        raise AuthenticationError(f"Token验证失败: {str(e)}")


def authenticate_request(context_meta: Dict[str, Any]) -> str:
    """认证请求并返回有效的Token
    
    Args:
        context_meta: MCP上下文元数据
        
    Returns:
        str: 有效的Token
        
    Raises:
        AuthenticationError: 当认证失败时抛出
    """
    try:
        validator = get_token_validator()
        
        # 从上下文中提取Token和客户端IP
        token = validator.extract_token_from_context(context_meta)
        client_ip = validator.get_client_ip(context_meta)
        
        if not settings.enable_auth:
            logger.debug("认证已禁用，跳过Token验证")
            return "auth_disabled"
        
        if not token:
            raise AuthenticationError("缺少认证Token，请在请求头中提供Authorization或X-API-Key")
        
        # 验证Token
        if not validator.validate_token(token, client_ip):
            raise AuthenticationError("无效的认证Token")
        
        return token
        
    except AuthenticationError:
        raise
    except Exception as e:
        logger.error(f"请求认证过程中发生错误: {e}")
        raise AuthenticationError(f"认证处理失败: {str(e)}")


class AuthenticationMiddleware:
    """认证中间件类，支持更复杂的认证逻辑"""
    
    def __init__(self):
        self.validator = get_token_validator()
        self.session_manager = get_session_manager()
        self._request_count = 0
        self._start_time = time.time()
    
    async def __call__(self, ctx, call_next):
        """中间件调用方法"""
        request_start = time.time()
        self._request_count += 1
        request_id = f"req_{self._request_count}"
        
        try:
            # 记录请求开始
            logger.debug(f"[{request_id}] 开始处理认证")
            
            # 认证请求
            token = authenticate_request(ctx.meta)
            
            # 将认证信息添加到上下文
            ctx.auth_token = token
            ctx.request_id = request_id
            ctx.authenticated = True
            
            # 继续处理请求
            result = await call_next(ctx)
            
            # 记录请求完成
            duration = time.time() - request_start
            logger.debug(f"[{request_id}] 认证处理完成，耗时: {duration:.3f}s")
            
            return result
            
        except AuthenticationError as e:
            duration = time.time() - request_start
            logger.warning(f"[{request_id}] 认证失败: {e}, 耗时: {duration:.3f}s")
            raise
        except Exception as e:
            duration = time.time() - request_start
            logger.error(f"[{request_id}] 认证中间件错误: {e}, 耗时: {duration:.3f}s")
            raise AuthenticationError(f"认证处理失败: {str(e)}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取认证统计信息"""
        uptime = time.time() - self._start_time
        return {
            "request_count": self._request_count,
            "uptime_seconds": uptime,
            "requests_per_second": self._request_count / uptime if uptime > 0 else 0,
            "auth_enabled": settings.enable_auth,
        }


def create_auth_middleware() -> AuthenticationMiddleware:
    """创建认证中间件实例
    
    Returns:
        AuthenticationMiddleware: 认证中间件实例
    """
    return AuthenticationMiddleware()


# RetryableAuthenticator 已移除，重试逻辑已集成到 SessionManager 中


# 向后兼容的函数（用于FastAPI）
# 向后兼容的函数已移除，因为MCP服务不使用FastAPI
