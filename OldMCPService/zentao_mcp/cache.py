"""
缓存系统模块

实现内存缓存，支持TTL和LRU策略，专门为禅道MCP服务优化
"""

import time
import logging
import asyncio
import hashlib
from typing import Any, Dict, Optional, Tuple, List, Callable, Union
from collections import OrderedDict
from threading import Lock
from dataclasses import dataclass
from enum import Enum

from .config import settings

logger = logging.getLogger(__name__)


class CacheStrategy(Enum):
    """缓存策略枚举"""
    LRU = "LRU"  # 最近最少使用
    LFU = "LFU"  # 最少使用频率
    FIFO = "FIFO"  # 先进先出


@dataclass
class CacheMetrics:
    """缓存指标数据类"""
    hits: int = 0
    misses: int = 0
    sets: int = 0
    deletes: int = 0
    evictions: int = 0
    expired_cleanups: int = 0
    
    @property
    def total_requests(self) -> int:
        return self.hits + self.misses
    
    @property
    def hit_rate(self) -> float:
        if self.total_requests == 0:
            return 0.0
        return (self.hits / self.total_requests) * 100


class CacheItem:
    """缓存项"""
    
    def __init__(self, value: Any, ttl: int, tags: Optional[List[str]] = None):
        self.value = value
        self.created_at = time.time()
        self.ttl = ttl
        self.access_count = 1
        self.last_access = time.time()
        self.tags = tags or []  # 用于批量清理
        self.size = self._calculate_size(value)
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl <= 0:
            return False  # 永不过期
        return time.time() - self.created_at > self.ttl
    
    def touch(self):
        """更新访问时间和计数"""
        self.last_access = time.time()
        self.access_count += 1
    
    def _calculate_size(self, value: Any) -> int:
        """估算缓存项大小（字节）"""
        try:
            import sys
            return sys.getsizeof(value)
        except:
            return 1024  # 默认1KB


class MemoryCache:
    """内存缓存实现，支持多种淘汰策略和高级功能"""
    
    def __init__(self, 
                 max_size: int = 1000, 
                 default_ttl: int = 300,
                 strategy: CacheStrategy = CacheStrategy.LRU,
                 max_memory_mb: int = 100):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.strategy = strategy
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        
        self._cache: OrderedDict[str, CacheItem] = OrderedDict()
        self._lock = Lock()
        self._metrics = CacheMetrics()
        self._tag_index: Dict[str, set] = {}  # 标签索引
        
        # 性能统计
        self._total_memory_used = 0
        self._last_cleanup_time = time.time()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key not in self._cache:
                self._metrics.misses += 1
                logger.debug(f"缓存未命中: {key}")
                return None
            
            item = self._cache[key]
            
            # 检查是否过期
            if item.is_expired():
                self._remove_item(key)
                self._metrics.misses += 1
                self._metrics.expired_cleanups += 1
                logger.debug(f"缓存过期: {key}")
                return None
            
            # 更新访问信息
            item.touch()
            
            # 根据策略调整位置
            if self.strategy == CacheStrategy.LRU:
                self._cache.move_to_end(key)
            
            self._metrics.hits += 1
            logger.debug(f"缓存命中: {key}")
            return item.value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None, tags: Optional[List[str]] = None) -> None:
        """设置缓存值"""
        if ttl is None:
            ttl = self.default_ttl
        
        with self._lock:
            # 创建缓存项
            item = CacheItem(value, ttl, tags)
            
            # 如果已存在，先移除旧项
            if key in self._cache:
                self._remove_item(key)
            
            # 检查内存限制
            if self._total_memory_used + item.size > self.max_memory_bytes:
                self._evict_by_memory()
            
            # 检查容量限制
            if len(self._cache) >= self.max_size:
                self._evict_by_strategy()
            
            # 添加新项
            self._cache[key] = item
            self._total_memory_used += item.size
            self._metrics.sets += 1
            
            # 更新标签索引
            if tags:
                for tag in tags:
                    if tag not in self._tag_index:
                        self._tag_index[tag] = set()
                    self._tag_index[tag].add(key)
            
            # 根据策略调整位置
            if self.strategy == CacheStrategy.LRU:
                self._cache.move_to_end(key)
            
            logger.debug(f"缓存添加: {key}, TTL: {ttl}s, 标签: {tags}")
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        with self._lock:
            if key in self._cache:
                self._remove_item(key)
                self._metrics.deletes += 1
                logger.debug(f"缓存删除: {key}")
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._tag_index.clear()
            self._total_memory_used = 0
            self._metrics = CacheMetrics()
            logger.info("缓存已清空")
    
    def cleanup_expired(self) -> int:
        """清理过期项"""
        expired_keys = []
        
        with self._lock:
            current_time = time.time()
            for key, item in self._cache.items():
                if item.is_expired():
                    expired_keys.append(key)
            
            for key in expired_keys:
                self._remove_item(key)
            
            self._last_cleanup_time = current_time
            self._metrics.expired_cleanups += len(expired_keys)
        
        if expired_keys:
            logger.info(f"清理过期缓存项: {len(expired_keys)} 个")
        
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            memory_usage_mb = self._total_memory_used / (1024 * 1024)
            memory_usage_percent = (self._total_memory_used / self.max_memory_bytes) * 100
            
            return {
                "size": len(self._cache),
                "max_size": self.max_size,
                "hits": self._metrics.hits,
                "misses": self._metrics.misses,
                "hit_rate": round(self._metrics.hit_rate, 2),
                "total_requests": self._metrics.total_requests,
                "sets": self._metrics.sets,
                "deletes": self._metrics.deletes,
                "evictions": self._metrics.evictions,
                "expired_cleanups": self._metrics.expired_cleanups,
                "memory_usage_mb": round(memory_usage_mb, 2),
                "memory_usage_percent": round(memory_usage_percent, 2),
                "strategy": self.strategy.value,
                "tags_count": len(self._tag_index),
                "last_cleanup": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(self._last_cleanup_time))
            }
    
    def delete_by_tag(self, tag: str) -> int:
        """根据标签删除缓存项"""
        with self._lock:
            if tag not in self._tag_index:
                logger.debug(f"标签 '{tag}' 不存在于索引中")
                return 0
            
            keys_to_delete = list(self._tag_index[tag])
            deleted_count = 0
            
            for key in keys_to_delete:
                if key in self._cache:
                    self._remove_item(key)
                    deleted_count += 1
            
            # 清理标签索引（如果还存在的话）
            if tag in self._tag_index:
                del self._tag_index[tag]
            
            logger.info(f"根据标签 '{tag}' 删除了 {deleted_count} 个缓存项")
            return deleted_count
    
    def delete_by_pattern(self, pattern: str) -> int:
        """根据键模式删除缓存项（支持通配符）"""
        import fnmatch
        
        with self._lock:
            keys_to_delete = []
            for key in self._cache.keys():
                if fnmatch.fnmatch(key, pattern):
                    keys_to_delete.append(key)
            
            for key in keys_to_delete:
                self._remove_item(key)
            
            logger.info(f"根据模式 '{pattern}' 删除了 {len(keys_to_delete)} 个缓存项")
            return len(keys_to_delete)
    
    def get_keys_by_tag(self, tag: str) -> List[str]:
        """获取指定标签的所有键"""
        with self._lock:
            if tag not in self._tag_index:
                return []
            return list(self._tag_index[tag])
    
    def _remove_item(self, key: str) -> None:
        """内部方法：移除缓存项并更新索引"""
        if key not in self._cache:
            return
        
        item = self._cache[key]
        
        # 更新内存使用量
        self._total_memory_used -= item.size
        
        # 从标签索引中移除
        for tag in item.tags:
            if tag in self._tag_index:
                self._tag_index[tag].discard(key)
                # 注意：不在这里删除空的标签索引，让delete_by_tag方法处理
        
        # 删除缓存项
        del self._cache[key]
    
    def _evict_by_strategy(self) -> None:
        """根据策略淘汰缓存项"""
        if not self._cache:
            return
        
        if self.strategy == CacheStrategy.LRU:
            # 删除最近最少使用的项（队列头部）
            oldest_key = next(iter(self._cache))
            self._remove_item(oldest_key)
            
        elif self.strategy == CacheStrategy.LFU:
            # 删除使用频率最低的项
            min_access_count = min(item.access_count for item in self._cache.values())
            for key, item in self._cache.items():
                if item.access_count == min_access_count:
                    self._remove_item(key)
                    break
                    
        elif self.strategy == CacheStrategy.FIFO:
            # 删除最早添加的项
            oldest_key = next(iter(self._cache))
            self._remove_item(oldest_key)
        
        self._metrics.evictions += 1
        logger.debug(f"根据策略 {self.strategy.value} 淘汰了一个缓存项")
    
    def _evict_by_memory(self) -> None:
        """根据内存使用量淘汰缓存项"""
        # 淘汰直到内存使用量降到80%以下
        target_memory = self.max_memory_bytes * 0.8
        
        while self._total_memory_used > target_memory and self._cache:
            self._evict_by_strategy()
        
        logger.debug(f"内存淘汰完成，当前使用: {self._total_memory_used / (1024*1024):.2f}MB")
    
    def should_cleanup(self) -> bool:
        """判断是否需要清理过期项"""
        # 每5分钟或者缓存项超过一定数量时进行清理
        return (time.time() - self._last_cleanup_time > 300 or 
                len(self._cache) > self.max_size * 0.8)


# 全局缓存实例
_global_cache: Optional[MemoryCache] = None
_cache_lock = Lock()


def get_cache() -> MemoryCache:
    """获取全局缓存实例"""
    global _global_cache
    
    if _global_cache is None:
        with _cache_lock:
            if _global_cache is None:  # 双重检查
                strategy = CacheStrategy(settings.cache_strategy)
                _global_cache = MemoryCache(
                    max_size=settings.cache_max_size,
                    default_ttl=settings.cache_ttl,
                    strategy=strategy,
                    max_memory_mb=100  # 默认100MB内存限制
                )
                logger.info(f"初始化全局缓存，策略: {strategy.value}, 最大条目: {settings.cache_max_size}")
    
    return _global_cache


def cache_key(*args, **kwargs) -> str:
    """生成缓存键，支持更复杂的参数"""
    key_parts = []
    
    # 处理位置参数
    for arg in args:
        if isinstance(arg, (list, tuple)):
            key_parts.append(f"[{','.join(str(x) for x in arg)}]")
        elif isinstance(arg, dict):
            # 对字典进行排序以确保一致性
            sorted_items = sorted(arg.items())
            key_parts.append(f"{{{','.join(f'{k}:{v}' for k, v in sorted_items)}}}")
        else:
            key_parts.append(str(arg))
    
    # 处理关键字参数
    if kwargs:
        sorted_kwargs = sorted(kwargs.items())
        kwargs_str = ','.join(f"{k}={v}" for k, v in sorted_kwargs)
        key_parts.append(f"({kwargs_str})")
    
    return ":".join(key_parts)


def cache_key_hash(*args, **kwargs) -> str:
    """生成缓存键的哈希版本（用于长键）"""
    key = cache_key(*args, **kwargs)
    if len(key) > 200:  # 如果键太长，使用哈希
        return hashlib.md5(key.encode('utf-8')).hexdigest()
    return key


# 全局锁用于并发控制
_function_call_locks: Dict[str, asyncio.Lock] = {}
_locks_lock = asyncio.Lock()


async def cached_call(key: str, func: Callable, *args, 
                     ttl: Optional[int] = None, 
                     tags: Optional[List[str]] = None,
                     **kwargs) -> Any:
    """缓存装饰器函数，支持并发安全"""
    if not settings.enable_cache:
        if asyncio.iscoroutinefunction(func):
            return await func(*args, **kwargs)
        else:
            return func(*args, **kwargs)
    
    cache = get_cache()
    
    # 自动清理过期项
    if cache.should_cleanup():
        cache.cleanup_expired()
    
    # 尝试从缓存获取
    cached_value = cache.get(key)
    if cached_value is not None:
        return cached_value
    
    # 获取或创建函数调用锁
    async with _locks_lock:
        if key not in _function_call_locks:
            _function_call_locks[key] = asyncio.Lock()
        func_lock = _function_call_locks[key]
    
    # 使用锁确保同一个键的函数只被调用一次
    async with func_lock:
        # 再次检查缓存（可能在等待锁的过程中被其他协程填充了）
        cached_value = cache.get(key)
        if cached_value is not None:
            return cached_value
        
        # 缓存未命中，调用函数
        if asyncio.iscoroutinefunction(func):
            result = await func(*args, **kwargs)
        else:
            result = func(*args, **kwargs)
        
        # 存储到缓存
        cache.set(key, result, ttl, tags)
        
        return result


def cached_sync_call(key: str, func: Callable, *args,
                    ttl: Optional[int] = None,
                    tags: Optional[List[str]] = None,
                    **kwargs) -> Any:
    """同步版本的缓存装饰器函数"""
    if not settings.enable_cache:
        return func(*args, **kwargs)
    
    cache = get_cache()
    
    # 自动清理过期项
    if cache.should_cleanup():
        cache.cleanup_expired()
    
    # 尝试从缓存获取
    cached_value = cache.get(key)
    if cached_value is not None:
        return cached_value
    
    # 缓存未命中，调用函数
    result = func(*args, **kwargs)
    
    # 存储到缓存
    cache.set(key, result, ttl, tags)
    
    return result


# 禅道特定的缓存键生成器
class ZentaoCacheKeys:
    """禅道MCP服务专用的缓存键生成器"""
    
    @staticmethod
    def departments() -> str:
        """部门列表缓存键"""
        return "zentao:departments:all"
    
    @staticmethod
    def department_users(dept_id: int) -> str:
        """部门用户列表缓存键"""
        return f"zentao:department:{dept_id}:users"
    
    @staticmethod
    def projects() -> str:
        """项目列表缓存键"""
        return "zentao:projects:all"
    
    @staticmethod
    def project_detail(project_id: int) -> str:
        """项目详情缓存键"""
        return f"zentao:project:{project_id}:detail"
    
    @staticmethod
    def project_stories(project_id: int) -> str:
        """项目需求列表缓存键"""
        return f"zentao:project:{project_id}:stories"
    
    @staticmethod
    def project_tasks(project_id: int) -> str:
        """项目任务列表缓存键"""
        return f"zentao:project:{project_id}:tasks"
    
    @staticmethod
    def project_bugs(project_id: int) -> str:
        """项目Bug列表缓存键"""
        return f"zentao:project:{project_id}:bugs"
    
    @staticmethod
    def story_detail(story_id: int) -> str:
        """需求详情缓存键"""
        return f"zentao:story:{story_id}:detail"
    
    @staticmethod
    def task_detail(task_id: int) -> str:
        """任务详情缓存键"""
        return f"zentao:task:{task_id}:detail"
    
    @staticmethod
    def bug_detail(bug_id: int) -> str:
        """Bug详情缓存键"""
        return f"zentao:bug:{bug_id}:detail"
    
    @staticmethod
    def user_detail(user_id: int) -> str:
        """用户详情缓存键"""
        return f"zentao:user:{user_id}:detail"
    
    @staticmethod
    def bugs_by_time_range(start_date: str, end_date: str) -> str:
        """时间范围Bug列表缓存键"""
        return f"zentao:bugs:time_range:{start_date}:{end_date}"
    
    @staticmethod
    def bugs_by_dept_time(dept_id: int, start_date: str, end_date: str) -> str:
        """部门时间范围Bug列表缓存键"""
        return f"zentao:bugs:dept:{dept_id}:time_range:{start_date}:{end_date}"


# 禅道特定的缓存标签
class ZentaoCacheTags:
    """禅道MCP服务专用的缓存标签"""
    
    DEPARTMENTS = "departments"
    PROJECTS = "projects"
    STORIES = "stories"
    TASKS = "tasks"
    BUGS = "bugs"
    USERS = "users"
    
    @staticmethod
    def project_related(project_id: int) -> str:
        """项目相关数据标签"""
        return f"project:{project_id}"
    
    @staticmethod
    def department_related(dept_id: int) -> str:
        """部门相关数据标签"""
        return f"department:{dept_id}"
    
    @staticmethod
    def time_range_related(start_date: str, end_date: str) -> str:
        """时间范围相关数据标签"""
        return f"time_range:{start_date}:{end_date}"


def invalidate_project_cache(project_id: int) -> int:
    """清理指定项目的相关缓存"""
    cache = get_cache()
    tag = ZentaoCacheTags.project_related(project_id)
    return cache.delete_by_tag(tag)


def invalidate_department_cache(dept_id: int) -> int:
    """清理指定部门的相关缓存"""
    cache = get_cache()
    tag = ZentaoCacheTags.department_related(dept_id)
    return cache.delete_by_tag(tag)


def get_cache_stats() -> Dict[str, Any]:
    """获取缓存统计信息的便捷函数"""
    cache = get_cache()
    return cache.get_stats()