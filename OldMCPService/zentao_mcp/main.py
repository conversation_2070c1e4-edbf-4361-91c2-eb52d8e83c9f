"""
禅道MCP服务器主入口
基于FastMCP框架实现的Model Context Protocol服务器
"""
import asyncio
import logging
import os
import signal
import sys
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager

from fastmcp import FastMCP, Context

from .config import settings
from .auth import authenticate_request, AuthenticationError
from .client import ZentaoClient
from .exceptions import ZentaoMCPError
from .logging_config import setup_logging, get_logger, request_tracker, performance_metrics
from .health_check import health_checker


class ZentaoMCPServer:
    """禅道MCP服务器主类"""
    
    def __init__(self, name: str = None):
        """初始化MCP服务器
        
        Args:
            name: 服务器名称，默认使用配置中的名称
        """
        self.name = name or "ZentaoMCP"
        self.zentao_client: Optional[ZentaoClient] = None
        self._shutdown_event = asyncio.Event()
        self._setup_logging()
        
        # 设置信号处理器
        self._setup_signal_handlers()
        
        # 创建生命周期管理器
        lifespan_manager = self._create_lifespan()
        
        # 创建FastMCP实例
        self.mcp = FastMCP(
            name=self.name,
            instructions="""
            这是一个禅道(Zentao)项目管理系统的MCP服务器。
            提供访问禅道系统中的项目、任务、Bug、需求等数据的工具和资源。
            支持查询部门信息、项目列表、Bug详情、任务状态等功能。
            """,
            lifespan=lifespan_manager
        )
        
        # 设置中间件 - 暂时禁用
        # self._setup_middleware()
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        self._signal_received = False
        
        def signal_handler(signum, frame):
            """信号处理函数"""
            if self._signal_received:
                # 如果已经收到信号，强制退出
                self.logger.warning("收到多次中断信号，强制退出")
                os._exit(1)
            
            self._signal_received = True
            signal_name = signal.Signals(signum).name
            self.logger.info(f"收到信号 {signal_name} ({signum})，准备关闭服务器...")
            
            # 直接强制退出，避免循环
            try:
                self.logger.info("正在强制关闭服务器...")
                os._exit(0)
            except:
                sys.exit(0)
        
        # 注册信号处理器
        if hasattr(signal, 'SIGINT'):
            signal.signal(signal.SIGINT, signal_handler)
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, signal_handler)
    
    async def _graceful_shutdown(self):
        """优雅关闭"""
        try:
            await self.shutdown()
        except Exception as e:
            self.logger.error(f"关闭过程中发生错误: {e}")
        finally:
            # 强制退出
            os._exit(0)
    
    def _setup_logging(self):
        """配置日志系统"""
        # 使用新的结构化日志系统
        setup_logging(
            log_level=settings.log_level,
            log_dir=settings.log_dir,
            json_format=getattr(settings, 'log_json_format', False),
            enable_file_logging=True,
            enable_console_logging=True
        )
        
        self.logger = get_logger(__name__)
        import tempfile
        
        # 确定日志目录路径
        log_dir = settings.log_dir
        
        # 如果是相对路径，转换为绝对路径
        if not os.path.isabs(log_dir):
            # 获取项目根目录（main.py的上级目录的上级目录）
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)
            log_dir = os.path.join(project_root, log_dir)
        
        # 尝试创建日志目录，如果失败则使用临时目录
        try:
            os.makedirs(log_dir, exist_ok=True)
        except (OSError, PermissionError) as e:
            # 如果无法创建日志目录，使用临时目录
            log_dir = tempfile.gettempdir()
            print(f"警告: 无法创建日志目录 {settings.log_dir}，使用临时目录 {log_dir}: {e}")
        
        # 配置日志处理器
        handlers = [logging.StreamHandler()]
        
        # 尝试添加文件处理器
        try:
            log_file = os.path.join(log_dir, "zentao_mcp.log")
            handlers.append(logging.FileHandler(log_file))
        except (OSError, PermissionError) as e:
            print(f"警告: 无法创建日志文件，仅使用控制台输出: {e}")
        
        logging.basicConfig(
            level=getattr(logging, settings.log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=handlers
        )
        self.logger = logging.getLogger(self.name)
        self.logger.info(f"禅道MCP服务器 {self.name} 初始化完成")
    
    def _setup_middleware(self):
        """设置中间件"""
        from fastmcp.server.middleware import Middleware, MiddlewareContext
        
        class AuthMiddleware(Middleware):
            def __init__(self, server_instance):
                self.server = server_instance
            
            async def on_message(self, context: MiddlewareContext, call_next):
                """认证中间件"""
                try:
                    # 从上下文中获取认证信息
                    # 注意：在FastMCP中，认证信息可能通过不同方式传递
                    # 这里我们先简化处理，后续可以根据实际需要调整
                    self.server.logger.debug("处理认证中间件")
                    
                    # 继续处理请求
                    return await call_next(context)
                    
                except AuthenticationError as e:
                    self.server.logger.warning(f"认证失败: {e}")
                    raise
                except Exception as e:
                    self.server.logger.error(f"认证中间件错误: {e}")
                    raise ZentaoMCPError(f"认证处理失败: {str(e)}")
        
        # 添加认证中间件
        self.mcp.add_middleware(AuthMiddleware(self))
    
    def _create_lifespan(self):
        """创建生命周期管理器"""
        from contextlib import asynccontextmanager
        
        @asynccontextmanager
        async def lifespan(server):
            """生命周期管理器"""
            # 启动时的初始化操作
            await self.startup()
            try:
                yield
            finally:
                # 关闭时的清理操作
                await self.shutdown()
        
        return lifespan
    
    async def _get_zentao_client(self) -> ZentaoClient:
        """获取禅道客户端实例"""
        if self.zentao_client is None:
            self.zentao_client = ZentaoClient()
            await self.zentao_client.initialize()
        return self.zentao_client
    
    @asynccontextmanager
    async def _client_context(self):
        """禅道客户端上下文管理器"""
        client = await self._get_zentao_client()
        try:
            yield client
        except Exception as e:
            self.logger.error(f"禅道客户端操作失败: {e}")
            raise ZentaoMCPError(f"禅道API调用失败: {str(e)}")
    
    def register_resources(self):
        """注册MCP资源"""
        try:
            # 注册部门资源
            from .resources.department import register_department_resources
            register_department_resources(self.mcp)
            
            # 注册用户资源
            from .resources.user import register_user_resources
            register_user_resources(self.mcp)
            
            # 注册项目资源
            from .resources.project import register_project_resources
            register_project_resources(self.mcp)
            
            # 注册需求资源
            from .resources.story import register_story_resources
            register_story_resources(self.mcp)
            
            # 注册任务资源
            from .resources.task import register_task_resources
            register_task_resources(self.mcp)
            
            # 注册Bug资源
            from .resources.bug import register_bug_resources
            register_bug_resources(self.mcp)
            
            self.logger.info("资源注册完成")
        except Exception as e:
            self.logger.error(f"资源注册失败: {e}")
            raise
    
    def register_tools(self):
        """注册MCP工具"""
        try:
            # 使用新的工具架构注册所有工具
            from .tools import register_all_tools
            register_all_tools(self.mcp)
            
            self.logger.info("工具注册完成")
        except Exception as e:
            self.logger.error(f"工具注册失败: {e}")
            raise
    
    async def startup(self):
        """服务器启动时的初始化操作"""
        try:
            self.logger.info(f"启动禅道MCP服务器: {self.name}")
            
            # 初始化禅道客户端
            await self._get_zentao_client()
            
            # 设置健康检查器的禅道客户端
            health_checker.set_zentao_client(self.zentao_client)
            
            # 启动健康检查
            await health_checker.start_periodic_checks()
            
            # 注册资源和工具
            self.register_resources()
            self.register_tools()
            
            # 执行初始健康检查
            await health_checker.check_all_components()
            
            self.logger.info("禅道MCP服务器启动完成")
            
        except Exception as e:
            self.logger.error(f"服务器启动失败: {e}")
            raise ZentaoMCPError(f"服务器启动失败: {str(e)}")
    
    async def shutdown(self):
        """服务器关闭时的清理操作"""
        try:
            self.logger.info("正在关闭禅道MCP服务器...")
            
            # 停止健康检查
            await health_checker.stop_periodic_checks()
            
            # 关闭禅道客户端连接
            if self.zentao_client:
                await self.zentao_client.close()
                self.zentao_client = None
            
            # 输出最终的性能指标
            final_metrics = performance_metrics.get_metrics()
            self.logger.info("服务器运行统计", extra=final_metrics)
            
            self.logger.info("禅道MCP服务器已关闭")
            
        except Exception as e:
            self.logger.error(f"服务器关闭时发生错误: {e}")
    
    def run(self, transport: str = None, **kwargs):
        """运行MCP服务器
        
        Args:
            transport: 传输协议，默认使用配置中的协议
            **kwargs: 其他运行参数
        """
        transport = transport or settings.mcp_transport
        
        try:
            self.logger.info(f"使用 {transport} 协议启动MCP服务器")
            
            # 使用FastMCP的run方法
            if transport == "stdio":
                self.mcp.run(**kwargs)
            elif transport == "http":
                # HTTP传输模式
                host = kwargs.get("host", "127.0.0.1")
                port = kwargs.get("port", 8000)
                self.mcp.run(transport="http", host=host, port=port, **kwargs)
            else:
                raise ZentaoMCPError(f"不支持的传输协议: {transport}")
                
        except KeyboardInterrupt:
            self.logger.info("收到键盘中断信号，正在关闭服务器...")
            # 触发优雅关闭
            asyncio.run(self._graceful_shutdown())
        except SystemExit:
            self.logger.info("收到系统退出信号")
        except Exception as e:
            self.logger.error(f"服务器运行失败: {e}")
            # 确保在异常情况下也能清理资源
            try:
                asyncio.run(self._graceful_shutdown())
            except:
                pass
            raise
        finally:
            self.logger.info("服务器已停止")


# 创建全局服务器实例
server = ZentaoMCPServer()

# 导出FastMCP实例供外部使用
mcp = server.mcp


def main():
    """主入口函数"""
    try:
        server.run()
    except Exception as e:
        logging.error(f"服务器启动失败: {e}")
        return 1
    return 0


if __name__ == "__main__":
    exit(main())
