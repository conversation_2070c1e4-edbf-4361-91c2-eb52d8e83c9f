"""
健康检查和服务状态监控模块
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from enum import Enum

from .config import settings
from .logging_config import get_logger, performance_metrics


logger = get_logger(__name__)


class HealthStatus(Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


class ComponentHealth:
    """组件健康状态"""
    
    def __init__(self, name: str, status: HealthStatus = HealthStatus.UNKNOWN, 
                 message: str = "", details: Optional[Dict[str, Any]] = None):
        self.name = name
        self.status = status
        self.message = message
        self.details = details or {}
        self.last_check = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "name": self.name,
            "status": self.status.value,
            "message": self.message,
            "details": self.details,
            "last_check": self.last_check.isoformat()
        }


class HealthChecker:
    """健康检查器"""
    
    def __init__(self):
        self.components: Dict[str, ComponentHealth] = {}
        self.overall_status = HealthStatus.UNKNOWN
        self.last_full_check = None
        self.check_interval = 30  # 30秒检查一次
        self._running = False
        self._check_task = None
    
    def register_component(self, name: str, check_func: callable) -> None:
        """注册组件健康检查函数"""
        self.components[name] = ComponentHealth(name)
        setattr(self, f"_check_{name}", check_func)
    
    async def check_component(self, name: str) -> ComponentHealth:
        """检查单个组件健康状态"""
        if name not in self.components:
            return ComponentHealth(name, HealthStatus.UNKNOWN, "组件未注册")
        
        check_func = getattr(self, f"_check_{name}", None)
        if not check_func:
            return ComponentHealth(name, HealthStatus.UNKNOWN, "检查函数未找到")
        
        try:
            start_time = time.time()
            result = await check_func()
            duration = time.time() - start_time
            
            if isinstance(result, ComponentHealth):
                result.details["check_duration"] = duration
                self.components[name] = result
                return result
            else:
                # 如果返回布尔值，转换为ComponentHealth
                status = HealthStatus.HEALTHY if result else HealthStatus.UNHEALTHY
                message = "检查通过" if result else "检查失败"
                component = ComponentHealth(name, status, message, {"check_duration": duration})
                self.components[name] = component
                return component
                
        except Exception as e:
            logger.error(f"组件 {name} 健康检查失败", extra={"error": str(e), "component": name})
            component = ComponentHealth(
                name, 
                HealthStatus.UNHEALTHY, 
                f"检查异常: {str(e)}", 
                {"exception": str(e)}
            )
            self.components[name] = component
            return component
    
    async def check_all_components(self) -> Dict[str, ComponentHealth]:
        """检查所有组件健康状态"""
        logger.debug("开始全面健康检查")
        
        # 并发检查所有组件，正确处理异常
        tasks = []
        for name in self.components.keys():
            tasks.append(self.check_component(name))
        
        if tasks:
            # 使用return_exceptions=True确保异常被正确处理
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 检查是否有未处理的异常
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    component_name = list(self.components.keys())[i]
                    logger.error(f"组件 {component_name} 检查时发生未捕获异常", exc_info=result)
        
        # 计算整体健康状态
        self._calculate_overall_status()
        self.last_full_check = datetime.now()
        
        logger.info(
            "健康检查完成",
            extra={
                "overall_status": self.overall_status.value,
                "component_count": len(self.components),
                "healthy_count": sum(1 for c in self.components.values() if c.status == HealthStatus.HEALTHY),
                "unhealthy_count": sum(1 for c in self.components.values() if c.status == HealthStatus.UNHEALTHY)
            }
        )
        
        return self.components.copy()
    
    def _calculate_overall_status(self) -> None:
        """计算整体健康状态"""
        if not self.components:
            self.overall_status = HealthStatus.UNKNOWN
            return
        
        statuses = [component.status for component in self.components.values()]
        
        if all(status == HealthStatus.HEALTHY for status in statuses):
            self.overall_status = HealthStatus.HEALTHY
        elif any(status == HealthStatus.UNHEALTHY for status in statuses):
            self.overall_status = HealthStatus.UNHEALTHY
        else:
            self.overall_status = HealthStatus.DEGRADED
    
    async def start_periodic_checks(self) -> None:
        """启动定期健康检查"""
        if self._running:
            return
        
        self._running = True
        self._check_task = asyncio.create_task(self._periodic_check_loop())
        logger.info(f"启动定期健康检查，间隔: {self.check_interval}秒")
    
    async def stop_periodic_checks(self) -> None:
        """停止定期健康检查"""
        self._running = False
        if self._check_task:
            self._check_task.cancel()
            try:
                await self._check_task
            except asyncio.CancelledError:
                pass
        logger.info("停止定期健康检查")
    
    async def _periodic_check_loop(self) -> None:
        """定期检查循环"""
        while self._running:
            try:
                await self.check_all_components()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"定期健康检查异常: {e}")
                await asyncio.sleep(self.check_interval)
    
    def get_health_report(self) -> Dict[str, Any]:
        """获取健康报告"""
        return {
            "overall_status": self.overall_status.value,
            "last_check": self.last_full_check.isoformat() if self.last_full_check else None,
            "components": {name: component.to_dict() for name, component in self.components.items()},
            "summary": {
                "total_components": len(self.components),
                "healthy": sum(1 for c in self.components.values() if c.status == HealthStatus.HEALTHY),
                "degraded": sum(1 for c in self.components.values() if c.status == HealthStatus.DEGRADED),
                "unhealthy": sum(1 for c in self.components.values() if c.status == HealthStatus.UNHEALTHY),
                "unknown": sum(1 for c in self.components.values() if c.status == HealthStatus.UNKNOWN)
            },
            "performance_metrics": performance_metrics.get_metrics()
        }


class ZentaoHealthChecker(HealthChecker):
    """禅道MCP服务健康检查器"""
    
    def __init__(self, zentao_client=None):
        super().__init__()
        self.zentao_client = zentao_client
        
        # 注册组件检查函数
        self.register_component("zentao_api", self._check_zentao_api)
        self.register_component("cache_system", self._check_cache_system)
        self.register_component("memory_usage", self._check_memory_usage)
        self.register_component("performance", self._check_performance)
    
    def set_zentao_client(self, client) -> None:
        """设置禅道客户端"""
        self.zentao_client = client
    
    async def _check_zentao_api(self) -> ComponentHealth:
        """检查禅道API连接"""
        if not self.zentao_client:
            return ComponentHealth(
                "zentao_api",
                HealthStatus.UNKNOWN,
                "禅道客户端未初始化"
            )
        
        # 创建专门的健康检查日志记录器，只输出到文件
        health_logger = logging.getLogger("zentao_mcp.health_check.api")
        
        try:
            # 尝试调用一个简单的API来测试连接
            start_time = time.time()
            
            # 记录健康检查请求到文件（不输出到控制台）
            health_logger.debug(
                "开始禅道API健康检查",
                extra={
                    "base_url": self.zentao_client.base_url,
                    "check_type": "api_connectivity"
                }
            )
            
            result = await self.zentao_client.get_all_departments_async()
            duration = time.time() - start_time
            
            # 记录健康检查结果到文件
            health_logger.debug(
                "禅道API健康检查完成",
                extra={
                    "response_time": duration,
                    "success": True,
                    "result_type": type(result).__name__,
                    "has_data": bool(result and isinstance(result, dict) and result.get("data"))
                }
            )
            
            if result and isinstance(result, dict):
                return ComponentHealth(
                    "zentao_api",
                    HealthStatus.HEALTHY,
                    "禅道API连接正常",
                    {
                        "response_time": duration,
                        "base_url": self.zentao_client.base_url,
                        "department_count": len(result.get("data", []))
                    }
                )
            else:
                return ComponentHealth(
                    "zentao_api",
                    HealthStatus.DEGRADED,
                    "禅道API响应异常",
                    {"response_time": duration}
                )
                
        except Exception as e:
            # 记录健康检查异常到文件
            health_logger.error(
                "禅道API健康检查失败",
                extra={
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "base_url": getattr(self.zentao_client, 'base_url', 'unknown')
                },
                exc_info=True
            )
            
            return ComponentHealth(
                "zentao_api",
                HealthStatus.UNHEALTHY,
                f"禅道API连接失败: {str(e)}",
                {"error": str(e)}
            )
    
    async def _check_cache_system(self) -> ComponentHealth:
        """检查缓存系统"""
        try:
            from .cache import get_cache
            cache = get_cache()
            
            # 测试缓存读写
            test_key = "health_check_test"
            test_value = {"timestamp": time.time()}
            
            # 写入测试
            cache.set(test_key, test_value, ttl=10)
            
            # 读取测试
            cached_value = cache.get(test_key)
            
            if cached_value == test_value:
                cache_stats = cache.get_stats()
                return ComponentHealth(
                    "cache_system",
                    HealthStatus.HEALTHY,
                    "缓存系统正常",
                    {
                        "cache_size": cache_stats.get("size", 0),
                        "hit_rate": cache_stats.get("hit_rate", 0),
                        "max_size": cache_stats.get("max_size", 0)
                    }
                )
            else:
                return ComponentHealth(
                    "cache_system",
                    HealthStatus.DEGRADED,
                    "缓存读写不一致"
                )
                
        except Exception as e:
            return ComponentHealth(
                "cache_system",
                HealthStatus.UNHEALTHY,
                f"缓存系统异常: {str(e)}",
                {"error": str(e)}
            )
    
    async def _check_memory_usage(self) -> ComponentHealth:
        """检查内存使用情况"""
        try:
            import psutil
            
            # 获取当前进程的内存使用情况
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_percent = process.memory_percent()
            
            # 获取系统内存使用情况
            system_memory = psutil.virtual_memory()
            
            status = HealthStatus.HEALTHY
            message = "内存使用正常"
            
            # 判断内存使用状态
            if memory_percent > 80:
                status = HealthStatus.UNHEALTHY
                message = "进程内存使用过高"
            elif memory_percent > 60:
                status = HealthStatus.DEGRADED
                message = "进程内存使用较高"
            elif system_memory.percent > 90:
                status = HealthStatus.DEGRADED
                message = "系统内存使用过高"
            
            return ComponentHealth(
                "memory_usage",
                status,
                message,
                {
                    "process_memory_mb": memory_info.rss / 1024 / 1024,
                    "process_memory_percent": memory_percent,
                    "system_memory_percent": system_memory.percent,
                    "system_available_mb": system_memory.available / 1024 / 1024
                }
            )
            
        except ImportError:
            return ComponentHealth(
                "memory_usage",
                HealthStatus.UNKNOWN,
                "psutil库未安装，无法检查内存使用"
            )
        except Exception as e:
            return ComponentHealth(
                "memory_usage",
                HealthStatus.UNKNOWN,
                f"内存检查异常: {str(e)}",
                {"error": str(e)}
            )
    
    async def _check_performance(self) -> ComponentHealth:
        """检查性能指标"""
        try:
            metrics = performance_metrics.get_metrics()
            
            status = HealthStatus.HEALTHY
            message = "性能指标正常"
            
            # 判断性能状态
            error_rate = metrics.get("error_rate", 0)
            avg_duration = metrics.get("average_duration", 0)
            
            if error_rate > 0.1:  # 错误率超过10%
                status = HealthStatus.UNHEALTHY
                message = "错误率过高"
            elif error_rate > 0.05:  # 错误率超过5%
                status = HealthStatus.DEGRADED
                message = "错误率较高"
            elif avg_duration > 3.0:  # 平均响应时间超过3秒
                status = HealthStatus.DEGRADED
                message = "响应时间较慢"
            
            return ComponentHealth(
                "performance",
                status,
                message,
                metrics
            )
            
        except Exception as e:
            return ComponentHealth(
                "performance",
                HealthStatus.UNKNOWN,
                f"性能检查异常: {str(e)}",
                {"error": str(e)}
            )


# 全局健康检查器实例
health_checker = ZentaoHealthChecker()