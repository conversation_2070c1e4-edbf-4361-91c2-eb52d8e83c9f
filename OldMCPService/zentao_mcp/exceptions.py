"""
禅道MCP服务异常定义
"""

from typing import Optional, Dict, Any


class ZentaoMCPError(Exception):
    """禅道MCP服务基础异常"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.error_code = error_code or "UNKNOWN_ERROR"
        self.details = details or {}
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error": {
                "code": self.error_code,
                "message": self.message,
                "details": self.details
            }
        }


class ZentaoAPIError(ZentaoMCPError):
    """禅道API调用异常"""
    
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict] = None):
        super().__init__(
            message=message,
            error_code="ZENTAO_API_ERROR",
            details={
                "status_code": status_code,
                "response_data": response_data
            }
        )
        self.status_code = status_code
        self.response_data = response_data


class AuthenticationError(ZentaoMCPError):
    """认证异常"""
    
    def __init__(self, message: str = "认证失败"):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR"
        )


class ResourceNotFoundError(ZentaoMCPError):
    """资源未找到异常"""
    
    def __init__(self, resource_type: str, resource_id: Any):
        message = f"{resource_type} ID {resource_id} 不存在"
        super().__init__(
            message=message,
            error_code="RESOURCE_NOT_FOUND",
            details={
                "resource_type": resource_type,
                "resource_id": resource_id
            }
        )


class ValidationError(ZentaoMCPError):
    """数据验证异常"""
    
    def __init__(self, message: str, field: Optional[str] = None, value: Any = None):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details={
                "field": field,
                "value": value
            }
        )


class NetworkError(ZentaoMCPError):
    """网络连接异常"""
    
    def __init__(self, message: str = "网络连接失败"):
        super().__init__(
            message=message,
            error_code="NETWORK_ERROR"
        )


class TimeoutError(ZentaoMCPError):
    """请求超时异常"""
    
    def __init__(self, message: str = "请求超时"):
        super().__init__(
            message=message,
            error_code="TIMEOUT_ERROR"
        )


class RateLimitError(ZentaoMCPError):
    """请求频率限制异常"""
    
    def __init__(self, message: str = "请求频率过高"):
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_ERROR"
        )


class ConfigurationError(ZentaoMCPError):
    """配置错误异常"""
    
    def __init__(self, message: str, config_key: Optional[str] = None):
        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            details={
                "config_key": config_key
            }
        )