"""
禅道数据模型定义

基于ERD文档定义的数据结构，确保与禅道API返回的数据格式一致
"""

from dataclasses import dataclass
from datetime import datetime, date
from typing import Optional, List, Dict, Any
from enum import Enum


class BugStatus(str, Enum):
    """Bug状态枚举"""
    ACTIVE = "active"
    RESOLVED = "resolved"
    CLOSED = "closed"
    RELEASED = "released"


class BugSeverity(int, Enum):
    """Bug严重程度枚举"""
    SMOKE = 0      # 冒烟
    CRITICAL = 1   # 严重
    NORMAL = 2     # 一般
    MINOR = 3      # 次要
    LOW = 4        # 低级


class ProjectStatus(str, Enum):
    """项目状态枚举"""
    DOING = "doing"
    WAIT = "wait"
    DONE = "done"
    SUSPENDED = "suspended"


class StoryStatus(str, Enum):
    """需求状态枚举"""
    ACTIVE = "active"
    CLOSED = "closed"


class StoryStage(str, Enum):
    """需求阶段枚举"""
    WAIT = "wait"
    PLANNED = "planned"
    DEVELOPING = "developing"
    RELEASED = "released"


class TaskStatus(str, Enum):
    """任务状态枚举"""
    WAIT = "wait"
    DOING = "doing"
    DONE = "done"
    CANCEL = "cancel"
    CLOSED = "closed"


class TaskType(str, Enum):
    """任务类型枚举"""
    DEVEL = "devel"
    TEST = "test"
    DESIGN = "design"
    STUDY = "study"
    DISCUSS = "discuss"
    UI = "ui"
    AFFAIR = "affair"
    MISC = "misc"


@dataclass
class BugModel:
    """Bug数据模型"""
    id: int
    title: str
    status: BugStatus
    severity: BugSeverity
    project: int
    projectname: str
    assignedTo: str
    assignedToEmpid: str
    openedBy: str
    openedByEmpid: str
    openedDate: datetime
    resolution: str
    environment: str
    steps: str
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BugModel':
        """从字典创建Bug模型"""
        return cls(
            id=int(data.get('id', 0)),
            title=str(data.get('title', '')),
            status=BugStatus(data.get('status', 'active')),
            severity=BugSeverity(int(data.get('severity', 2))),
            project=int(data.get('project', 0)),
            projectname=str(data.get('projectname', '')),
            assignedTo=str(data.get('assignedTo', '')),
            assignedToEmpid=str(data.get('assignedToEmpid', '')),
            openedBy=str(data.get('openedBy', '')),
            openedByEmpid=str(data.get('openedByEmpid', '')),
            openedDate=datetime.fromisoformat(data.get('openedDate', '1970-01-01T00:00:00')),
            resolution=str(data.get('resolution', '')),
            environment=str(data.get('environment', '')),
            steps=str(data.get('steps', ''))
        )


@dataclass
class ProjectModel:
    """项目数据模型"""
    id: int
    name: str
    code: str
    begin: date
    end: date
    status: ProjectStatus
    parent: int
    openedBy: str
    closedDate: Optional[date]
    closedBy: str
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProjectModel':
        """从字典创建项目模型"""
        return cls(
            id=int(data.get('id', 0)),
            name=str(data.get('name', '')),
            code=str(data.get('code', '')),
            begin=date.fromisoformat(data.get('begin', '1970-01-01')),
            end=date.fromisoformat(data.get('end', '1970-01-01')),
            status=ProjectStatus(data.get('status', 'wait')),
            parent=int(data.get('parent', 0)),
            openedBy=str(data.get('openedBy', '')),
            closedDate=date.fromisoformat(data['closedDate']) if data.get('closedDate') else None,
            closedBy=str(data.get('closedBy', ''))
        )


@dataclass
class StoryModel:
    """需求数据模型"""
    id: int
    title: str
    product: int
    productname: str
    module: int
    modulename: str
    pri: int
    estimate: float
    status: StoryStatus
    stage: StoryStage
    openedBy: str
    openedByEmpid: str
    assignedTo: str
    assignedToEmpid: str
    spec: Optional[str]
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StoryModel':
        """从字典创建需求模型"""
        return cls(
            id=int(data.get('id', 0)),
            title=str(data.get('title', '')),
            product=int(data.get('product', 0)),
            productname=str(data.get('productname', '')),
            module=int(data.get('module', 0)),
            modulename=str(data.get('modulename', '')),
            pri=int(data.get('pri', 3)),
            estimate=float(data.get('estimate', 0.0)),
            status=StoryStatus(data.get('status', 'active')),
            stage=StoryStage(data.get('stage', 'wait')),
            openedBy=str(data.get('openedBy', '')),
            openedByEmpid=str(data.get('openedByEmpid', '')),
            assignedTo=str(data.get('assignedTo', '')),
            assignedToEmpid=str(data.get('assignedToEmpid', '')),
            spec=data.get('spec')
        )


@dataclass
class TaskModel:
    """任务数据模型"""
    id: int
    name: str
    project: int
    projectname: str
    story: int
    storyname: str
    type: TaskType
    status: TaskStatus
    pri: int
    estimate: float
    consumed: float
    left: float
    assignedTo: str
    assignedToEmpid: str
    finishedBy: str
    finishedByEmpid: str
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskModel':
        """从字典创建任务模型"""
        return cls(
            id=int(data.get('id', 0)),
            name=str(data.get('name', '')),
            project=int(data.get('project', 0)),
            projectname=str(data.get('projectname', '')),
            story=int(data.get('story', 0)),
            storyname=str(data.get('storyname', '')),
            type=TaskType(data.get('type', 'devel')),
            status=TaskStatus(data.get('status', 'wait')),
            pri=int(data.get('pri', 3)),
            estimate=float(data.get('estimate', 0.0)),
            consumed=float(data.get('consumed', 0.0)),
            left=float(data.get('left', 0.0)),
            assignedTo=str(data.get('assignedTo', '')),
            assignedToEmpid=str(data.get('assignedToEmpid', '')),
            finishedBy=str(data.get('finishedBy', '')),
            finishedByEmpid=str(data.get('finishedByEmpid', ''))
        )


@dataclass
class DepartmentModel:
    """部门数据模型"""
    id: int
    name: str
    parent: int
    path: str
    grade: int
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DepartmentModel':
        """从字典创建部门模型"""
        return cls(
            id=int(data.get('id', 0)),
            name=str(data.get('name', '')),
            parent=int(data.get('parent', 0)),
            path=str(data.get('path', '')),
            grade=int(data.get('grade', 1))
        )


@dataclass
class UserModel:
    """用户数据模型"""
    id: int
    account: str
    realname: str
    dept: int
    deptname: str
    role: str
    email: str
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserModel':
        """从字典创建用户模型"""
        return cls(
            id=int(data.get('id', 0)),
            account=str(data.get('account', '')),
            realname=str(data.get('realname', '')),
            dept=int(data.get('dept', 0)),
            deptname=str(data.get('deptname', '')),
            role=str(data.get('role', '')),
            email=str(data.get('email', ''))
        )


# API响应包装器
@dataclass
class APIResponse:
    """API响应包装器"""
    success: bool
    data: Any
    message: str = ""
    error_code: Optional[str] = None
    
    @classmethod
    def success_response(cls, data: Any, message: str = "操作成功") -> 'APIResponse':
        """创建成功响应"""
        return cls(success=True, data=data, message=message)
    
    @classmethod
    def error_response(cls, message: str, error_code: Optional[str] = None, data: Any = None) -> 'APIResponse':
        """创建错误响应"""
        return cls(success=False, data=data, message=message, error_code=error_code)