"""
部门资源模块

实现部门相关的MCP资源，专注于基础数据访问，支持缓存机制
根据设计文档要求，简化资源URI设计，避免与工具功能重叠
"""

import logging
from typing import List, Dict, Any
from contextlib import asynccontextmanager

from ..exceptions import ZentaoAPIError, ResourceNotFoundError
from ..cache import cached_call, cache_key

logger = logging.getLogger(__name__)


@asynccontextmanager
async def get_zentao_client():
    """获取禅道客户端的上下文管理器"""
    from ..client import ZentaoClient
    client = ZentaoClient()
    try:
        await client.initialize()
        yield client
    finally:
        await client.close()


def register_department_resources(mcp):
    """注册部门相关的MCP资源"""
    
    @mcp.resource("zentao://departments")
    async def list_all_departments() -> List[Dict[str, Any]]:
        """
        获取所有部门列表 - 静态数据，适合缓存
        
        Returns:
            List[Dict]: 部门列表，包含部门层级结构
        """
        async def _fetch_departments():
            try:
                logger.info("从API获取所有部门列表")
                
                async with get_zentao_client() as client:
                    # 调用禅道API获取部门数据
                    response = await client.get_all_departments_async(parse_model=False)
                    
                    if not response or response.get("rsCode") != "00000000":
                        logger.warning(f"获取部门列表失败: {response}")
                        return []
                    
                    departments_data = response.get("body", [])
                    
                    # 转换为标准格式
                    departments = []
                    for dept_data in departments_data:
                        try:
                            # 确保数据类型正确
                            dept = {
                                "id": int(dept_data.get("id", 0)),
                                "name": str(dept_data.get("name", "")),
                                "parent": int(dept_data.get("parent", 0)),
                                "path": str(dept_data.get("path", "")),
                                "grade": int(dept_data.get("grade", 1))
                            }
                            departments.append(dept)
                        except (ValueError, TypeError) as e:
                            logger.warning(f"部门数据格式错误: {dept_data}, 错误: {e}")
                            continue
                    
                    logger.info(f"成功获取 {len(departments)} 个部门")
                    return departments
                    
            except Exception as e:
                logger.error(f"获取部门列表失败: {e}")
                raise ZentaoAPIError(f"获取部门列表失败: {str(e)}")
        
        # 使用缓存，部门列表变化不频繁，缓存1小时
        return await cached_call(
            cache_key("departments", "all"),
            _fetch_departments,
            ttl=3600  # 1小时
        )
    
    @mcp.resource("zentao://department/{dept_id}/users")
    async def get_department_users(dept_id: int) -> List[Dict[str, Any]]:
        """
        获取指定部门的用户列表 - 基础关联数据
        
        Args:
            dept_id: 部门ID
            
        Returns:
            List[Dict]: 部门用户列表
        """
        async def _fetch_department_users():
            try:
                logger.info(f"从API获取部门用户列表: {dept_id}")
                
                async with get_zentao_client() as client:
                    # 调用禅道API获取部门用户数据
                    response = await client.get_users_by_department_async(dept_id, parse_model=False)
                    
                    if not response or response.get("rsCode") != "00000000":
                        logger.warning(f"获取部门用户列表失败: {response}")
                        return []
                    
                    users_data = response.get("body", [])
                    
                    # 转换为标准格式
                    users = []
                    for user_data in users_data:
                        try:
                            user = {
                                "id": int(user_data.get("id", 0)),
                                "account": str(user_data.get("account", "")),
                                "realname": str(user_data.get("realname", "")),
                                "dept": int(user_data.get("dept", 0)),
                                "deptname": str(user_data.get("deptname", "")),
                                "role": str(user_data.get("role", "")),
                                "email": str(user_data.get("email", ""))
                            }
                            users.append(user)
                        except (ValueError, TypeError) as e:
                            logger.warning(f"用户数据格式错误: {user_data}, 错误: {e}")
                            continue
                    
                    logger.info(f"成功获取部门 {dept_id} 的 {len(users)} 个用户")
                    return users
                    
            except Exception as e:
                logger.error(f"获取部门用户列表失败: {e}")
                raise ZentaoAPIError(f"获取部门用户列表失败: {str(e)}")
        
        # 使用缓存，部门用户变化相对不频繁，缓存15分钟
        return await cached_call(
            cache_key("department", dept_id, "users"),
            _fetch_department_users,
            ttl=900  # 15分钟
        )
    
    logger.info("部门资源注册完成")