"""
需求资源模块

实现需求相关的MCP资源，专注于基础数据访问，支持缓存机制
根据设计文档要求，简化为单个实体详情访问
"""

import logging
from typing import Dict, Any
from contextlib import asynccontextmanager

from ..exceptions import ZentaoAPIError, ResourceNotFoundError
from ..cache import cached_call, cache_key

logger = logging.getLogger(__name__)


@asynccontextmanager
async def get_zentao_client():
    """获取禅道客户端的上下文管理器"""
    from ..client import ZentaoClient
    client = ZentaoClient()
    try:
        await client.initialize()
        yield client
    finally:
        await client.close()


def register_story_resources(mcp):
    """注册需求相关的MCP资源"""
    
    @mcp.resource("zentao://story/{story_id}")
    async def get_story_detail(story_id: int) -> Dict[str, Any]:
        """
        获取需求详细信息 - 单个实体详情
        
        Args:
            story_id: 需求ID
            
        Returns:
            Dict: 需求详细信息，包含工时和规格说明
        """
        async def _fetch_story_detail():
            try:
                logger.info(f"从API获取需求详情: {story_id}")
                
                async with get_zentao_client() as client:
                    # 调用禅道API获取需求详情
                    response = await client.get_story_by_id_async(story_id)
                    
                    if not response or response.get("rsCode") != "00000000":
                        logger.warning(f"需求不存在或获取失败: {story_id}, 响应: {response}")
                        raise ResourceNotFoundError(f"需求 {story_id} 不存在")
                    
                    story_data = response.get("body")
                    if not story_data:
                        raise ResourceNotFoundError(f"需求 {story_id} 不存在")
                    
                    # 转换为标准格式
                    story = {
                        "id": int(story_data.get("id", 0)),
                        "title": str(story_data.get("title", "")),
                        "product": int(story_data.get("product", 0)),
                        "productname": str(story_data.get("productname", "")),
                        "module": int(story_data.get("module", 0)),
                        "modulename": str(story_data.get("modulename", "")),
                        "pri": int(story_data.get("pri", 3)),
                        "estimate": float(story_data.get("estimate", 0.0)),
                        "status": str(story_data.get("status", "active")),
                        "stage": str(story_data.get("stage", "wait")),
                        "openedBy": str(story_data.get("openedBy", "")),
                        "openedByEmpid": str(story_data.get("openedByEmpid", "")),
                        "assignedTo": str(story_data.get("assignedTo", "")),
                        "assignedToEmpid": str(story_data.get("assignedToEmpid", "")),
                        "spec": story_data.get("spec"),
                        "openedDate": story_data.get("openedDate"),
                        "assignedDate": story_data.get("assignedDate"),
                        "closedDate": story_data.get("closedDate"),
                        "closedBy": str(story_data.get("closedBy", "")),
                        "closedReason": str(story_data.get("closedReason", "")),
                        "version": int(story_data.get("version", 1)),
                        "keywords": str(story_data.get("keywords", "")),
                        "reviewedBy": str(story_data.get("reviewedBy", "")),
                        "reviewedDate": story_data.get("reviewedDate")
                    }
                    
                    logger.info(f"成功获取需求详情: {story['title']}")
                    return story
                    
            except ResourceNotFoundError:
                raise
            except Exception as e:
                logger.error(f"获取需求详情失败: {e}")
                raise ZentaoAPIError(f"获取需求详情失败: {str(e)}")
        
        # 使用缓存，需求详情相对稳定，缓存10分钟
        return await cached_call(
            cache_key("story", story_id, "detail"),
            _fetch_story_detail,
            ttl=600  # 10分钟
        )
    
    @mcp.resource("zentao://stories")
    async def list_all_stories() -> Dict[str, Any]:
        """
        获取所有需求列表 - 提供基础列表访问
        
        Returns:
            Dict: 包含需求列表的响应
        """
        try:
            logger.info("获取所有需求列表")
            
            # 由于需求数据量可能很大，这里返回一个提示信息
            # 实际使用中建议通过工具按项目或条件查询
            return {
                "message": "需求数据量较大，建议使用工具按项目或条件查询",
                "available_tools": [
                    "mcp_zentao_get_project_stories",
                    "mcp_zentao_get_stories_by_time"
                ]
            }
            
        except Exception as e:
            logger.error(f"获取需求列表失败: {e}")
            raise ZentaoAPIError(f"获取需求列表失败: {str(e)}")
    
    logger.info("需求资源注册完成")