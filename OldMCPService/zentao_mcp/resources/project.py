"""
项目资源模块

实现项目相关的MCP资源，专注于基础数据访问，支持缓存机制
根据设计文档要求，简化资源URI设计，避免与工具功能重叠
"""

import logging
from typing import List, Dict, Any
from contextlib import asynccontextmanager

from ..exceptions import ZentaoAPIError, ResourceNotFoundError
from ..cache import cached_call, cache_key

logger = logging.getLogger(__name__)


@asynccontextmanager
async def get_zentao_client():
    """获取禅道客户端的上下文管理器"""
    from ..client import ZentaoClient
    client = ZentaoClient()
    try:
        await client.initialize()
        yield client
    finally:
        await client.close()


async def _fetch_all_projects() -> List[Dict[str, Any]]:
    """内部函数：获取所有项目列表"""
    try:
        logger.info("从API获取所有项目列表")
        
        async with get_zentao_client() as client:
            # 调用禅道API获取项目数据
            response = await client.get_all_projects_async(parse_model=False)
            
            if not response or response.get("rsCode") != "00000000":
                logger.warning(f"获取项目列表失败: {response}")
                return []
            
            projects_data = response.get("body", [])
            
            # 转换为标准格式
            projects = []
            for project_data in projects_data:
                try:
                    # 确保数据类型正确
                    project = {
                        "id": int(project_data.get("id", 0)),
                        "name": str(project_data.get("name", "")),
                        "code": str(project_data.get("code", "")),
                        "begin": str(project_data.get("begin", "")),
                        "end": str(project_data.get("end", "")),
                        "status": str(project_data.get("status", "wait")),
                        "parent": int(project_data.get("parent", 0)),
                        "openedBy": str(project_data.get("openedBy", "")),
                        "closedDate": project_data.get("closedDate"),
                        "closedBy": str(project_data.get("closedBy", ""))
                    }
                    projects.append(project)
                except (ValueError, TypeError) as e:
                    logger.warning(f"项目数据格式错误: {project_data}, 错误: {e}")
                    continue
            
            logger.info(f"成功获取 {len(projects)} 个项目")
            return projects
            
    except Exception as e:
        logger.error(f"获取项目列表失败: {e}")
        raise ZentaoAPIError(f"获取项目列表失败: {str(e)}")


async def _get_cached_projects() -> List[Dict[str, Any]]:
    """获取缓存的项目列表"""
    return await cached_call(
        cache_key("projects", "all"),
        _fetch_all_projects,
        ttl=1800  # 30分钟
    )


def register_project_resources(mcp):
    """注册项目相关的MCP资源"""
    
    @mcp.resource("zentao://projects")
    async def list_all_projects() -> List[Dict[str, Any]]:
        """
        获取所有项目列表 - 静态数据，适合缓存
        
        Returns:
            List[Dict]: 项目列表，包含项目基本信息
        """
        # 使用缓存，项目列表变化不频繁，缓存30分钟
        return await _get_cached_projects()
    
    @mcp.resource("zentao://project/{project_id}")
    async def get_project_detail(project_id: int) -> Dict[str, Any]:
        """
        获取项目基本信息 - 单个实体详情
        
        Args:
            project_id: 项目ID
            
        Returns:
            Dict: 项目详细信息
        """
        try:
            logger.info(f"获取项目详情: {project_id}")
            
            # 从缓存的项目列表中查找
            projects = await _get_cached_projects()
            
            # 查找指定项目
            for project in projects:
                if project.get("id") == project_id:
                    logger.info(f"找到项目: {project['name']}")
                    return project
            
            # 项目不存在
            logger.warning(f"项目不存在: {project_id}")
            raise ResourceNotFoundError(f"项目 {project_id} 不存在")
            
        except ResourceNotFoundError:
            raise
        except Exception as e:
            logger.error(f"获取项目详情失败: {e}")
            raise ZentaoAPIError(f"获取项目详情失败: {str(e)}")
    
    @mcp.resource("zentao://projects/by-status/{status}")
    async def get_projects_by_status(status: str) -> List[Dict[str, Any]]:
        """
        根据状态获取项目列表
        
        Args:
            status: 项目状态 (wait, doing, suspended, closed)
            context: MCP上下文
            
        Returns:
            List[Dict]: 符合状态的项目列表
        """
        try:
            # 验证状态参数
            valid_statuses = ["wait", "doing", "suspended", "closed"]
            if status not in valid_statuses:
                raise ValueError(f"无效的项目状态: {status}，有效值: {valid_statuses}")
            
            logger.info(f"获取状态为 {status} 的项目")
            
            # 获取所有项目并过滤
            all_projects = await _get_cached_projects()
            filtered_projects = [p for p in all_projects if p.get("status") == status]
            
            logger.info(f"找到 {len(filtered_projects)} 个状态为 {status} 的项目")
            return filtered_projects
            
        except ValueError:
            raise
        except Exception as e:
            logger.error(f"根据状态获取项目失败: {e}")
            raise ZentaoAPIError(f"根据状态获取项目失败: {str(e)}")
    
    @mcp.resource("zentao://projects/by-parent/{parent_id}")
    async def get_projects_by_parent(parent_id: int) -> List[Dict[str, Any]]:
        """
        根据父项目ID获取子项目列表
        
        Args:
            parent_id: 父项目ID
            context: MCP上下文
            
        Returns:
            List[Dict]: 子项目列表
        """
        try:
            logger.info(f"获取父项目 {parent_id} 的子项目")
            
            # 获取所有项目并过滤
            all_projects = await _get_cached_projects()
            child_projects = [p for p in all_projects if p.get("parent") == parent_id]
            
            logger.info(f"找到 {len(child_projects)} 个子项目")
            return child_projects
            
        except Exception as e:
            logger.error(f"根据父项目获取子项目失败: {e}")
            raise ZentaoAPIError(f"根据父项目获取子项目失败: {str(e)}")
    
    @mcp.resource("zentao://projects/summary")
    async def get_projects_summary() -> Dict[str, Any]:
        """
        获取项目统计摘要
        
        Args:
            context: MCP上下文
            
        Returns:
            Dict: 项目统计信息
        """
        try:
            logger.info("获取项目统计摘要")
            
            # 获取所有项目
            all_projects = await _get_cached_projects()
            
            # 统计各状态项目数量
            status_counts = {}
            for project in all_projects:
                status = project.get("status", "unknown")
                status_counts[status] = status_counts.get(status, 0) + 1
            
            # 统计父子项目关系
            parent_projects = [p for p in all_projects if p.get("parent") == 0]
            child_projects = [p for p in all_projects if p.get("parent") != 0]
            
            summary = {
                "total_projects": len(all_projects),
                "status_distribution": status_counts,
                "parent_projects": len(parent_projects),
                "child_projects": len(child_projects),
                "active_projects": status_counts.get("doing", 0),
                "waiting_projects": status_counts.get("wait", 0),
                "closed_projects": status_counts.get("closed", 0)
            }
            
            logger.info(f"项目统计摘要: {summary}")
            return summary
            
        except Exception as e:
            logger.error(f"获取项目统计摘要失败: {e}")
            raise ZentaoAPIError(f"获取项目统计摘要失败: {str(e)}")
    
    logger.info("项目资源注册完成")