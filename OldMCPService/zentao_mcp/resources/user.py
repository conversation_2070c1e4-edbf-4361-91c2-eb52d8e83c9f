"""
用户资源模块

实现用户相关的MCP资源，专注于基础数据访问，支持缓存机制
根据设计文档要求，实现用户资源的基础访问功能
"""

import logging
from typing import Dict, Any
from contextlib import asynccontextmanager

from ..exceptions import ZentaoAPIError, ResourceNotFoundError
from ..cache import cached_call, cache_key

logger = logging.getLogger(__name__)


@asynccontextmanager
async def get_zentao_client():
    """获取禅道客户端的上下文管理器"""
    from ..client import ZentaoClient
    client = ZentaoClient()
    try:
        await client.initialize()
        yield client
    finally:
        await client.close()


def register_user_resources(mcp):
    """注册用户相关的MCP资源"""
    
    @mcp.resource("zentao://user/{user_account}")
    async def get_user_info(user_account: str) -> Dict[str, Any]:
        """
        获取用户基本信息 - 单个实体详情
        
        Args:
            user_account: 用户账号
            
        Returns:
            Dict: 用户详细信息
        """
        async def _fetch_user_info():
            try:
                logger.info(f"从API获取用户信息: {user_account}")
                
                async with get_zentao_client() as client:
                    # 调用禅道API获取用户信息
                    response = await client.get_user_info_async(user_account)
                    
                    if not response or response.get("rsCode") != "********":
                        logger.warning(f"获取用户信息失败: {response}")
                        raise ResourceNotFoundError(f"用户 {user_account} 不存在")
                    
                    user_data = response.get("body", {})
                    
                    # 获取部门信息以补充部门名称
                    dept_id = user_data.get("dept", 0)
                    dept_name = "未知部门"
                    
                    if dept_id > 0:
                        try:
                            dept_response = await client.get_all_departments_async(parse_model=False)
                            if dept_response and dept_response.get("rsCode") == "********":
                                for dept in dept_response.get("body", []):
                                    if int(dept.get("id", 0)) == dept_id:
                                        dept_name = dept.get("name", "未知部门")
                                        break
                        except Exception as e:
                            logger.warning(f"获取部门信息失败: {e}")
                    
                    # 转换为标准格式
                    user = {
                        "id": hash(user_account) % 1000000,  # 临时生成ID
                        "account": str(user_data.get("account", "")),
                        "realname": str(user_data.get("realname", "")),
                        "dept": int(dept_id),
                        "deptname": str(dept_name),
                        "role": str(user_data.get("role", "unknown")),
                        "email": str(user_data.get("email", ""))
                    }
                    
                    logger.info(f"成功获取用户信息: {user['realname']}")
                    return user
                    
            except ResourceNotFoundError:
                raise
            except Exception as e:
                logger.error(f"获取用户信息失败: {e}")
                raise ZentaoAPIError(f"获取用户信息失败: {str(e)}")
        
        # 使用缓存，用户信息相对稳定，缓存30分钟
        return await cached_call(
            cache_key("user", user_account, "info"),
            _fetch_user_info,
            ttl=1800  # 30分钟
        )
    
    logger.info("用户资源注册完成")