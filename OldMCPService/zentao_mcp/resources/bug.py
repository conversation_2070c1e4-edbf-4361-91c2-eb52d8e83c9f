"""
Bug资源模块

实现Bug相关的MCP资源，专注于基础数据访问，支持缓存机制
根据设计文档要求，简化为单个实体详情访问
"""

import logging
from typing import Dict, Any
from datetime import datetime
from contextlib import asynccontextmanager

from ..exceptions import ZentaoAPIError, ResourceNotFoundError
from ..cache import cached_call, cache_key

logger = logging.getLogger(__name__)


@asynccontextmanager
async def get_zentao_client():
    """获取禅道客户端的上下文管理器"""
    from ..client import ZentaoClient
    client = ZentaoClient()
    try:
        await client.initialize()
        yield client
    finally:
        await client.close()


def register_bug_resources(mcp):
    """注册Bug相关的MCP资源"""
    
    @mcp.resource("zentao://bug/{bug_id}")
    async def get_bug_detail(bug_id: int) -> Dict[str, Any]:
        """
        获取Bug详细信息 - 单个实体详情
        
        Args:
            bug_id: Bug ID
            
        Returns:
            Dict: Bug详细信息
        """
        async def _fetch_bug_detail():
            try:
                logger.info(f"从API获取Bug详情: {bug_id}")
                
                async with get_zentao_client() as client:
                    # 调用禅道API获取Bug详情
                    response = await client.get_bug_detail_async(bug_id)
                    
                    if not response or response.get("rsCode") != "00000000":
                        logger.warning(f"获取Bug详情失败: {response}")
                        raise ResourceNotFoundError(f"Bug {bug_id} 不存在或获取失败")
                    
                    body = response.get("body", {})
                    
                    # 处理不同的响应格式
                    bug_data = None
                    if "data" in body:
                        bug_data = body["data"]
                    elif "bugdetail" in body:
                        bug_data = body["bugdetail"]
                    elif "detail" in body:
                        bug_data = body["detail"]
                    else:
                        bug_data = body
                    
                    # 如果是列表，取第一个元素
                    if isinstance(bug_data, list) and bug_data:
                        bug_data = bug_data[0]
                    
                    if not bug_data or not isinstance(bug_data, dict):
                        raise ResourceNotFoundError(f"Bug {bug_id} 数据格式错误")
                    
                    # 转换为标准格式
                    bug = _format_bug_data(bug_data)
                    
                    logger.info(f"成功获取Bug详情: {bug['title']}")
                    return bug
                    
            except ResourceNotFoundError:
                raise
            except Exception as e:
                logger.error(f"获取Bug详情失败: {e}")
                raise ZentaoAPIError(f"获取Bug详情失败: {str(e)}")
        
        # 使用缓存，Bug详情相对稳定，缓存10分钟
        return await cached_call(
            cache_key("bug", bug_id, "detail"),
            _fetch_bug_detail,
            ttl=600  # 10分钟
        )
    
    @mcp.resource("zentao://bugs")
    async def list_all_bugs() -> Dict[str, Any]:
        """
        获取所有Bug列表 - 提供基础列表访问
        
        Returns:
            Dict: 包含Bug列表的响应
        """
        try:
            logger.info("获取所有Bug列表")
            
            # 由于Bug数据量可能很大，这里返回一个提示信息
            # 实际使用中建议通过工具按项目或条件查询
            return {
                "message": "Bug数据量较大，建议使用工具按项目或条件查询",
                "available_tools": [
                    "mcp_zentao_get_project_bugs",
                    "mcp_zentao_get_bugs_by_time_range",
                    "mcp_zentao_get_personal_bugs"
                ]
            }
            
        except Exception as e:
            logger.error(f"获取Bug列表失败: {e}")
            raise ZentaoAPIError(f"获取Bug列表失败: {str(e)}")
    
    @mcp.resource("zentao://project/{project_id}/bugs")
    async def get_bugs_by_project(project_id: int) -> Dict[str, Any]:
        """
        获取项目的Bug列表
        
        Args:
            project_id: 项目ID
            
        Returns:
            Dict: 项目Bug列表
        """
        try:
            logger.info(f"获取项目 {project_id} 的Bug列表")
            
            async with get_zentao_client() as client:
                response = await client.get_bugs_by_project_async(project_id)
                
                if not response or response.get("rsCode") != "00000000":
                    logger.warning(f"获取项目Bug失败: {project_id}, 响应: {response}")
                    return {"bugs": [], "total": 0}
                
                bugs_data = response.get("body", [])
                
                # 转换为标准格式
                bugs = []
                for bug_data in bugs_data:
                    try:
                        bug = _format_bug_data(bug_data)
                        bugs.append(bug)
                    except Exception as e:
                        logger.warning(f"Bug数据格式化失败: {bug_data}, 错误: {e}")
                        continue
                
                result = {
                    "project_id": project_id,
                    "bugs": bugs,
                    "total": len(bugs)
                }
                
                logger.info(f"成功获取项目 {project_id} 的 {len(bugs)} 个Bug")
                return result
                
        except Exception as e:
            logger.error(f"获取项目Bug失败: {e}")
            raise ZentaoAPIError(f"获取项目Bug失败: {str(e)}")
    
    @mcp.resource("zentao://bugs/by-status/{status}")
    async def get_bugs_by_status(status: str) -> Dict[str, Any]:
        """
        根据状态获取Bug列表
        
        Args:
            status: Bug状态
            
        Returns:
            Dict: 包含Bug列表的响应
        """
        try:
            # 验证状态参数
            valid_statuses = ["active", "resolved", "closed", "released"]
            if status not in valid_statuses:
                raise ValueError(f"无效的Bug状态: {status}，有效值: {valid_statuses}")
            
            logger.info(f"根据状态 {status} 获取Bug")
            
            return {
                "message": f"按状态 {status} 查询Bug需要指定项目或时间范围",
                "suggestion": "使用 mcp_zentao_get_bugs_by_time_range 或 mcp_zentao_get_personal_bugs 工具"
            }
            
        except ValueError:
            raise
        except Exception as e:
            logger.error(f"根据状态获取Bug失败: {e}")
            raise ZentaoAPIError(f"根据状态获取Bug失败: {str(e)}")
    
    @mcp.resource("zentao://bugs/by-severity/{severity}")
    async def get_bugs_by_severity(severity: int) -> Dict[str, Any]:
        """
        根据严重程度获取Bug列表
        
        Args:
            severity: Bug严重程度 (1-4)
            
        Returns:
            Dict: 包含Bug列表的响应
        """
        try:
            # 验证严重程度参数
            if severity not in [0, 1, 2, 3, 4]:
                raise ValueError(f"无效的Bug严重程度: {severity}，有效值: 0-4")
            
            logger.info(f"根据严重程度 {severity} 获取Bug")
            
            return {
                "message": f"按严重程度 {severity} 查询Bug需要指定项目或时间范围",
                "suggestion": "使用 mcp_zentao_get_bugs_by_time_range 或 mcp_zentao_get_personal_bugs 工具"
            }
            
        except ValueError:
            raise
        except Exception as e:
            logger.error(f"根据严重程度获取Bug失败: {e}")
            raise ZentaoAPIError(f"根据严重程度获取Bug失败: {str(e)}")
    
    logger.info("Bug资源注册完成")


def _format_bug_data(bug_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    格式化Bug数据，确保与ERD文档一致
    
    Args:
        bug_data: 原始Bug数据
        
    Returns:
        Dict: 格式化后的Bug数据
    """
    try:
        # 处理日期字段
        opened_date = bug_data.get("openedDate", "")
        if opened_date:
            try:
                # 尝试解析不同的日期格式
                if "T" in opened_date:
                    parsed_date = datetime.fromisoformat(opened_date.replace("Z", "+00:00"))
                else:
                    parsed_date = datetime.strptime(opened_date, "%Y-%m-%d %H:%M:%S")
                opened_date_str = parsed_date.isoformat()
            except (ValueError, TypeError):
                opened_date_str = opened_date
        else:
            opened_date_str = ""
        
        # 确保数据类型正确
        bug = {
            "id": int(bug_data.get("id", 0)),
            "title": str(bug_data.get("title", "")),
            "status": str(bug_data.get("status", "active")),
            "severity": int(bug_data.get("severity", 2)),
            "project": int(bug_data.get("project", 0)),
            "projectname": str(bug_data.get("projectname", "")),
            "assignedTo": str(bug_data.get("assignedTo", "")),
            "assignedToEmpid": str(bug_data.get("assignedToEmpid", "")),
            "openedBy": str(bug_data.get("openedBy", "")),
            "openedByEmpid": str(bug_data.get("openedByEmpid", "")),
            "openedDate": opened_date_str,
            "resolution": str(bug_data.get("resolution", "")),
            "environment": str(bug_data.get("environment", "")),
            "steps": str(bug_data.get("steps", ""))
        }
        
        # 添加额外的有用字段
        if "pri" in bug_data:
            bug["priority"] = int(bug_data["pri"])
        if "type" in bug_data:
            bug["type"] = str(bug_data["type"])
        if "os" in bug_data:
            bug["os"] = str(bug_data["os"])
        if "browser" in bug_data:
            bug["browser"] = str(bug_data["browser"])
        if "resolvedBy" in bug_data:
            bug["resolvedBy"] = str(bug_data["resolvedBy"])
        if "resolvedDate" in bug_data:
            bug["resolvedDate"] = str(bug_data["resolvedDate"])
        if "closedBy" in bug_data:
            bug["closedBy"] = str(bug_data["closedBy"])
        if "closedDate" in bug_data:
            bug["closedDate"] = str(bug_data["closedDate"])
        
        return bug
        
    except (ValueError, TypeError, KeyError) as e:
        logger.warning(f"Bug数据格式化失败: {bug_data}, 错误: {e}")
        # 返回最基本的Bug结构，使用安全的类型转换
        def safe_int(value, default=0):
            try:
                return int(value)
            except (ValueError, TypeError):
                return default
        
        def safe_str(value, default=""):
            try:
                return str(value) if value is not None else default
            except (ValueError, TypeError):
                return default
        
        return {
            "id": safe_int(bug_data.get("id"), 0),
            "title": safe_str(bug_data.get("title"), "未知Bug"),
            "status": safe_str(bug_data.get("status"), "active"),
            "severity": safe_int(bug_data.get("severity"), 2),
            "project": safe_int(bug_data.get("project"), 0),
            "projectname": safe_str(bug_data.get("projectname"), ""),
            "assignedTo": safe_str(bug_data.get("assignedTo"), ""),
            "assignedToEmpid": safe_str(bug_data.get("assignedToEmpid"), ""),
            "openedBy": safe_str(bug_data.get("openedBy"), ""),
            "openedByEmpid": safe_str(bug_data.get("openedByEmpid"), ""),
            "openedDate": safe_str(bug_data.get("openedDate"), ""),
            "resolution": safe_str(bug_data.get("resolution"), ""),
            "environment": safe_str(bug_data.get("environment"), ""),
            "steps": safe_str(bug_data.get("steps"), "")
        }