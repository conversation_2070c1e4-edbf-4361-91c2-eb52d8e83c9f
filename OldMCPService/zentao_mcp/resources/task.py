"""
任务资源模块

实现任务相关的MCP资源，专注于基础数据访问，支持缓存机制
根据设计文档要求，简化为单个实体详情访问
"""

import logging
from typing import Dict, Any
from contextlib import asynccontextmanager

from ..exceptions import ZentaoAPIError, ResourceNotFoundError
from ..cache import cached_call, cache_key

logger = logging.getLogger(__name__)


@asynccontextmanager
async def get_zentao_client():
    """获取禅道客户端的上下文管理器"""
    from ..client import ZentaoClient
    client = ZentaoClient()
    try:
        await client.initialize()
        yield client
    finally:
        await client.close()


def register_task_resources(mcp):
    """注册任务相关的MCP资源"""
    
    @mcp.resource("zentao://task/{task_id}")
    async def get_task_detail(task_id: int) -> Dict[str, Any]:
        """
        获取任务详细信息 - 单个实体详情
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 任务详细信息，包含工时消耗和状态信息
        """
        async def _fetch_task_detail():
            try:
                logger.info(f"从API获取任务详情: {task_id}")
                
                async with get_zentao_client() as client:
                    # 调用禅道API获取任务详情
                    response = await client.get_task_by_id_async(task_id)
                    
                    if not response or response.get("rsCode") != "********":
                        logger.warning(f"任务不存在或获取失败: {task_id}, 响应: {response}")
                        raise ResourceNotFoundError(f"任务 {task_id} 不存在")
                    
                    task_data = response.get("body", {}).get("data")
                    if not task_data:
                        raise ResourceNotFoundError(f"任务 {task_id} 不存在")
                    
                    # 转换为标准格式
                    task = {
                        "id": int(task_data.get("id", 0)),
                        "name": str(task_data.get("name", "")),
                        "project": int(task_data.get("project", 0)),
                        "projectname": str(task_data.get("projectname", "")),
                        "story": int(task_data.get("story", 0)),
                        "storyname": str(task_data.get("storyname", "")),
                        "type": str(task_data.get("type", "devel")),
                        "status": str(task_data.get("status", "wait")),
                        "pri": int(task_data.get("pri", 3)),
                        "estimate": float(task_data.get("estimate", 0.0)),
                        "consumed": float(task_data.get("consumed", 0.0)),
                        "left": float(task_data.get("left", 0.0)),
                        "assignedTo": str(task_data.get("assignedTo", "")),
                        "assignedToEmpid": str(task_data.get("assignedToEmpid", "")),
                        "finishedBy": str(task_data.get("finishedBy", "")),
                        "finishedByEmpid": str(task_data.get("finishedByEmpid", "")),
                        "openedBy": str(task_data.get("openedBy", "")),
                        "openedDate": task_data.get("openedDate"),
                        "finishedDate": task_data.get("finishedDate"),
                        "closedBy": str(task_data.get("closedBy", "")),
                        "closedDate": task_data.get("closedDate"),
                        "closedReason": str(task_data.get("closedReason", "")),
                        "desc": task_data.get("desc"),
                        "version": int(task_data.get("version", 1)),
                        "keywords": str(task_data.get("keywords", ""))
                    }
                    
                    logger.info(f"成功获取任务详情: {task['name']}")
                    return task
                    
            except ResourceNotFoundError:
                raise
            except Exception as e:
                logger.error(f"获取任务详情失败: {e}")
                raise ZentaoAPIError(f"获取任务详情失败: {str(e)}")
        
        # 使用缓存，任务详情相对稳定，缓存10分钟
        return await cached_call(
            cache_key("task", task_id, "detail"),
            _fetch_task_detail,
            ttl=600  # 10分钟
        )
    
    @mcp.resource("zentao://tasks")
    async def list_all_tasks() -> Dict[str, Any]:
        """
        获取所有任务列表 - 提供基础列表访问

        Returns:
            Dict: 包含任务列表的响应
        """
        try:
            logger.info("获取所有任务列表")
            
            # 由于任务数据量可能很大，这里返回一个提示信息
            # 实际使用中建议通过工具按项目或条件查询
            return {
                "message": "任务数据量较大，建议使用工具按项目或条件查询",
                "available_tools": [
                    "mcp_zentao_get_project_tasks",
                    "mcp_zentao_get_tasks_by_account",
                    "mcp_zentao_get_tasks_by_dept"
                ]
            }
            
        except Exception as e:
            logger.error(f"获取任务列表失败: {e}")
            raise ZentaoAPIError(f"获取任务列表失败: {str(e)}")
    
    @mcp.resource("zentao://project/{project_id}/tasks")
    async def get_tasks_by_project(project_id: int) -> Dict[str, Any]:
        """
        获取项目的任务列表
        
        Args:
            project_id: 项目ID
            
        Returns:
            Dict: 项目任务列表
        """
        try:
            logger.info(f"获取项目 {project_id} 的任务列表")
            
            async with get_zentao_client() as client:
                response = await client.get_tasks_by_project_async(project_id)
                
                if not response or response.get("rsCode") != "********":
                    logger.warning(f"获取项目任务失败: {project_id}, 响应: {response}")
                    return {"tasks": [], "total": 0}
                
                tasks_data = response.get("body", {}).get("data", [])
                
                # 转换为标准格式
                tasks = []
                for task_data in tasks_data:
                    try:
                        task = {
                            "id": int(task_data.get("id", 0)),
                            "name": str(task_data.get("name", "")),
                            "project": int(task_data.get("project", 0)),
                            "story": int(task_data.get("story", 0)),
                            "type": str(task_data.get("type", "devel")),
                            "status": str(task_data.get("status", "wait")),
                            "pri": int(task_data.get("pri", 3)),
                            "estimate": float(task_data.get("estimate", 0.0)),
                            "consumed": float(task_data.get("consumed", 0.0)),
                            "left": float(task_data.get("left", 0.0)),
                            "assignedTo": str(task_data.get("assignedTo", "")),
                            "openedBy": str(task_data.get("openedBy", "")),
                            "openedDate": task_data.get("openedDate"),
                            "finishedDate": task_data.get("finishedDate")
                        }
                        tasks.append(task)
                    except (ValueError, TypeError) as e:
                        logger.warning(f"任务数据格式错误: {task_data}, 错误: {e}")
                        continue
                
                result = {
                    "project_id": project_id,
                    "tasks": tasks,
                    "total": len(tasks)
                }
                
                logger.info(f"成功获取项目 {project_id} 的 {len(tasks)} 个任务")
                return result
                
        except Exception as e:
            logger.error(f"获取项目任务失败: {e}")
            raise ZentaoAPIError(f"获取项目任务失败: {str(e)}")
    
    @mcp.resource("zentao://tasks/by-status/{status}")
    async def get_tasks_by_status(status: str) -> Dict[str, Any]:
        """
        根据状态获取任务列表
        
        Args:
            status: 任务状态
            
        Returns:
            Dict: 包含任务列表的响应
        """
        try:
            # 验证状态参数
            valid_statuses = ["wait", "doing", "done", "pause", "cancel", "closed"]
            if status not in valid_statuses:
                raise ValueError(f"无效的任务状态: {status}，有效值: {valid_statuses}")
            
            logger.info(f"根据状态 {status} 获取任务")
            
            return {
                "message": f"按状态 {status} 查询任务需要指定项目或用户范围",
                "suggestion": "使用 mcp_zentao_get_tasks_by_account 或 mcp_zentao_get_tasks_by_dept 工具"
            }
            
        except ValueError:
            raise
        except Exception as e:
            logger.error(f"根据状态获取任务失败: {e}")
            raise ZentaoAPIError(f"根据状态获取任务失败: {str(e)}")
    
    @mcp.resource("zentao://tasks/by-type/{task_type}")
    async def get_tasks_by_type(task_type: str) -> Dict[str, Any]:
        """
        根据类型获取任务列表
        
        Args:
            task_type: 任务类型
            
        Returns:
            Dict: 包含任务列表的响应
        """
        try:
            # 验证类型参数
            valid_types = ["devel", "test", "design", "study", "discuss", "ui", "affair", "misc"]
            if task_type not in valid_types:
                raise ValueError(f"无效的任务类型: {task_type}，有效值: {valid_types}")
            
            logger.info(f"根据类型 {task_type} 获取任务")
            
            return {
                "message": f"按类型 {task_type} 查询任务需要指定项目或用户范围",
                "suggestion": "使用 mcp_zentao_get_tasks_by_account 或 mcp_zentao_get_tasks_by_dept 工具"
            }
            
        except ValueError:
            raise
        except Exception as e:
            logger.error(f"根据类型获取任务失败: {e}")
            raise ZentaoAPIError(f"根据类型获取任务失败: {str(e)}")
    
    @mcp.resource("zentao://tasks/by-priority/{priority}")
    async def get_tasks_by_priority(priority: int) -> Dict[str, Any]:
        """
        根据优先级获取任务列表
        
        Args:
            priority: 任务优先级 (1-4)
            
        Returns:
            Dict: 包含任务列表的响应
        """
        try:
            # 验证优先级参数
            if priority not in [1, 2, 3, 4]:
                raise ValueError(f"无效的任务优先级: {priority}，有效值: 1-4")
            
            logger.info(f"根据优先级 {priority} 获取任务")
            
            return {
                "message": f"按优先级 {priority} 查询任务需要指定项目或用户范围",
                "suggestion": "使用 mcp_zentao_get_tasks_by_account 或 mcp_zentao_get_tasks_by_dept 工具"
            }
            
        except ValueError:
            raise
        except Exception as e:
            logger.error(f"根据优先级获取任务失败: {e}")
            raise ZentaoAPIError(f"根据优先级获取任务失败: {str(e)}")
    
    logger.info("任务资源注册完成")