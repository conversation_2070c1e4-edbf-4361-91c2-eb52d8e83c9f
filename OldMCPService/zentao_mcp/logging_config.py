"""
结构化日志配置模块
提供JSON格式日志、请求追踪和性能指标收集
"""

import json
import logging
import logging.handlers
import os
import sys
import time
import uuid
from datetime import datetime
from typing import Any, Dict, Optional
from contextvars import ContextVar

from .config import settings


# 请求上下文变量
request_id_var: ContextVar[Optional[str]] = ContextVar('request_id', default=None)
user_id_var: ContextVar[Optional[str]] = ContextVar('user_id', default=None)


class StructuredFormatter(logging.Formatter):
    """结构化日志格式器，支持JSON输出"""
    
    def __init__(self, json_format: bool = False):
        super().__init__()
        self.json_format = json_format
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录"""
        # 基础日志信息
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # 添加请求上下文信息
        try:
            request_id = request_id_var.get()
            if request_id:
                log_data["request_id"] = request_id
        except LookupError:
            pass  # 上下文变量未设置
        
        try:
            user_id = user_id_var.get()
            if user_id:
                log_data["user_id"] = user_id
        except LookupError:
            pass  # 上下文变量未设置
        
        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": self.formatException(record.exc_info)
            }
        
        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            log_data.update(record.extra_fields)
        
        # 从record中提取额外的属性
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName', 
                          'processName', 'process', 'getMessage', 'exc_info', 
                          'exc_text', 'stack_info', 'extra_fields']:
                if not key.startswith('_'):
                    log_data[key] = value
        
        if self.json_format:
            return json.dumps(log_data, ensure_ascii=False, default=str)
        else:
            # 传统格式
            base_msg = f"{log_data['timestamp']} - {log_data['logger']} - {log_data['level']} - {log_data['message']}"
            
            # 添加请求ID
            if request_id:
                base_msg = f"[{request_id}] {base_msg}"
            
            # 添加额外信息
            extra_info = []
            for key, value in log_data.items():
                if key not in ['timestamp', 'level', 'logger', 'message', 'module', 
                              'function', 'line', 'request_id', 'user_id', 'exception']:
                    extra_info.append(f"{key}={value}")
            
            if extra_info:
                base_msg += f" | {' '.join(extra_info)}"
            
            # 添加异常信息
            if 'exception' in log_data:
                base_msg += f"\n{log_data['exception']['traceback']}"
            
            return base_msg


class RequestTracker:
    """请求追踪器"""
    
    def __init__(self):
        self.active_requests: Dict[str, Dict[str, Any]] = {}
    
    def start_request(self, request_id: str, method: str, url: str, **kwargs) -> None:
        """开始追踪请求"""
        self.active_requests[request_id] = {
            "request_id": request_id,
            "method": method,
            "url": url,
            "start_time": time.time(),
            "status": "started",
            **kwargs
        }
        
        # 设置请求上下文
        request_id_var.set(request_id)
        
        logger = logging.getLogger("zentao_mcp.request_tracker")
        logger.info(
            "请求开始",
            extra={
                "request_id": request_id,
                "method": method,
                "url": url,
                "event_type": "request_start"
            }
        )
    
    def end_request(self, request_id: str, status_code: Optional[int] = None, 
                   error: Optional[str] = None, **kwargs) -> None:
        """结束请求追踪"""
        if request_id not in self.active_requests:
            return
        
        request_info = self.active_requests[request_id]
        duration = time.time() - request_info["start_time"]
        
        request_info.update({
            "end_time": time.time(),
            "duration": duration,
            "status_code": status_code,
            "error": error,
            "status": "completed" if not error else "failed",
            **kwargs
        })
        
        logger = logging.getLogger("zentao_mcp.request_tracker")
        
        if error:
            logger.error(
                "请求失败",
                extra={
                    "request_id": request_id,
                    "duration": duration,
                    "status_code": status_code,
                    "error": error,
                    "event_type": "request_error"
                }
            )
        else:
            log_level = logging.WARNING if duration > 2.0 else logging.INFO
            logger.log(
                log_level,
                "请求完成",
                extra={
                    "request_id": request_id,
                    "duration": duration,
                    "status_code": status_code,
                    "event_type": "request_complete",
                    "slow_request": duration > 2.0
                }
            )
        
        # 清理请求记录
        del self.active_requests[request_id]
        
        # 清理请求上下文
        request_id_var.set(None)
    
    def get_active_requests(self) -> Dict[str, Dict[str, Any]]:
        """获取活跃请求列表"""
        return self.active_requests.copy()


class PerformanceMetrics:
    """性能指标收集器"""
    
    def __init__(self):
        self.metrics = {
            "request_count": 0,
            "error_count": 0,
            "total_duration": 0.0,
            "cache_hits": 0,
            "cache_misses": 0,
            "retry_count": 0,
            "slow_requests": 0
        }
        self.start_time = time.time()
    
    def record_request(self, duration: float, success: bool = True, 
                      cache_hit: bool = False, retries: int = 0) -> None:
        """记录请求指标"""
        self.metrics["request_count"] += 1
        self.metrics["total_duration"] += duration
        
        if not success:
            self.metrics["error_count"] += 1
        
        if cache_hit:
            self.metrics["cache_hits"] += 1
        else:
            self.metrics["cache_misses"] += 1
        
        if retries > 0:
            self.metrics["retry_count"] += retries
        
        if duration > 2.0:
            self.metrics["slow_requests"] += 1
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        uptime = time.time() - self.start_time
        request_count = self.metrics["request_count"]
        
        return {
            **self.metrics,
            "uptime": uptime,
            "requests_per_second": request_count / uptime if uptime > 0 else 0,
            "average_duration": (
                self.metrics["total_duration"] / request_count 
                if request_count > 0 else 0
            ),
            "error_rate": (
                self.metrics["error_count"] / request_count 
                if request_count > 0 else 0
            ),
            "cache_hit_rate": (
                self.metrics["cache_hits"] / (self.metrics["cache_hits"] + self.metrics["cache_misses"])
                if (self.metrics["cache_hits"] + self.metrics["cache_misses"]) > 0 else 0
            )
        }
    
    def reset_metrics(self) -> None:
        """重置指标"""
        self.metrics = {key: 0 if isinstance(value, (int, float)) else value 
                       for key, value in self.metrics.items()}
        self.start_time = time.time()


def setup_logging(
    log_level: str = None,
    log_dir: str = None,
    json_format: bool = False,
    enable_file_logging: bool = True,
    enable_console_logging: bool = True
) -> None:
    """设置日志系统"""
    
    # 使用配置或默认值
    log_level = log_level or settings.log_level
    log_dir = log_dir or settings.log_dir
    
    # 确保日志目录存在
    if enable_file_logging:
        os.makedirs(log_dir, exist_ok=True)
    
    # 获取根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建格式器
    formatter = StructuredFormatter(json_format=json_format)
    
    # 控制台处理器
    if enable_console_logging:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        console_handler.setLevel(getattr(logging, log_level.upper(), logging.INFO))
        root_logger.addHandler(console_handler)
    
    # 文件处理器
    if enable_file_logging:
        # 主日志文件
        main_log_file = os.path.join(log_dir, "zentao_mcp.log")
        file_handler = logging.handlers.RotatingFileHandler(
            main_log_file,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)
        root_logger.addHandler(file_handler)
        
        # 错误日志文件
        error_log_file = os.path.join(log_dir, "zentao_mcp_error.log")
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        error_handler.setFormatter(formatter)
        error_handler.setLevel(logging.ERROR)
        root_logger.addHandler(error_handler)
        
        # 健康检查专用日志文件
        health_log_file = os.path.join(log_dir, "zentao_mcp_health.log")
        health_handler = logging.handlers.RotatingFileHandler(
            health_log_file,
            maxBytes=5 * 1024 * 1024,  # 5MB
            backupCount=3,
            encoding='utf-8'
        )
        health_handler.setFormatter(formatter)
        health_handler.setLevel(logging.DEBUG)
        
        # 设置健康检查日志器，只输出到文件
        health_logger = logging.getLogger("zentao_mcp.health_check")
        health_logger.setLevel(logging.DEBUG)
        health_logger.addHandler(health_handler)
        health_logger.propagate = False  # 不传播到父日志器，避免输出到控制台
    
    # 设置第三方库的日志级别
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    
    logger = logging.getLogger("zentao_mcp.logging")
    logger.info(
        "日志系统初始化完成",
        extra={
            "log_level": log_level,
            "log_dir": log_dir,
            "json_format": json_format,
            "file_logging": enable_file_logging,
            "console_logging": enable_console_logging
        }
    )


def get_request_id() -> str:
    """生成新的请求ID"""
    return str(uuid.uuid4())[:8]


def set_request_context(request_id: str, user_id: str = None) -> None:
    """设置请求上下文"""
    request_id_var.set(request_id)
    if user_id:
        user_id_var.set(user_id)


def clear_request_context() -> None:
    """清理请求上下文"""
    request_id_var.set(None)
    user_id_var.set(None)


# 全局实例
request_tracker = RequestTracker()
performance_metrics = PerformanceMetrics()


class LoggerAdapter(logging.LoggerAdapter):
    """日志适配器，自动添加上下文信息"""
    
    def process(self, msg, kwargs):
        # 添加请求上下文
        extra = kwargs.get('extra', {})
        
        try:
            request_id = request_id_var.get()
            if request_id:
                extra['request_id'] = request_id
        except LookupError:
            pass  # 上下文变量未设置
        
        try:
            user_id = user_id_var.get()
            if user_id:
                extra['user_id'] = user_id
        except LookupError:
            pass  # 上下文变量未设置
        
        kwargs['extra'] = extra
        return msg, kwargs


def get_logger(name: str) -> LoggerAdapter:
    """获取带上下文的日志器"""
    logger = logging.getLogger(name)
    return LoggerAdapter(logger, {})