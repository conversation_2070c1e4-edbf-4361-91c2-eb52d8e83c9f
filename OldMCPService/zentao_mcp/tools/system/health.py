"""
系统健康检查工具
"""

from typing import Dict, Any
from fastmcp import FastMCP

from ...health_check import health_checker
from ...logging_config import get_logger, performance_metrics


logger = get_logger(__name__)


def register_health_tools(mcp: FastMCP):
    """注册健康检查工具"""
    
    @mcp.tool()
    async def mcp_get_health_status() -> Dict[str, Any]:
        """
        获取服务健康状态
        
        Returns:
            Dict: 包含整体健康状态和各组件详情的字典
        """
        try:
            # 只输出简单的连通性检查信息，详细日志记录到文件
            logger.info("执行健康状态检查")
            
            # 执行健康检查
            await health_checker.check_all_components()
            
            # 获取健康报告
            health_report = health_checker.get_health_report()
            
            # 只输出整体状态，不输出详细的请求信息
            logger.info(f"健康检查完成 - 状态: {health_report['overall_status']}")
            
            return {
                "success": True,
                "data": health_report,
                "message": "健康状态检查完成"
            }
            
        except Exception as e:
            logger.error(f"健康状态检查失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "健康状态检查失败"
            }
    
    @mcp.tool()
    async def mcp_get_performance_metrics() -> Dict[str, Any]:
        """
        获取性能指标
        
        Returns:
            Dict: 包含性能指标的字典
        """
        try:
            logger.info("获取性能指标")
            
            # 获取性能指标
            metrics = performance_metrics.get_metrics()
            
            logger.info(
                "性能指标获取完成",
                extra={
                    "request_count": metrics.get("request_count", 0),
                    "error_rate": metrics.get("error_rate", 0),
                    "average_duration": metrics.get("average_duration", 0)
                }
            )
            
            return {
                "success": True,
                "data": metrics,
                "message": "性能指标获取完成"
            }
            
        except Exception as e:
            logger.error(f"性能指标获取失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "性能指标获取失败"
            }
    
    @mcp.tool()
    async def mcp_reset_performance_metrics() -> Dict[str, Any]:
        """
        重置性能指标
        
        Returns:
            Dict: 操作结果
        """
        try:
            logger.info("重置性能指标")
            
            # 重置性能指标
            performance_metrics.reset_metrics()
            
            logger.info("性能指标重置完成")
            
            return {
                "success": True,
                "message": "性能指标已重置"
            }
            
        except Exception as e:
            logger.error(f"性能指标重置失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "性能指标重置失败"
            }
    
    logger.info("健康检查工具注册完成")