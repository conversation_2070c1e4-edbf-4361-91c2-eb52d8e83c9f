"""
Bug分析增强工具

基于直接接口提供Bug数据聚合和分析功能
使用 mcp_ 前缀命名
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from fastmcp import Context

from ...exceptions import ZentaoAPIError, ValidationError

logger = logging.getLogger(__name__)


def register_bug_analysis_tools(mcp):
    """注册Bug分析增强工具"""
    
    @mcp.tool()
    async def mcp_analyze_bugs_by_dept_and_time(
        start_date: str,
        end_date: str,
        dept_id: int
    ) -> Dict[str, Any]:
        """
        按部门和时间段统计Bug分析
        
        基于直接接口层实现数据聚合和分析功能
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD HH:MM:SS格式)
            end_date: 结束日期 (YYYY-MM-DD HH:MM:SS格式)
            dept_id: 部门ID
            
        Returns:
            Dict: 包含Bug统计分析结果的字典
        """
        try:
            logger.info(f"按部门和时间段统计Bug: 部门 {dept_id}, 时间 {start_date} 到 {end_date}")
            
            # 验证日期格式
            try:
                start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                end_dt = datetime.strptime(end_date, "%Y-%m-%d")
                
                if start_dt > end_dt:
                    raise ValidationError("开始日期不能晚于结束日期")
                    
            except ValueError as e:
                raise ValidationError(f"日期格式错误，应为YYYY-MM-DD格式: {str(e)}")
            
            # 从全局服务器实例获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                
                # 使用直接接口获取部门时间范围内的Bug列表
                response = await client.get_bugs_by_time_range_and_dept_async(start_date, end_date, dept_id)
                
                if not response or response.get("rsCode") != "00000000":
                    logger.warning(f"获取部门时间范围Bug列表失败: {response}")
                    return {
                        "success": False,
                        "message": "获取部门时间范围Bug列表失败",
                        "start_date": start_date,
                        "end_date": end_date,
                        "dept_id": dept_id,
                        "analysis": {}
                    }
                
                bugs_data = response.get("body", [])
                
                if not isinstance(bugs_data, list):
                    logger.warning(f"Bug数据格式错误: {bugs_data}")
                    return {
                        "success": False,
                        "message": "Bug数据格式错误",
                        "start_date": start_date,
                        "end_date": end_date,
                        "dept_id": dept_id,
                        "analysis": {}
                    }
                
                # 转换为标准格式并进行统计分析
                bugs = []
                status_stats = {}
                severity_stats = {}
                project_stats = {}
                assignee_stats = {}
                daily_stats = {}
                
                for bug_data in bugs_data:
                    try:
                        bug = _format_bug_data(bug_data)
                        bugs.append(bug)
                        
                        # 状态统计
                        status = bug.get("status", "unknown")
                        status_stats[status] = status_stats.get(status, 0) + 1
                        
                        # 严重程度统计
                        severity = bug.get("severity", 2)
                        severity_name = _get_severity_name(severity)
                        severity_stats[severity_name] = severity_stats.get(severity_name, 0) + 1
                        
                        # 项目统计
                        project_name = bug.get("projectname", "未知项目")
                        project_stats[project_name] = project_stats.get(project_name, 0) + 1
                        
                        # 指派人统计
                        assignee = bug.get("assignedTo", "未指派")
                        assignee_stats[assignee] = assignee_stats.get(assignee, 0) + 1
                        
                        # 按日统计
                        opened_date = bug.get("openedDate", "")
                        if opened_date:
                            try:
                                if "T" in opened_date:
                                    date_part = opened_date.split("T")[0]
                                else:
                                    date_part = opened_date.split(" ")[0]
                                daily_stats[date_part] = daily_stats.get(date_part, 0) + 1
                            except Exception:
                                pass
                        
                    except Exception as e:
                        logger.warning(f"Bug数据格式化失败: {bug_data}, 错误: {e}")
                        continue
                
                # 计算统计指标
                total_bugs = len(bugs)
                
                # 计算解决率
                resolved_count = status_stats.get("resolved", 0) + status_stats.get("closed", 0)
                resolution_rate = (resolved_count / total_bugs * 100) if total_bugs > 0 else 0
                
                # 计算严重Bug比例
                critical_count = severity_stats.get("严重", 0) + severity_stats.get("冒烟", 0)
                critical_rate = (critical_count / total_bugs * 100) if total_bugs > 0 else 0
                
                # 获取部门信息
                dept_name = "未知部门"
                try:
                    dept_response = await client.get_all_departments_async(parse_model=False)
                    if dept_response and dept_response.get("rsCode") == "00000000":
                        departments = dept_response.get("body", [])
                        for dept in departments:
                            if dept.get("id") == dept_id:
                                dept_name = dept.get("name", "未知部门")
                                break
                except Exception as e:
                    logger.warning(f"获取部门信息失败: {e}")
                
                result = {
                    "success": True,
                    "message": f"部门 {dept_name} 在 {start_date} 到 {end_date} 期间共有 {total_bugs} 个Bug",
                    "start_date": start_date,
                    "end_date": end_date,
                    "dept_id": dept_id,
                    "dept_name": dept_name,
                    "analysis": {
                        "summary": {
                            "total_bugs": total_bugs,
                            "resolved_bugs": resolved_count,
                            "resolution_rate": round(resolution_rate, 2),
                            "critical_bugs": critical_count,
                            "critical_rate": round(critical_rate, 2)
                        },
                        "status_distribution": {
                            status: {
                                "count": count,
                                "percentage": round((count / total_bugs * 100), 2) if total_bugs > 0 else 0
                            }
                            for status, count in status_stats.items()
                        },
                        "severity_distribution": {
                            severity: {
                                "count": count,
                                "percentage": round((count / total_bugs * 100), 2) if total_bugs > 0 else 0
                            }
                            for severity, count in severity_stats.items()
                        },
                        "project_distribution": dict(sorted(project_stats.items(), key=lambda x: x[1], reverse=True)[:10]),  # 前10个项目
                        "assignee_distribution": dict(sorted(assignee_stats.items(), key=lambda x: x[1], reverse=True)[:10]),  # 前10个指派人
                        "daily_trend": dict(sorted(daily_stats.items()))
                    },
                    "bugs": bugs
                }
                
                logger.info(f"部门Bug统计分析完成: {result['message']}")
                return result
                
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"按部门和时间段统计Bug失败: {e}")
            raise ZentaoAPIError(f"按部门和时间段统计Bug失败: {str(e)}")
    
    @mcp.tool()
    async def mcp_filter_bugs_by_criteria(
        bugs_data: Optional[List[Dict[str, Any]]] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        status_filter: Optional[List[str]] = None,
        severity_filter: Optional[List[int]] = None,
        project_filter: Optional[List[str]] = None,
        assignee_filter: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        根据多种条件过滤Bug
        
        基于直接接口层实现数据过滤和分析功能
        
        Args:
            bugs_data: 可选的Bug数据列表，如果不提供则从时间范围获取
            start_date: 开始日期 (YYYY-MM-DD HH:MM:SS格式)
            end_date: 结束日期 (YYYY-MM-DD HH:MM:SS格式)
            status_filter: 状态过滤列表 (active, resolved, closed, released)
            severity_filter: 严重程度过滤列表 (0:冒烟 1:严重 2:一般 3:次要 4:低级)
            project_filter: 项目名称过滤列表
            assignee_filter: 指派人过滤列表
            
        Returns:
            Dict: 包含过滤后Bug列表的字典
        """
        try:
            logger.info(f"根据条件过滤Bug: 状态={status_filter}, 严重程度={severity_filter}, 项目={project_filter}, 指派人={assignee_filter}")
            
            # 获取Bug数据
            if bugs_data is None:
                if start_date and end_date:
                    # 使用直接接口从时间范围获取Bug数据
                    from ..client_utils import get_zentao_client
                    async with get_zentao_client() as client:
                        response = await client.get_bugs_by_time_range_async(start_date, end_date, parse_model=False)
                        if response and response.get("rsCode") == "00000000":
                            bugs_data = []
                            for bug_data in response.get("body", []):
                                try:
                                    bug = _format_bug_data(bug_data)
                                    bugs_data.append(bug)
                                except Exception as e:
                                    logger.warning(f"Bug数据格式化失败: {bug_data}, 错误: {e}")
                                    continue
                        else:
                            bugs_data = []
                else:
                    raise ValidationError("必须提供bugs_data或者start_date和end_date")
            
            if not bugs_data:
                return {
                    "success": True,
                    "message": "没有Bug数据需要过滤",
                    "total_input": 0,
                    "total_filtered": 0,
                    "filtered_bugs": []
                }
            
            # 应用过滤条件
            filtered_bugs = []
            
            for bug in bugs_data:
                # 状态过滤
                if status_filter and bug.get("status") not in status_filter:
                    continue
                
                # 严重程度过滤
                if severity_filter and bug.get("severity") not in severity_filter:
                    continue
                
                # 项目过滤
                if project_filter:
                    project_name = bug.get("projectname", "")
                    if not any(project in project_name for project in project_filter):
                        continue
                
                # 指派人过滤
                if assignee_filter and bug.get("assignedTo") not in assignee_filter:
                    continue
                
                filtered_bugs.append(bug)
            
            # 生成过滤统计
            filter_summary = {
                "applied_filters": {
                    "status": status_filter,
                    "severity": severity_filter,
                    "project": project_filter,
                    "assignee": assignee_filter
                },
                "filter_rate": round((len(filtered_bugs) / len(bugs_data) * 100), 2) if bugs_data else 0
            }
            
            result = {
                "success": True,
                "message": f"过滤完成，从 {len(bugs_data)} 个Bug中筛选出 {len(filtered_bugs)} 个符合条件的Bug",
                "total_input": len(bugs_data),
                "total_filtered": len(filtered_bugs),
                "filter_summary": filter_summary,
                "filtered_bugs": filtered_bugs
            }
            
            logger.info(f"Bug过滤完成: {result['message']}")
            return result
            
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"根据条件过滤Bug失败: {e}")
            raise ZentaoAPIError(f"根据条件过滤Bug失败: {str(e)}")
    
    logger.info("Bug分析增强工具注册完成")


def _get_severity_name(severity: int) -> str:
    """
    获取严重程度名称
    
    Args:
        severity: 严重程度数值
        
    Returns:
        str: 严重程度名称
    """
    severity_names = {
        0: "冒烟",
        1: "严重", 
        2: "一般",
        3: "次要",
        4: "低级"
    }
    return severity_names.get(severity, "未知")


def _format_bug_data(bug_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    格式化Bug数据
    
    Args:
        bug_data: 原始Bug数据
        
    Returns:
        Dict: 格式化后的Bug数据
    """
    return {
        "id": int(bug_data.get("id", 0)),
        "title": str(bug_data.get("title", "")),
        "status": str(bug_data.get("status", "active")),
        "severity": int(bug_data.get("severity", 2)),
        "project": int(bug_data.get("project", 0)),
        "projectname": str(bug_data.get("projectname", "")),
        "assignedTo": str(bug_data.get("assignedTo", "")),
        "openedBy": str(bug_data.get("openedBy", "")),
        "openedDate": str(bug_data.get("openedDate", "")),
        "resolution": str(bug_data.get("resolution", "")),
        "environment": str(bug_data.get("environment", "")),
        "steps": str(bug_data.get("steps", ""))
    }


def _get_severity_name(severity: int) -> str:
    """
    获取严重程度名称
    
    Args:
        severity: 严重程度数值
        
    Returns:
        str: 严重程度名称
    """
    severity_map = {
        0: "冒烟",
        1: "严重", 
        2: "一般",
        3: "次要",
        4: "低级"
    }
    return severity_map.get(severity, "未知")