"""
需求分析增强工具

基于直接接口提供需求数据聚合和分析功能
使用 mcp_ 前缀命名
"""

import logging
from typing import List, Dict, Any, Optional
from fastmcp import Context

from ...exceptions import ZentaoAPIError, ValidationError

logger = logging.getLogger(__name__)


def register_story_analysis_tools(mcp):
    """注册需求分析增强工具"""
    
    @mcp.tool()
    async def mcp_analyze_story_workload(
        story_ids: Optional[List[str]] = None,
        project_id: Optional[int] = None,
        include_completed: bool = True
    ) -> Dict[str, Any]:
        """
        分析需求工时统计
        
        基于直接接口层实现数据聚合和分析功能
        
        Args:
            story_ids: 可选的需求ID列表，如果不提供则分析所有需求
            project_id: 可选的项目ID，如果提供则只分析该项目的需求
            include_completed: 是否包含已完成的需求工时
            
        Returns:
            Dict: 包含工时分析结果的字典
        """
        try:
            logger.info(f"分析需求工时统计，需求ID: {story_ids}, 项目ID: {project_id}, 包含已完成: {include_completed}")
            
            # 获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                
                stories_to_analyze = []
                
                if story_ids:
                    # 使用直接接口获取指定需求
                    from ..direct.story import zentao_get_story_effort
                    response = await client.get_story_info_async(story_ids)
                    if response and response.get("rsCode") == "00000000":
                        stories_data = response.get("body", [])
                        for story_data in stories_data:
                            try:
                                story = {
                                    "id": int(story_data.get("id", 0)),
                                    "title": str(story_data.get("title", "")),
                                    "estimate": float(story_data.get("estimate", 0.0)),
                                    "status": str(story_data.get("status", "active")),
                                    "stage": str(story_data.get("stage", "wait")),
                                    "pri": int(story_data.get("pri", 3))
                                }
                                stories_to_analyze.append(story)
                            except (ValueError, TypeError) as e:
                                logger.warning(f"需求数据格式错误: {story_data}, 错误: {e}")
                                continue
                elif project_id:
                    # 使用直接接口获取项目需求
                    response = await client.get_stories_by_project_async(project_id)
                    if response and response.get("rsCode") == "00000000":
                        stories_data = response.get("body", [])
                        for story_data in stories_data:
                            try:
                                story = {
                                    "id": int(story_data.get("id", 0)),
                                    "title": str(story_data.get("title", "")),
                                    "estimate": float(story_data.get("estimate", 0.0)),
                                    "status": str(story_data.get("status", "active")),
                                    "stage": str(story_data.get("stage", "wait")),
                                    "pri": int(story_data.get("pri", 3))
                                }
                                stories_to_analyze.append(story)
                            except (ValueError, TypeError) as e:
                                logger.warning(f"需求数据格式错误: {story_data}, 错误: {e}")
                                continue
                else:
                    raise ValidationError("必须提供story_ids或project_id参数")
                
                if not stories_to_analyze:
                    return {
                        "success": False,
                        "message": "没有找到需要分析的需求",
                        "total_stories": 0,
                        "analysis": {}
                    }
                
                # 进行工时分析
                total_stories = len(stories_to_analyze)
                total_estimate = 0.0
                completed_estimate = 0.0
                active_estimate = 0.0
                
                status_workload = {
                    "active": {"count": 0, "estimate": 0.0},
                    "closed": {"count": 0, "estimate": 0.0}
                }
                
                stage_workload = {
                    "wait": {"count": 0, "estimate": 0.0},
                    "planned": {"count": 0, "estimate": 0.0},
                    "developing": {"count": 0, "estimate": 0.0},
                    "released": {"count": 0, "estimate": 0.0}
                }
                
                priority_workload = {
                    1: {"count": 0, "estimate": 0.0},  # 高
                    2: {"count": 0, "estimate": 0.0},  # 中
                    3: {"count": 0, "estimate": 0.0},  # 普通
                    4: {"count": 0, "estimate": 0.0}   # 低
                }
                
                # 统计分析
                for story in stories_to_analyze:
                    estimate = story.get("estimate", 0.0)
                    status = story.get("status", "active")
                    stage = story.get("stage", "wait")
                    priority = story.get("pri", 3)
                    
                    total_estimate += estimate
                    
                    # 状态统计
                    if status == "closed":
                        completed_estimate += estimate
                        if include_completed:
                            status_workload["closed"]["count"] += 1
                            status_workload["closed"]["estimate"] += estimate
                    else:
                        active_estimate += estimate
                        status_workload["active"]["count"] += 1
                        status_workload["active"]["estimate"] += estimate
                    
                    # 阶段统计
                    if stage in stage_workload:
                        stage_workload[stage]["count"] += 1
                        stage_workload[stage]["estimate"] += estimate
                    
                    # 优先级统计
                    if priority in priority_workload:
                        priority_workload[priority]["count"] += 1
                        priority_workload[priority]["estimate"] += estimate
                
                # 计算完成率和平均工时
                completion_rate = (completed_estimate / total_estimate * 100) if total_estimate > 0 else 0
                avg_estimate = total_estimate / total_stories if total_stories > 0 else 0
                
                # 获取已完成需求的实际工时（如果需要）
                completed_actual_hours = 0.0
                if include_completed and story_ids:
                    try:
                        completed_response = await client.get_story_end_async(story_ids)
                        if completed_response and completed_response.get("rsCode") == "00000000":
                            completed_data = completed_response.get("body", [])
                            for completed_story in completed_data:
                                completed_actual_hours += float(completed_story.get("consumed", 0.0))
                    except Exception as e:
                        logger.warning(f"获取已完成需求实际工时失败: {e}")
                
                result = {
                    "success": True,
                    "message": f"工时分析完成，共分析 {total_stories} 个需求",
                    "total_stories": total_stories,
                    "analysis": {
                        "total_estimate_hours": round(total_estimate, 2),
                        "completed_estimate_hours": round(completed_estimate, 2),
                        "active_estimate_hours": round(active_estimate, 2),
                        "completed_actual_hours": round(completed_actual_hours, 2),
                        "average_estimate_hours": round(avg_estimate, 2),
                        "completion_rate": round(completion_rate, 2),
                        "status_breakdown": {
                            status: {
                                "count": data["count"],
                                "estimate_hours": round(data["estimate"], 2),
                                "percentage": round((data["estimate"] / total_estimate * 100), 2) if total_estimate > 0 else 0
                            }
                            for status, data in status_workload.items()
                        },
                        "stage_breakdown": {
                            stage: {
                                "count": data["count"],
                                "estimate_hours": round(data["estimate"], 2),
                                "percentage": round((data["estimate"] / total_estimate * 100), 2) if total_estimate > 0 else 0
                            }
                            for stage, data in stage_workload.items()
                        },
                        "priority_breakdown": {
                            f"priority_{priority}": {
                                "count": data["count"],
                                "estimate_hours": round(data["estimate"], 2),
                                "percentage": round((data["estimate"] / total_estimate * 100), 2) if total_estimate > 0 else 0
                            }
                            for priority, data in priority_workload.items()
                        }
                    }
                }
                
                logger.info(f"需求工时分析完成: 总工时 {result['analysis']['total_estimate_hours']} 小时，完成率 {result['analysis']['completion_rate']}%")
                return result
                
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"分析需求工时统计失败: {e}")
            raise ZentaoAPIError(f"分析需求工时统计失败: {str(e)}")
    
    logger.info("需求分析增强工具注册完成")