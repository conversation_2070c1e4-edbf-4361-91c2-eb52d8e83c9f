"""
项目分析增强工具

基于直接接口提供项目数据聚合和分析功能
使用 mcp_ 前缀命名

实现的分析工具：
1. mcp_project_summary_analysis - 项目整体统计工具
2. mcp_story_task_relation_query - 需求-任务关联查询工具  
3. mcp_bug_to_story_tracking - Bug转需求追踪工具
4. mcp_personnel_workload_analysis - 人员工作量统计和分析工具
"""

import logging
from typing import List, Dict, Any, Optional
from fastmcp import Context

from ...exceptions import ZentaoAPIError, ValidationError

logger = logging.getLogger(__name__)


def register_project_analysis_tools(mcp):
    """注册项目分析增强工具"""
    
    @mcp.tool()
    async def mcp_project_summary_analysis(
        project_id: int,
    ) -> Dict[str, Any]:
        """
        项目整体统计工具，包含需求、任务、Bug 数量
        
        基于直接接口层实现项目数据聚合和分析功能
        
        Args:
            project_id: 项目ID
            
        Returns:
            Dict: 包含项目统计分析结果的字典
        """
        try:
            logger.info(f"开始分析项目 {project_id} 的整体统计信息")
            
            # 从全局服务器实例获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                
                # 获取项目基本信息
                projects_response = await client.get_all_projects_async(parse_model=False)
                if not projects_response or projects_response.get("rsCode") != "********":
                    raise ZentaoAPIError("获取项目列表失败")
                
                project_info = None
                for project in projects_response.get("body", []):
                    if project.get("id") == project_id:
                        project_info = project
                        break
                
                if not project_info:
                    raise ValidationError(f"项目 {project_id} 不存在")
                
                # 并行获取项目相关数据
                stories_response = await client.get_stories_by_project_async(project_id)
                tasks_response = await client.get_tasks_by_project_async(project_id)
                bugs_response = await client.get_bugs_by_project_async(project_id)
                
                # 处理需求数据
                stories_data = []
                if stories_response and stories_response.get("rsCode") == "********":
                    stories_data = stories_response.get("body", [])
                
                # 处理任务数据
                tasks_data = []
                if tasks_response and tasks_response.get("rsCode") == "********":
                    tasks_data = tasks_response.get("body", [])
                
                # 处理Bug数据
                bugs_data = []
                if bugs_response and bugs_response.get("rsCode") == "********":
                    bugs_data = bugs_response.get("body", [])
                
                # 统计分析
                analysis = _analyze_project_data(project_info, stories_data, tasks_data, bugs_data)
                
                result = {
                    "success": True,
                    "message": f"项目 {project_info.get('name', '')} 统计分析完成",
                    "project_id": project_id,
                    "project_name": project_info.get("name", ""),
                    "analysis": analysis
                }
                
                logger.info(f"项目 {project_id} 统计分析完成")
                return result
                
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"项目统计分析失败: {e}")
            raise ZentaoAPIError(f"项目统计分析失败: {str(e)}")
    
    @mcp.tool()
    async def mcp_story_task_relation_query(
        story_ids: Optional[List[str]] = None,
        project_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        创建需求-任务关联查询工具
        
        基于直接接口层实现需求-任务关联分析功能
        
        Args:
            story_ids: 可选的需求ID列表
            project_id: 可选的项目ID，如果提供则查询该项目的需求-任务关联
            
        Returns:
            Dict: 包含需求-任务关联分析结果的字典
        """
        try:
            logger.info(f"开始查询需求-任务关联，需求ID: {story_ids}, 项目ID: {project_id}")
            
            # 从全局服务器实例获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                
                # 获取需求数据
                stories_data = []
                if story_ids:
                    # 查询指定需求
                    response = await client.get_story_info_async(story_ids)
                    if response and response.get("rsCode") == "********":
                        stories_data = response.get("body", [])
                elif project_id:
                    # 查询项目需求
                    stories_response = await client.get_stories_by_project_async(project_id)
                    if stories_response and stories_response.get("rsCode") == "********":
                        stories_data = stories_response.get("body", [])
                else:
                    raise ValidationError("必须提供story_ids或project_id参数")
                
                if not stories_data:
                    return {
                        "success": True,
                        "message": "没有找到需求数据",
                        "relations": []
                    }
                
                # 获取相关任务数据
                relations = []
                for story in stories_data:
                    story_id = story.get("id")
                    story_title = story.get("title", "")
                    
                    # 获取与该需求相关的任务
                    if project_id:
                        tasks_response = await client.get_tasks_by_project_async(project_id)
                        if tasks_response and tasks_response.get("rsCode") == "********":
                            tasks_data = tasks_response.get("body", [])
                            
                            # 筛选与当前需求相关的任务
                            related_tasks = []
                            for task in tasks_data:
                                # 检查任务是否与需求相关（通过story字段或名称匹配）
                                task_story = task.get("story", 0)
                                if task_story == story_id or str(story_id) in task.get("name", ""):
                                    related_tasks.append({
                                        "id": task.get("id"),
                                        "name": task.get("name", ""),
                                        "status": task.get("status", ""),
                                        "assignedTo": task.get("assignedTo", ""),
                                        "estimate": task.get("estimate", 0),
                                        "consumed": task.get("consumed", 0),
                                        "left": task.get("left", 0)
                                    })
                            
                            relations.append({
                                "story": {
                                    "id": story_id,
                                    "title": story_title,
                                    "status": story.get("status", ""),
                                    "stage": story.get("stage", ""),
                                    "estimate": story.get("estimate", 0)
                                },
                                "tasks": related_tasks,
                                "task_count": len(related_tasks),
                                "total_task_estimate": sum(t.get("estimate", 0) for t in related_tasks),
                                "total_task_consumed": sum(t.get("consumed", 0) for t in related_tasks)
                            })
                
                # 生成关联分析统计
                total_stories = len(relations)
                total_tasks = sum(r["task_count"] for r in relations)
                stories_with_tasks = len([r for r in relations if r["task_count"] > 0])
                
                result = {
                    "success": True,
                    "message": f"需求-任务关联查询完成，共 {total_stories} 个需求，{total_tasks} 个相关任务",
                    "summary": {
                        "total_stories": total_stories,
                        "total_tasks": total_tasks,
                        "stories_with_tasks": stories_with_tasks,
                        "coverage_rate": round((stories_with_tasks / total_stories * 100), 2) if total_stories > 0 else 0
                    },
                    "relations": relations
                }
                
                logger.info(f"需求-任务关联查询完成: {result['message']}")
                return result
                
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"需求-任务关联查询失败: {e}")
            raise ZentaoAPIError(f"需求-任务关联查询失败: {str(e)}")
    
    @mcp.tool()
    async def mcp_bug_to_story_tracking(
        bug_ids: Optional[List[str]] = None,
        project_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        实现Bug转需求的追踪工具
        
        基于直接接口层实现Bug转需求追踪功能
        
        Args:
            bug_ids: 可选的Bug ID列表
            project_id: 可选的项目ID，如果提供则分析该项目的Bug转需求情况
            
        Returns:
            Dict: 包含Bug转需求追踪结果的字典
        """
        try:
            logger.info(f"开始Bug转需求追踪，Bug ID: {bug_ids}, 项目ID: {project_id}")
            
            # 从全局服务器实例获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                
                # 获取Bug数据
                bugs_data = []
                if bug_ids:
                    # 查询指定Bug
                    for bug_id in bug_ids:
                        try:
                            bug_response = await client.get_bug_detail_async(int(bug_id))
                            if bug_response and bug_response.get("rsCode") == "********":
                                bug_detail = bug_response.get("body")
                                if bug_detail:
                                    bugs_data.append(bug_detail)
                        except Exception as e:
                            logger.warning(f"获取Bug {bug_id} 详情失败: {e}")
                elif project_id:
                    # 查询项目Bug
                    bugs_response = await client.get_bugs_by_project_async(project_id)
                    if bugs_response and bugs_response.get("rsCode") == "********":
                        bugs_data = bugs_response.get("body", [])
                else:
                    raise ValidationError("必须提供bug_ids或project_id参数")
                
                if not bugs_data:
                    return {
                        "success": True,
                        "message": "没有找到Bug数据",
                        "tracking_results": []
                    }
                
                # 获取项目需求数据用于关联分析
                stories_data = []
                if project_id:
                    stories_response = await client.get_stories_by_project_async(project_id)
                    if stories_response and stories_response.get("rsCode") == "********":
                        stories_data = stories_response.get("body", [])
                
                # 分析Bug转需求的追踪
                tracking_results = []
                for bug in bugs_data:
                    bug_id = bug.get("id")
                    bug_title = bug.get("title", "")
                    bug_status = bug.get("status", "")
                    
                    # 查找相关需求
                    related_stories = []
                    
                    # 方法1: 通过Bug的story字段直接关联
                    bug_story_id = bug.get("story", 0)
                    if bug_story_id and bug_story_id > 0:
                        for story in stories_data:
                            if story.get("id") == bug_story_id:
                                related_stories.append({
                                    "id": story.get("id"),
                                    "title": story.get("title", ""),
                                    "status": story.get("status", ""),
                                    "stage": story.get("stage", ""),
                                    "relation_type": "direct_link"
                                })
                                break
                    
                    # 方法2: 通过标题关键词匹配
                    if not related_stories:
                        bug_keywords = set(bug_title.lower().split())
                        for story in stories_data:
                            story_keywords = set(story.get("title", "").lower().split())
                            # 如果有共同关键词，认为可能相关
                            common_keywords = bug_keywords.intersection(story_keywords)
                            if len(common_keywords) >= 2:  # 至少2个共同关键词
                                related_stories.append({
                                    "id": story.get("id"),
                                    "title": story.get("title", ""),
                                    "status": story.get("status", ""),
                                    "stage": story.get("stage", ""),
                                    "relation_type": "keyword_match",
                                    "common_keywords": list(common_keywords)
                                })
                    
                    # 判断Bug是否已转为需求
                    converted_to_story = len(related_stories) > 0
                    
                    tracking_results.append({
                        "bug": {
                            "id": bug_id,
                            "title": bug_title,
                            "status": bug_status,
                            "severity": bug.get("severity", 2),
                            "priority": bug.get("pri", 3),
                            "openedDate": bug.get("openedDate", "")
                        },
                        "converted_to_story": converted_to_story,
                        "related_stories": related_stories,
                        "story_count": len(related_stories)
                    })
                
                # 生成追踪统计
                total_bugs = len(tracking_results)
                converted_bugs = len([r for r in tracking_results if r["converted_to_story"]])
                conversion_rate = (converted_bugs / total_bugs * 100) if total_bugs > 0 else 0
                
                result = {
                    "success": True,
                    "message": f"Bug转需求追踪完成，共 {total_bugs} 个Bug，{converted_bugs} 个已转为需求",
                    "summary": {
                        "total_bugs": total_bugs,
                        "converted_bugs": converted_bugs,
                        "unconverted_bugs": total_bugs - converted_bugs,
                        "conversion_rate": round(conversion_rate, 2)
                    },
                    "tracking_results": tracking_results
                }
                
                logger.info(f"Bug转需求追踪完成: {result['message']}")
                return result
                
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"Bug转需求追踪失败: {e}")
            raise ZentaoAPIError(f"Bug转需求追踪失败: {str(e)}")
    
    @mcp.tool()
    async def mcp_personnel_workload_analysis(
        user_accounts: Optional[List[str]] = None,
        project_id: Optional[int] = None,
        dept_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        支持人员工作量统计和分析
        
        基于直接接口层实现人员工作量分析功能
        
        Args:
            user_accounts: 可选的用户账号列表
            project_id: 可选的项目ID，如果提供则分析该项目的人员工作量
            dept_id: 可选的部门ID，如果提供则分析该部门的人员工作量
            
        Returns:
            Dict: 包含人员工作量分析结果的字典
        """
        try:
            logger.info(f"开始人员工作量分析，用户: {user_accounts}, 项目ID: {project_id}, 部门ID: {dept_id}")
            
            # 从全局服务器实例获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                
                # 获取分析范围内的数据
                stories_data = []
                tasks_data = []
                bugs_data = []
                
                if project_id:
                    # 获取项目相关数据
                    stories_response = await client.get_stories_by_project_async(project_id)
                    if stories_response and stories_response.get("rsCode") == "********":
                        stories_data = stories_response.get("body", [])
                    
                    tasks_response = await client.get_tasks_by_project_async(project_id)
                    if tasks_response and tasks_response.get("rsCode") == "********":
                        tasks_data = tasks_response.get("body", [])
                    
                    bugs_response = await client.get_bugs_by_project_async(project_id)
                    if bugs_response and bugs_response.get("rsCode") == "********":
                        bugs_data = bugs_response.get("body", [])
                
                # 分析人员工作量
                personnel_stats = {}
                
                # 从需求中统计工作量
                for story in stories_data:
                    opened_by = story.get("openedBy", "")
                    assigned_to = story.get("assignedTo", "")
                    estimate = float(story.get("estimate", 0))
                    
                    # 如果指定了用户列表，只分析这些用户
                    if user_accounts and opened_by not in user_accounts and assigned_to not in user_accounts:
                        continue
                    
                    # 统计创建者工作量
                    if opened_by:
                        if opened_by not in personnel_stats:
                            personnel_stats[opened_by] = {
                                "stories_created": 0,
                                "stories_assigned": 0,
                                "tasks_assigned": 0,
                                "bugs_assigned": 0,
                                "total_story_estimate": 0.0,
                                "total_task_estimate": 0.0,
                                "total_task_consumed": 0.0,
                                "total_task_left": 0.0
                            }
                        personnel_stats[opened_by]["stories_created"] += 1
                    
                    # 统计指派者工作量
                    if assigned_to:
                        if assigned_to not in personnel_stats:
                            personnel_stats[assigned_to] = {
                                "stories_created": 0,
                                "stories_assigned": 0,
                                "tasks_assigned": 0,
                                "bugs_assigned": 0,
                                "total_story_estimate": 0.0,
                                "total_task_estimate": 0.0,
                                "total_task_consumed": 0.0,
                                "total_task_left": 0.0
                            }
                        personnel_stats[assigned_to]["stories_assigned"] += 1
                        personnel_stats[assigned_to]["total_story_estimate"] += estimate
                
                # 从任务中统计工作量
                for task in tasks_data:
                    assigned_to = task.get("assignedTo", "")
                    estimate = float(task.get("estimate", 0))
                    consumed = float(task.get("consumed", 0))
                    left = float(task.get("left", 0))
                    
                    if user_accounts and assigned_to not in user_accounts:
                        continue
                    
                    if assigned_to:
                        if assigned_to not in personnel_stats:
                            personnel_stats[assigned_to] = {
                                "stories_created": 0,
                                "stories_assigned": 0,
                                "tasks_assigned": 0,
                                "bugs_assigned": 0,
                                "total_story_estimate": 0.0,
                                "total_task_estimate": 0.0,
                                "total_task_consumed": 0.0,
                                "total_task_left": 0.0
                            }
                        personnel_stats[assigned_to]["tasks_assigned"] += 1
                        personnel_stats[assigned_to]["total_task_estimate"] += estimate
                        personnel_stats[assigned_to]["total_task_consumed"] += consumed
                        personnel_stats[assigned_to]["total_task_left"] += left
                
                # 从Bug中统计工作量
                for bug in bugs_data:
                    assigned_to = bug.get("assignedTo", "")
                    
                    if user_accounts and assigned_to not in user_accounts:
                        continue
                    
                    if assigned_to:
                        if assigned_to not in personnel_stats:
                            personnel_stats[assigned_to] = {
                                "stories_created": 0,
                                "stories_assigned": 0,
                                "tasks_assigned": 0,
                                "bugs_assigned": 0,
                                "total_story_estimate": 0.0,
                                "total_task_estimate": 0.0,
                                "total_task_consumed": 0.0,
                                "total_task_left": 0.0
                            }
                        personnel_stats[assigned_to]["bugs_assigned"] += 1
                
                # 计算每个人员的工作量指标
                workload_analysis = []
                for user_account, stats in personnel_stats.items():
                    total_estimate = stats["total_story_estimate"] + stats["total_task_estimate"]
                    total_consumed = stats["total_task_consumed"]
                    total_left = stats["total_task_left"]
                    
                    # 计算工作效率
                    efficiency = (total_consumed / total_estimate * 100) if total_estimate > 0 else 0
                    
                    # 计算工作负荷
                    workload_score = (
                        stats["stories_assigned"] * 2 +
                        stats["tasks_assigned"] * 1 +
                        stats["bugs_assigned"] * 1.5
                    )
                    
                    workload_analysis.append({
                        "user_account": user_account,
                        "statistics": stats,
                        "metrics": {
                            "total_estimate_hours": round(total_estimate, 2),
                            "total_consumed_hours": round(total_consumed, 2),
                            "total_remaining_hours": round(total_left, 2),
                            "work_efficiency": round(efficiency, 2),
                            "workload_score": round(workload_score, 2)
                        }
                    })
                
                # 按工作负荷排序
                workload_analysis.sort(key=lambda x: x["metrics"]["workload_score"], reverse=True)
                
                # 生成汇总统计
                total_users = len(workload_analysis)
                total_workload = sum(w["metrics"]["workload_score"] for w in workload_analysis)
                avg_workload = total_workload / total_users if total_users > 0 else 0
                
                result = {
                    "success": True,
                    "message": f"人员工作量分析完成，共分析 {total_users} 个人员",
                    "summary": {
                        "total_users": total_users,
                        "total_workload_score": round(total_workload, 2),
                        "average_workload_score": round(avg_workload, 2),
                        "analysis_scope": {
                            "project_id": project_id,
                            "dept_id": dept_id,
                            "user_accounts": user_accounts
                        }
                    },
                    "workload_analysis": workload_analysis
                }
                
                logger.info(f"人员工作量分析完成: {result['message']}")
                return result
                
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"人员工作量分析失败: {e}")
            raise ZentaoAPIError(f"人员工作量分析失败: {str(e)}")
    
    logger.info("项目分析增强工具注册完成")


def _analyze_project_data(project_info: Dict, stories_data: List, tasks_data: List, bugs_data: List) -> Dict[str, Any]:
    """
    分析项目数据并生成统计报告
    
    Args:
        project_info: 项目基本信息
        stories_data: 需求数据列表
        tasks_data: 任务数据列表
        bugs_data: Bug数据列表
        
    Returns:
        Dict: 项目分析结果
    """
    # 需求统计
    story_stats = _analyze_stories(stories_data)
    
    # 任务统计
    task_stats = _analyze_tasks(tasks_data)
    
    # Bug统计
    bug_stats = _analyze_bugs(bugs_data)
    
    # 项目进度计算
    progress = _calculate_project_progress(story_stats, task_stats)
    
    # 项目健康度评估
    health_score = _calculate_project_health(story_stats, task_stats, bug_stats, progress)
    
    return {
        "overview": {
            "total_stories": len(stories_data),
            "total_tasks": len(tasks_data),
            "total_bugs": len(bugs_data),
            "project_status": project_info.get("status", "unknown"),
            "project_progress": progress,
            "health_score": health_score
        },
        "story_analysis": story_stats,
        "task_analysis": task_stats,
        "bug_analysis": bug_stats,
        "workload_summary": {
            "estimated_hours": story_stats.get("total_estimate", 0) + task_stats.get("total_estimate", 0),
            "consumed_hours": task_stats.get("total_consumed", 0),
            "remaining_hours": task_stats.get("total_left", 0)
        }
    }


def _analyze_stories(stories_data: List) -> Dict[str, Any]:
    """分析需求数据"""
    if not stories_data:
        return {
            "total_count": 0,
            "status_distribution": {},
            "stage_distribution": {},
            "priority_distribution": {},
            "total_estimate": 0.0
        }
    
    status_count = {}
    stage_count = {}
    priority_count = {}
    total_estimate = 0.0
    
    for story in stories_data:
        # 状态统计
        status = story.get("status", "unknown")
        status_count[status] = status_count.get(status, 0) + 1
        
        # 阶段统计
        stage = story.get("stage", "unknown")
        stage_count[stage] = stage_count.get(stage, 0) + 1
        
        # 优先级统计
        priority = story.get("pri", 3)
        priority_count[priority] = priority_count.get(priority, 0) + 1
        
        # 工时统计
        estimate = float(story.get("estimate", 0))
        total_estimate += estimate
    
    return {
        "total_count": len(stories_data),
        "status_distribution": status_count,
        "stage_distribution": stage_count,
        "priority_distribution": priority_count,
        "total_estimate": round(total_estimate, 2)
    }


def _analyze_tasks(tasks_data: List) -> Dict[str, Any]:
    """分析任务数据"""
    if not tasks_data:
        return {
            "total_count": 0,
            "status_distribution": {},
            "type_distribution": {},
            "total_estimate": 0.0,
            "total_consumed": 0.0,
            "total_left": 0.0
        }
    
    status_count = {}
    type_count = {}
    total_estimate = 0.0
    total_consumed = 0.0
    total_left = 0.0
    
    for task in tasks_data:
        # 状态统计
        status = task.get("status", "unknown")
        status_count[status] = status_count.get(status, 0) + 1
        
        # 类型统计
        task_type = task.get("type", "unknown")
        type_count[task_type] = type_count.get(task_type, 0) + 1
        
        # 工时统计
        estimate = float(task.get("estimate", 0))
        consumed = float(task.get("consumed", 0))
        left = float(task.get("left", 0))
        
        total_estimate += estimate
        total_consumed += consumed
        total_left += left
    
    return {
        "total_count": len(tasks_data),
        "status_distribution": status_count,
        "type_distribution": type_count,
        "total_estimate": round(total_estimate, 2),
        "total_consumed": round(total_consumed, 2),
        "total_left": round(total_left, 2)
    }


def _analyze_bugs(bugs_data: List) -> Dict[str, Any]:
    """分析Bug数据"""
    if not bugs_data:
        return {
            "total_count": 0,
            "status_distribution": {},
            "severity_distribution": {},
            "priority_distribution": {}
        }
    
    status_count = {}
    severity_count = {}
    priority_count = {}
    
    for bug in bugs_data:
        # 状态统计
        status = bug.get("status", "unknown")
        status_count[status] = status_count.get(status, 0) + 1
        
        # 严重程度统计
        severity = bug.get("severity", 2)
        severity_count[severity] = severity_count.get(severity, 0) + 1
        
        # 优先级统计
        priority = bug.get("pri", 3)
        priority_count[priority] = priority_count.get(priority, 0) + 1
    
    return {
        "total_count": len(bugs_data),
        "status_distribution": status_count,
        "severity_distribution": severity_count,
        "priority_distribution": priority_count
    }


def _calculate_project_progress(story_stats: Dict, task_stats: Dict) -> Dict[str, Any]:
    """计算项目进度"""
    # 基于需求完成情况计算进度
    story_total = story_stats.get("total_count", 0)
    story_closed = story_stats.get("status_distribution", {}).get("closed", 0)
    story_progress = (story_closed / story_total * 100) if story_total > 0 else 0
    
    # 基于任务完成情况计算进度
    task_total = task_stats.get("total_count", 0)
    task_done = task_stats.get("status_distribution", {}).get("done", 0)
    task_progress = (task_done / task_total * 100) if task_total > 0 else 0
    
    # 基于工时消耗计算进度
    total_estimate = task_stats.get("total_estimate", 0)
    total_consumed = task_stats.get("total_consumed", 0)
    workload_progress = (total_consumed / total_estimate * 100) if total_estimate > 0 else 0
    
    # 综合进度（加权平均）
    overall_progress = (story_progress * 0.4 + task_progress * 0.4 + workload_progress * 0.2)
    
    return {
        "story_progress": round(story_progress, 2),
        "task_progress": round(task_progress, 2),
        "workload_progress": round(workload_progress, 2),
        "overall_progress": round(overall_progress, 2)
    }


def _calculate_project_health(story_stats: Dict, task_stats: Dict, bug_stats: Dict, progress: Dict) -> Dict[str, Any]:
    """计算项目健康度"""
    # 基础健康度评分
    health_score = 100.0
    
    # 进度健康度（权重30%）
    progress_score = progress.get("overall_progress", 0)
    if progress_score < 50:
        health_score -= (50 - progress_score) * 0.3
    
    # Bug健康度（权重25%）
    total_bugs = bug_stats.get("total_count", 0)
    total_stories = story_stats.get("total_count", 1)  # 避免除零
    bug_ratio = total_bugs / total_stories
    if bug_ratio > 0.3:  # Bug与需求比例超过30%
        health_score -= (bug_ratio - 0.3) * 100 * 0.25
    
    # 工时健康度（权重25%）
    total_estimate = task_stats.get("total_estimate", 0)
    total_consumed = task_stats.get("total_consumed", 0)
    if total_estimate > 0:
        time_efficiency = total_consumed / total_estimate
        if time_efficiency > 1.2:  # 超时20%以上
            health_score -= (time_efficiency - 1.2) * 100 * 0.25
    
    # 任务分布健康度（权重20%）
    task_status_dist = task_stats.get("status_distribution", {})
    total_tasks = task_stats.get("total_count", 1)
    doing_tasks = task_status_dist.get("doing", 0)
    if total_tasks > 0:
        doing_ratio = doing_tasks / total_tasks
        if doing_ratio > 0.5:  # 进行中任务超过50%
            health_score -= (doing_ratio - 0.5) * 100 * 0.2
    
    # 确保健康度在0-100范围内
    health_score = max(0, min(100, health_score))
    
    # 健康度等级
    if health_score >= 80:
        health_level = "优秀"
    elif health_score >= 60:
        health_level = "良好"
    elif health_score >= 40:
        health_level = "一般"
    else:
        health_level = "需要关注"
    
    return {
        "score": round(health_score, 2),
        "level": health_level,
        "factors": {
            "progress_health": round(progress_score, 2),
            "bug_ratio": round(bug_ratio, 2),
            "time_efficiency": round(total_consumed / total_estimate, 2) if total_estimate > 0 else 0,
            "task_distribution": round(doing_ratio, 2) if total_tasks > 0 else 0
        }
    }
