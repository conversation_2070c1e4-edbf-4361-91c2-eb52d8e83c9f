"""
禅道MCP工具模块

重构后的工具架构：
- direct/: 直接接口层工具，使用 zentao_ 前缀
- analysis/: 分析增强层工具，使用 mcp_ 前缀
"""

from .direct.department import register_department_direct_tools
from .direct.project import register_project_direct_tools
from .direct.story import register_story_direct_tools
from .direct.task import register_task_direct_tools
from .direct.bug import register_bug_direct_tools
from .direct.user import register_user_direct_tools

from .analysis.story import register_story_analysis_tools
from .analysis.bug import register_bug_analysis_tools
from .analysis.project import register_project_analysis_tools

from .system.health import register_health_tools


def register_all_tools(mcp):
    """注册所有工具到MCP服务器"""
    
    # 注册直接接口层工具
    register_department_direct_tools(mcp)
    register_project_direct_tools(mcp)
    register_story_direct_tools(mcp)
    register_task_direct_tools(mcp)
    register_bug_direct_tools(mcp)
    register_user_direct_tools(mcp)
    
    # 注册分析增强层工具
    register_story_analysis_tools(mcp)
    register_bug_analysis_tools(mcp)
    register_project_analysis_tools(mcp)
    
    # 注册系统工具
    register_health_tools(mcp)
