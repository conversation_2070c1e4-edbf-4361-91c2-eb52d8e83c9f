"""
任务管理直接接口工具

提供与禅道任务API 1:1对应的直接查询接口
"""

import logging
from typing import Dict, Any
from fastmcp import Context

from ...exceptions import ZentaoAPIError

logger = logging.getLogger(__name__)


def register_task_direct_tools(mcp):
    """注册任务管理直接接口工具"""
    
    @mcp.tool()
    async def zentao_get_task_detail(task_id: int) -> Dict[str, Any]:
        """
        根据任务ID查询详情
        
        直接调用禅道API: /api/getTaskById
        保持原始数据结构
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            logger.info(f"调用禅道API根据任务ID {task_id} 查询详情")
            
            # 从全局服务器实例获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                response = await client.get_task_by_id_async(task_id)
                
                if not response:
                    raise ZentaoAPIError("禅道API响应为空")
                
                logger.info(f"成功获取任务 {task_id} 详情，响应码: {response.get('rsCode')}")
                return response
                
        except Exception as e:
            logger.error(f"根据任务ID查询详情失败: {e}")
            raise ZentaoAPIError(f"根据任务ID查询详情失败: {str(e)}")
    
    @mcp.tool()
    async def zentao_get_tasks_by_account(account: str, start_date: str, end_date: str, is_doing: bool) -> Dict[str, Any]:
        """
        根据域账号查询任务
        
        直接调用禅道API: /api/getTaskByAccount
        保持原始数据结构
        
        Args:
            account: 域账号
            start_date: 开始日期 (YYYY-MM-DD HH:MM:SS格式)
            end_date: 结束日期 (YYYY-MM-DD HH:MM:SS格式)
            is_doing: 是否只查询进行中的任务
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            logger.info(f"调用禅道API根据域账号 {account} 查询任务，时间: {start_date} 到 {end_date}, 进行中: {is_doing}")
            
            # 从全局服务器实例获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                response = await client.get_tasks_by_account_async(account, start_date, end_date, is_doing)
                
                if not response:
                    raise ZentaoAPIError("禅道API响应为空")
                
                logger.info(f"成功根据域账号查询任务，响应码: {response.get('rsCode')}")
                return response
                
        except Exception as e:
            logger.error(f"根据域账号查询任务失败: {e}")
            raise ZentaoAPIError(f"根据域账号查询任务失败: {str(e)}")
    
    @mcp.tool()
    async def zentao_get_tasks_by_dept(dept_id: int, start_date: str, end_date: str, is_doing: bool) -> Dict[str, Any]:
        """
        根据部门查询任务
        
        直接调用禅道API: /api/getTaskByDept
        保持原始数据结构
        
        Args:
            dept_id: 部门ID
            start_date: 开始日期 (YYYY-MM-DD HH:MM:SS格式)
            end_date: 结束日期 (YYYY-MM-DD HH:MM:SS格式)
            is_doing: 是否只查询进行中的任务
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            logger.info(f"调用禅道API根据部门ID {dept_id} 查询任务，时间: {start_date} 到 {end_date}, 进行中: {is_doing}")
            
            # 从全局服务器实例获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                response = await client.get_tasks_by_dept_async(dept_id, start_date, end_date, is_doing)
                
                if not response:
                    raise ZentaoAPIError("禅道API响应为空")
                
                logger.info(f"成功根据部门查询任务，响应码: {response.get('rsCode')}")
                return response
                
        except Exception as e:
            logger.error(f"根据部门查询任务失败: {e}")
            raise ZentaoAPIError(f"根据部门查询任务失败: {str(e)}")
    
    logger.info("任务管理直接接口工具注册完成")