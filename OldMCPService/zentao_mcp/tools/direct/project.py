"""
项目管理直接接口工具

提供与禅道项目API 1:1对应的直接查询接口
"""

import logging
from typing import Dict, Any
from fastmcp import Context

from ...exceptions import ZentaoAPIError

logger = logging.getLogger(__name__)


def register_project_direct_tools(mcp):
    """注册项目管理直接接口工具"""
    
    @mcp.tool()
    async def zentao_get_all_projects() -> Dict[str, Any]:
        """
        获取所有项目列表
        
        直接调用禅道API: /apiData/getAllProject
        保持原始数据结构
        
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            logger.info("调用禅道API获取所有项目列表")
            
            # 获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                response = await client.get_all_projects_async(parse_model=False)
                
                if not response:
                    raise ZentaoAPIError("禅道API响应为空")
                
                logger.info(f"成功获取项目列表，响应码: {response.get('rsCode')}")
                return response
                
        except Exception as e:
            logger.error(f"获取所有项目列表失败: {e}")
            raise ZentaoAPIError(f"获取所有项目列表失败: {str(e)}")
    
    @mcp.tool()
    async def zentao_get_project_tasks(project_id: int) -> Dict[str, Any]:
        """
        根据项目ID查询任务列表
        
        直接调用禅道API: /api/getTaskByProject
        保持原始数据结构
        
        Args:
            project_id: 项目ID
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            logger.info(f"调用禅道API根据项目ID {project_id} 查询任务列表")
            
            # 获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                response = await client.get_tasks_by_project_async(project_id)
                
                if not response:
                    raise ZentaoAPIError("禅道API响应为空")
                
                logger.info(f"成功获取项目 {project_id} 任务列表，响应码: {response.get('rsCode')}")
                return response
                
        except Exception as e:
            logger.error(f"根据项目ID查询任务列表失败: {e}")
            raise ZentaoAPIError(f"根据项目ID查询任务列表失败: {str(e)}")
    
    @mcp.tool()
    async def zentao_get_project_stories(project_id: int) -> Dict[str, Any]:
        """
        根据项目ID查询需求列表
        
        直接调用禅道API: /api/getStoryByProjectid
        保持原始数据结构
        
        Args:
            project_id: 项目ID
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            logger.info(f"调用禅道API根据项目ID {project_id} 查询需求列表")
            
            # 获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                response = await client.get_stories_by_project_async(project_id)
                
                if not response:
                    raise ZentaoAPIError("禅道API响应为空")
                
                logger.info(f"成功获取项目 {project_id} 需求列表，响应码: {response.get('rsCode')}")
                return response
                
        except Exception as e:
            logger.error(f"根据项目ID查询需求列表失败: {e}")
            raise ZentaoAPIError(f"根据项目ID查询需求列表失败: {str(e)}")
    
    @mcp.tool()
    async def zentao_get_project_bugs(project_id: int) -> Dict[str, Any]:
        """
        根据项目ID查询Bug列表
        
        直接调用禅道API: /api/getBugByProject
        保持原始数据结构
        
        Args:
            project_id: 项目ID
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            logger.info(f"调用禅道API根据项目ID {project_id} 查询Bug列表")
            
            # 获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                response = await client.get_bugs_by_project_async(project_id)
                
                if not response:
                    raise ZentaoAPIError("禅道API响应为空")
                
                logger.info(f"成功获取项目 {project_id} Bug列表，响应码: {response.get('rsCode')}")
                return response
                
        except Exception as e:
            logger.error(f"根据项目ID查询Bug列表失败: {e}")
            raise ZentaoAPIError(f"根据项目ID查询Bug列表失败: {str(e)}")
    
    logger.info("项目管理直接接口工具注册完成")