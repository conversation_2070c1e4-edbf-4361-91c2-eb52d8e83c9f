"""
用户管理直接接口工具

提供与禅道用户API 1:1对应的直接查询接口
"""

import logging
from typing import Dict, Any, List
from fastmcp import Context

from ...exceptions import ZentaoAPIError, ValidationError

logger = logging.getLogger(__name__)


def register_user_direct_tools(mcp):
    """注册用户管理直接接口工具"""
    
    @mcp.tool()
    async def zentao_get_users_by_account(accounts: List[str]) -> Dict[str, Any]:
        """
        根据域账号批量查询用户信息
        
        直接调用禅道API: /api/getUserByAccount
        保持原始数据结构
        
        Args:
            accounts: 域账号列表
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            logger.info(f"调用禅道API根据域账号批量查询用户信息，账号: {accounts}")
            
            if not accounts:
                raise ValidationError("域账号列表不能为空")
            
            # 从全局服务器实例获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                response = await client.get_users_by_accounts_async(accounts)
                
                if not response:
                    raise ZentaoAPIError("禅道API响应为空")
                
                logger.info(f"成功根据域账号批量查询用户信息，响应码: {response.get('rsCode')}")
                return response
                
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"根据域账号批量查询用户信息失败: {e}")
            raise ZentaoAPIError(f"根据域账号批量查询用户信息失败: {str(e)}")
    
    logger.info("用户管理直接接口工具注册完成")