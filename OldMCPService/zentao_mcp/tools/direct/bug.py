"""
Bug管理直接接口工具

提供与禅道Bug API 1:1对应的直接查询接口
"""

import logging
from typing import Dict, Any
from fastmcp import Context

from ...exceptions import ZentaoAPIError

logger = logging.getLogger(__name__)


def register_bug_direct_tools(mcp):
    """注册Bug管理直接接口工具"""
    
    @mcp.tool()
    async def zentao_get_bugs_by_time_range(start_date: str, end_date: str) -> Dict[str, Any]:
        """
        根据时间段查询Bug列表
        
        直接调用禅道API: /apiData/getBugListByTimeRange
        保持原始数据结构
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD HH:MM:SS格式)
            end_date: 结束日期 (YYYY-MM-DD HH:MM:SS格式)
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            logger.info(f"调用禅道API根据时间段查询Bug列表，时间: {start_date} 到 {end_date}")
            
            # 从全局服务器实例获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                response = await client.get_bugs_by_time_range_async(start_date, end_date, parse_model=False)
                
                if not response:
                    raise ZentaoAPIError("禅道API响应为空")
                
                logger.info(f"成功根据时间段查询Bug列表，响应码: {response.get('rsCode')}")
                return response
                
        except Exception as e:
            logger.error(f"根据时间段查询Bug列表失败: {e}")
            raise ZentaoAPIError(f"根据时间段查询Bug列表失败: {str(e)}")
    
    @mcp.tool()
    async def zentao_get_bugs_by_time_and_dept(start_date: str, end_date: str, dept_id: int) -> Dict[str, Any]:
        """
        根据时间段和部门查询Bug
        
        直接调用禅道API: /apiData/getBugListByTimeRangeAndDeptId
        保持原始数据结构
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD HH:MM:SS格式)
            end_date: 结束日期 (YYYY-MM-DD HH:MM:SS格式)
            dept_id: 部门ID
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            logger.info(f"调用禅道API根据时间段和部门查询Bug，时间: {start_date} 到 {end_date}, 部门ID: {dept_id}")
            
            # 从全局服务器实例获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                response = await client.get_bugs_by_time_range_and_dept_async(start_date, end_date, dept_id)
                
                if not response:
                    raise ZentaoAPIError("禅道API响应为空")
                
                logger.info(f"成功根据时间段和部门查询Bug，响应码: {response.get('rsCode')}")
                return response
                
        except Exception as e:
            logger.error(f"根据时间段和部门查询Bug失败: {e}")
            raise ZentaoAPIError(f"根据时间段和部门查询Bug失败: {str(e)}")
    
    @mcp.tool()
    async def zentao_get_bug_detail(bug_id: int) -> Dict[str, Any]:
        """
        根据Bug ID获取详情
        
        直接调用禅道API: /api/getBugDetail
        保持原始数据结构
        
        Args:
            bug_id: Bug ID
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            logger.info(f"调用禅道API根据Bug ID {bug_id} 获取详情")
            
            # 从全局服务器实例获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                response = await client.get_bug_detail_async(bug_id)
                
                if not response:
                    raise ZentaoAPIError("禅道API响应为空")
                
                logger.info(f"成功获取Bug {bug_id} 详情，响应码: {response.get('rsCode')}")
                return response
                
        except Exception as e:
            logger.error(f"根据Bug ID获取详情失败: {e}")
            raise ZentaoAPIError(f"根据Bug ID获取详情失败: {str(e)}")
    
    @mcp.tool()
    async def zentao_get_personal_bugs(account: str, status: str, start_date: str, end_date: str) -> Dict[str, Any]:
        """
        查询个人Bug
        
        直接调用禅道API: /api/getPersonalBugs
        保持原始数据结构
        
        Args:
            account: 域账号
            status: Bug状态 (active/resolved/released/closed)
            start_date: 开始日期 (YYYY-MM-DD HH:MM:SS格式)
            end_date: 结束日期 (YYYY-MM-DD HH:MM:SS格式)
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            logger.info(f"调用禅道API查询个人Bug，账号: {account}, 状态: {status}, 时间: {start_date} 到 {end_date}")
            
            # 从全局服务器实例获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                response = await client.get_personal_bugs_async(account, status, start_date, end_date)
                
                if not response:
                    raise ZentaoAPIError("禅道API响应为空")
                
                logger.info(f"成功查询个人Bug，响应码: {response.get('rsCode')}")
                return response
                
        except Exception as e:
            logger.error(f"查询个人Bug失败: {e}")
            raise ZentaoAPIError(f"查询个人Bug失败: {str(e)}")
    
    logger.info("Bug管理直接接口工具注册完成")