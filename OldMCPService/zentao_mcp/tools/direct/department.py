"""
部门管理直接接口工具

提供与禅道部门API 1:1对应的直接查询接口
"""

import logging
from typing import Dict, Any
from fastmcp import Context

from ...exceptions import ZentaoAPIError

logger = logging.getLogger(__name__)


def register_department_direct_tools(mcp):
    """注册部门管理直接接口工具"""
    
    @mcp.tool()
    async def zentao_get_all_departments() -> Dict[str, Any]:
        """
        获取所有部门列表
        
        直接调用禅道API: /apiData/getAllDept
        保持原始数据结构
        
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            logger.info("调用禅道API获取所有部门列表")
            
            # 获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                response = await client.get_all_departments_async(parse_model=False)
                
                if not response:
                    raise ZentaoAPIError("禅道API响应为空")
                
                logger.info(f"成功获取部门列表，响应码: {response.get('rsCode')}")
                return response
                
        except Exception as e:
            logger.error(f"获取所有部门列表失败: {e}")
            raise ZentaoAPIError(f"获取所有部门列表失败: {str(e)}")
    
    @mcp.tool()
    async def zentao_get_users_by_dept(dept_id: int) -> Dict[str, Any]:
        """
        根据部门查询用户列表
        
        直接调用禅道API: /api/getUserByDept
        保持原始数据结构
        
        Args:
            dept_id: 部门ID
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            logger.info(f"调用禅道API根据部门ID {dept_id} 查询用户列表")
            
            # 获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                response = await client.get_users_by_department_async(dept_id)
                
                if not response:
                    raise ZentaoAPIError("禅道API响应为空")
                
                logger.info(f"成功获取部门 {dept_id} 用户列表，响应码: {response.get('rsCode')}")
                return response
                
        except Exception as e:
            logger.error(f"根据部门查询用户列表失败: {e}")
            raise ZentaoAPIError(f"根据部门查询用户列表失败: {str(e)}")
    
    logger.info("部门管理直接接口工具注册完成")