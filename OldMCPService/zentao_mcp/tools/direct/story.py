"""
需求管理直接接口工具

提供与禅道需求API 1:1对应的直接查询接口
"""

import logging
from typing import Dict, Any, List
from fastmcp import Context

from ...exceptions import ZentaoAPIError, ValidationError

logger = logging.getLogger(__name__)


def register_story_direct_tools(mcp):
    """注册需求管理直接接口工具"""
    
    @mcp.tool()
    async def zentao_get_story_effort(story_ids: List[str]) -> Dict[str, Any]:
        """
        批量获取需求工时信息
        
        直接调用禅道API: /api/getStory
        保持原始数据结构
        
        Args:
            story_ids: 需求ID列表（字符串格式）
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            logger.info(f"调用禅道API批量获取需求工时信息，需求ID: {story_ids}")
            
            if not story_ids:
                raise ValidationError("需求ID列表不能为空")
            
            # 从全局服务器实例获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                response = await client.get_story_info_async(story_ids)
                
                if not response:
                    raise ZentaoAPIError("禅道API响应为空")
                
                logger.info(f"成功获取需求工时信息，响应码: {response.get('rsCode')}")
                return response
                
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"批量获取需求工时信息失败: {e}")
            raise ZentaoAPIError(f"批量获取需求工时信息失败: {str(e)}")
    
    @mcp.tool()
    async def zentao_get_story_completed_effort(story_ids: List[str]) -> Dict[str, Any]:
        """
        批量获取已完成需求任务工时
        
        直接调用禅道API: /api/getStoryEnd
        保持原始数据结构
        
        Args:
            story_ids: 需求ID列表（字符串格式）
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            logger.info(f"调用禅道API批量获取已完成需求任务工时，需求ID: {story_ids}")
            
            if not story_ids:
                raise ValidationError("需求ID列表不能为空")
            
            # 从全局服务器实例获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                response = await client.get_story_end_async(story_ids)
                
                if not response:
                    raise ZentaoAPIError("禅道API响应为空")
                
                logger.info(f"成功获取已完成需求任务工时，响应码: {response.get('rsCode')}")
                return response
                
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"批量获取已完成需求任务工时失败: {e}")
            raise ZentaoAPIError(f"批量获取已完成需求任务工时失败: {str(e)}")
    
    @mcp.tool()
    async def zentao_check_story_exists(story_ids: List[str]) -> Dict[str, Any]:
        """
        批量检查需求是否存在
        
        直接调用禅道API: /api/getStoryId
        保持原始数据结构
        
        Args:
            story_ids: 需求ID列表（字符串格式）
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            logger.info(f"调用禅道API批量检查需求是否存在，需求ID: {story_ids}")
            
            if not story_ids:
                raise ValidationError("需求ID列表不能为空")
            
            # 从全局服务器实例获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                response = await client.check_story_exists_async(story_ids)
                
                if not response:
                    raise ZentaoAPIError("禅道API响应为空")
                
                logger.info(f"成功检查需求存在性，响应码: {response.get('rsCode')}")
                return response
                
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"批量检查需求是否存在失败: {e}")
            raise ZentaoAPIError(f"批量检查需求是否存在失败: {str(e)}")
    
    @mcp.tool()
    async def zentao_get_story_detail(story_id: int) -> Dict[str, Any]:
        """
        根据需求ID获取详情
        
        直接调用禅道API: /api/getStoryDetail
        保持原始数据结构
        
        Args:
            story_id: 需求ID
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            logger.info(f"调用禅道API根据需求ID {story_id} 获取详情")
            
            # 从全局服务器实例获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                response = await client.get_story_by_id_async(story_id)
                
                if not response:
                    raise ZentaoAPIError("禅道API响应为空")
                
                logger.info(f"成功获取需求 {story_id} 详情，响应码: {response.get('rsCode')}")
                return response
                
        except Exception as e:
            logger.error(f"根据需求ID获取详情失败: {e}")
            raise ZentaoAPIError(f"根据需求ID获取详情失败: {str(e)}")
    
    @mcp.tool()
    async def zentao_get_stories_by_time(status: str, start_date: str, end_date: str) -> Dict[str, Any]:
        """
        根据时间段查询需求
        
        直接调用禅道API: /api/getStorysByTime
        保持原始数据结构
        
        Args:
            status: 需求状态
            start_date: 开始日期 (YYYY-MM-DD HH:MM:SS格式)
            end_date: 结束日期 (YYYY-MM-DD HH:MM:SS格式)
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            logger.info(f"调用禅道API根据时间段查询需求，状态: {status}, 时间: {start_date} 到 {end_date}")
            
            # 从全局服务器实例获取禅道客户端
            from ..client_utils import get_zentao_client
            async with get_zentao_client() as client:
                response = await client.get_stories_by_time_async(status, start_date, end_date)
                
                if not response:
                    raise ZentaoAPIError("禅道API响应为空")
                
                logger.info(f"成功根据时间段查询需求，响应码: {response.get('rsCode')}")
                return response
                
        except Exception as e:
            logger.error(f"根据时间段查询需求失败: {e}")
            raise ZentaoAPIError(f"根据时间段查询需求失败: {str(e)}")
    
    logger.info("需求管理直接接口工具注册完成")