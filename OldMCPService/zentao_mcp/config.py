import os
import logging
from typing import Optional, Dict, Any, List, Literal
from pathlib import Path
from pydantic import Field, field_validator, model_validator
from pydantic_settings import BaseSettings

from .exceptions import ConfigurationError


logger = logging.getLogger(__name__)


class Settings(BaseSettings):
    """禅道MCP服务配置类"""
    
    # ==================== 禅道API配置 ====================
    
    # ==================== MCP服务配置 ====================
    mcp_token: str = Field("your-secret-token", description="MCP服务认证Token，用于客户端连接验证")
    mcp_transport: Literal["stdio", "http"] = Field("stdio", description="MCP传输协议")
    mcp_host: str = Field("localhost", description="MCP HTTP服务主机")
    mcp_port: int = Field(8000, description="MCP HTTP服务端口")
    
    # ==================== 环境配置 ====================
    environment: Literal["beta", "online"] = Field("beta", description="环境类型")
    
    # API域名配置
    beta_domain: str = Field("", description="Beta环境域名")
    online_domain: str = Field("", description="Online环境域名")
    
    # ==================== 缓存配置 ====================
    enable_cache: bool = Field(True, description="是否启用缓存")
    cache_ttl: int = Field(300, description="缓存过期时间(秒)")
    cache_max_size: int = Field(1000, description="缓存最大条目数")
    cache_strategy: Literal["LRU", "LFU", "FIFO"] = Field("LRU", description="缓存淘汰策略")
    
    # 静态数据缓存TTL配置
    cache_ttl_departments: int = Field(3600, description="部门数据缓存TTL(秒)")
    cache_ttl_projects: int = Field(1800, description="项目数据缓存TTL(秒)")
    cache_ttl_users: int = Field(900, description="用户数据缓存TTL(秒)")
    
    # ==================== 性能配置 ====================
    request_timeout: int = Field(30, description="请求超时时间(秒)")
    max_concurrent_requests: int = Field(100, description="最大并发请求数")
    connection_pool_size: int = Field(20, description="HTTP连接池大小")
    
    # 重试配置
    enable_retry: bool = Field(True, description="是否启用重试机制")
    max_retries: int = Field(3, description="最大重试次数")
    retry_backoff_factor: float = Field(1.0, description="重试退避因子")
    retry_max_delay: int = Field(60, description="重试最大延迟(秒)")
    
    # 批量处理配置
    batch_size: int = Field(50, description="批量处理大小")
    enable_batch_optimization: bool = Field(True, description="是否启用批量优化")
    
    # ==================== 安全配置 ====================
    enable_auth: bool = Field(False, description="是否启用认证")
    token_min_length: int = Field(16, description="Token最小长度")
    
    # 请求限制配置
    enable_rate_limit: bool = Field(False, description="是否启用请求频率限制")
    rate_limit_requests: int = Field(100, description="频率限制请求数")
    rate_limit_window: int = Field(60, description="频率限制时间窗口(秒)")
    
    # 数据脱敏配置
    enable_data_masking: bool = Field(False, description="是否启用数据脱敏")
    mask_sensitive_fields: List[str] = Field(
        default=["password", "token", "email"], 
        description="需要脱敏的字段列表"
    )
    
    # ==================== 日志配置 ====================
    log_level: Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"] = Field("INFO", description="日志级别")
    log_dir: str = Field("logs", description="日志目录")
    log_format: Literal["text", "json"] = Field("text", description="日志格式")
    log_max_size: str = Field("10MB", description="单个日志文件最大大小")
    log_backup_count: int = Field(5, description="日志文件备份数量")
    
    # 结构化日志配置
    enable_structured_logging: bool = Field(False, description="是否启用结构化日志")
    log_request_id: bool = Field(True, description="是否记录请求ID")
    log_performance_metrics: bool = Field(True, description="是否记录性能指标")
    
    # ==================== 监控配置 ====================
    enable_metrics: bool = Field(False, description="是否启用指标收集")
    metrics_port: int = Field(9090, description="指标服务端口")
    metrics_path: str = Field("/metrics", description="指标路径")
    
    # 健康检查配置
    enable_health_check: bool = Field(True, description="是否启用健康检查")
    health_check_interval: int = Field(30, description="健康检查间隔(秒)")
    health_check_timeout: int = Field(5, description="健康检查超时(秒)")
    
    # ==================== 功能开关 ====================
    enable_legacy_tools: bool = Field(False, description="是否启用遗留工具")
    enable_experimental_features: bool = Field(False, description="是否启用实验性功能")
    enable_debug_mode: bool = Field(False, description="是否启用调试模式")
    
    # 工具功能开关
    enable_direct_tools: bool = Field(True, description="是否启用直接接口工具")
    enable_analysis_tools: bool = Field(True, description="是否启用分析增强工具")
    enable_business_tools: bool = Field(False, description="是否启用业务封装工具")

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "extra": "ignore",  # 忽略额外的字段
        "case_sensitive": False,  # 不区分大小写
    }

    @field_validator('log_level')
    @classmethod
    def validate_log_level(cls, v):
        """验证日志级别"""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"日志级别必须是 {valid_levels} 中的一个")
        return v.upper()

    @field_validator('cache_ttl', 'cache_ttl_departments', 'cache_ttl_projects', 'cache_ttl_users')
    @classmethod
    def validate_cache_ttl(cls, v):
        """验证缓存TTL配置"""
        if v < 0:
            raise ValueError("缓存TTL不能为负数")
        if v > 86400:  # 24小时
            logger.warning(f"缓存TTL设置过长: {v}秒，建议不超过24小时")
        return v

    @field_validator('request_timeout')
    @classmethod
    def validate_request_timeout(cls, v):
        """验证请求超时配置"""
        if v <= 0:
            raise ValueError("请求超时时间必须大于0")
        if v > 300:  # 5分钟
            logger.warning(f"请求超时时间设置过长: {v}秒")
        return v

    @field_validator('max_concurrent_requests')
    @classmethod
    def validate_max_concurrent_requests(cls, v):
        """验证最大并发请求数"""
        if v <= 0:
            raise ValueError("最大并发请求数必须大于0")
        if v > 1000:
            logger.warning(f"最大并发请求数设置过高: {v}")
        return v

    @field_validator('mcp_port', 'metrics_port')
    @classmethod
    def validate_port(cls, v):
        """验证端口号"""
        if not (1 <= v <= 65535):
            raise ValueError("端口号必须在1-65535范围内")
        return v

    @model_validator(mode='after')
    def validate_token_and_dependencies(self):
        """验证Token和功能依赖关系"""
        # 验证MCP Token
        if self.enable_auth:
            if not self.mcp_token or self.mcp_token == "your-secret-token":
                raise ValueError("启用认证时必须设置有效的MCP_TOKEN")
            
            if len(self.mcp_token) < self.token_min_length:
                raise ValueError(f"MCP Token长度不能少于{self.token_min_length}位")
        
        # 域名配置验证已移至 get_zentao_base_url 和 validate_configuration
        
        # 检查分析工具依赖直接工具
        if self.enable_analysis_tools and not self.enable_direct_tools:
            raise ValueError("分析增强工具依赖直接接口工具，请同时启用enable_direct_tools")
        
        # 检查业务工具依赖分析工具
        if self.enable_business_tools and not self.enable_analysis_tools:
            raise ValueError("业务封装工具依赖分析增强工具，请同时启用enable_analysis_tools")
        
        # 检查指标收集端口冲突
        if (self.enable_metrics and 
            self.mcp_transport == 'http' and 
            self.mcp_port == self.metrics_port):
            raise ValueError("MCP服务端口和指标服务端口不能相同")
        
        return self

    def get_zentao_base_url(self) -> str:
        """根据环境配置获取禅道基础URL"""
        domain_map = {
            "beta": self.beta_domain,
            "online": self.online_domain,
        }
        domain = domain_map.get(self.environment)

        # 默认域名配置
        default_domains = {
            "beta": "newzentao-api.beta1.fn",
            "online": "newzentao-api.idc1.fn",
        }

        if not domain:
            env_key = f"{self.environment.upper()}_DOMAIN"
            
            # 使用默认域名并打印日志提醒
            default_domain = default_domains.get(self.environment, "localhost:8080")
            logger.warning(
                f"当前环境 '{self.environment}' 未配置对应的API域名，"
                f"请设置环境变量 '{env_key}'，当前使用默认域名: {default_domain}"
            )
            domain = default_domain

        protocol = "https" if self.environment == "online" else "http"
        return f"{protocol}://{domain}"

    def get_cache_ttl_for_resource(self, resource_type: str) -> int:
        """根据资源类型获取缓存TTL"""
        ttl_mapping = {
            'departments': self.cache_ttl_departments,
            'projects': self.cache_ttl_projects,
            'users': self.cache_ttl_users,
        }
        return ttl_mapping.get(resource_type, self.cache_ttl)

    def get_log_config(self) -> Dict[str, Any]:
        """获取日志配置字典"""
        return {
            'level': self.log_level,
            'format': self.log_format,
            'directory': self.log_dir,
            'max_size': self.log_max_size,
            'backup_count': self.log_backup_count,
            'structured': self.enable_structured_logging,
            'request_id': self.log_request_id,
            'performance_metrics': self.log_performance_metrics,
        }

    def get_performance_config(self) -> Dict[str, Any]:
        """获取性能配置字典"""
        return {
            'request_timeout': self.request_timeout,
            'max_concurrent_requests': self.max_concurrent_requests,
            'connection_pool_size': self.connection_pool_size,
            'enable_retry': self.enable_retry,
            'max_retries': self.max_retries,
            'retry_backoff_factor': self.retry_backoff_factor,
            'retry_max_delay': self.retry_max_delay,
            'batch_size': self.batch_size,
            'enable_batch_optimization': self.enable_batch_optimization,
        }

    def get_security_config(self) -> Dict[str, Any]:
        """获取安全配置字典"""
        return {
            'enable_auth': self.enable_auth,
            'token_min_length': self.token_min_length,
            'enable_rate_limit': self.enable_rate_limit,
            'rate_limit_requests': self.rate_limit_requests,
            'rate_limit_window': self.rate_limit_window,
            'enable_data_masking': self.enable_data_masking,
            'mask_sensitive_fields': self.mask_sensitive_fields,
        }

    def validate_configuration(self) -> None:
        """验证完整配置的有效性"""
        try:
            # 验证日志目录
            log_path = Path(self.log_dir)
            if not log_path.exists():
                try:
                    log_path.mkdir(parents=True, exist_ok=True)
                    logger.info(f"创建日志目录: {log_path}")
                except Exception as e:
                    raise ConfigurationError(f"无法创建日志目录 {log_path}: {e}")

            # 验证禅道基础URL是否可以确定
            self.get_zentao_base_url()

        except ConfigurationError:
            raise  # 重新引发特定的配置错误
        except Exception as e:
            if isinstance(e, ConfigurationError):
                raise
            raise ConfigurationError(f"配置验证过程中发生错误: {e}")

    def is_development_mode(self) -> bool:
        """判断是否为开发模式"""
        return (self.enable_debug_mode or 
                self.log_level == "DEBUG")

    def get_enabled_features(self) -> List[str]:
        """获取启用的功能列表"""
        features = []
        
        if self.enable_cache:
            features.append("cache")
        if self.enable_auth:
            features.append("auth")
        if self.enable_retry:
            features.append("retry")
        if self.enable_rate_limit:
            features.append("rate_limit")
        if self.enable_metrics:
            features.append("metrics")
        if self.enable_health_check:
            features.append("health_check")
        if self.enable_direct_tools:
            features.append("direct_tools")
        if self.enable_analysis_tools:
            features.append("analysis_tools")
        if self.enable_business_tools:
            features.append("business_tools")
        if self.enable_legacy_tools:
            features.append("legacy_tools")
        if self.enable_experimental_features:
            features.append("experimental")
        
        return features


class ConfigManager:
    """配置管理器，负责配置的加载、验证和管理"""
    
    def __init__(self):
        self._settings: Optional[Settings] = None
        self._config_file_paths: List[str] = []
        self._environment_overrides: Dict[str, Any] = {}
    
    def load_settings(self, 
                     config_file: Optional[str] = None,
                     environment_overrides: Optional[Dict[str, Any]] = None) -> Settings:
        """加载配置设置
        
        Args:
            config_file: 配置文件路径
            environment_overrides: 环境变量覆盖
            
        Returns:
            Settings: 配置实例
        """
        try:
            # 设置环境变量覆盖
            if environment_overrides:
                self._environment_overrides = environment_overrides
                for key, value in environment_overrides.items():
                    os.environ[key.upper()] = str(value)
            
            # 确定配置文件路径
            env_files = []
            if config_file:
                env_files.append(config_file)
            else:
                # 按优先级顺序查找配置文件
                possible_files = [
                    f".env.{os.getenv('ENVIRONMENT', 'beta')}",  # 环境特定配置
                    ".env.local",  # 本地配置
                    ".env",        # 默认配置
                ]
                
                for file_path in possible_files:
                    if Path(file_path).exists():
                        env_files.append(file_path)
                        logger.info(f"找到配置文件: {file_path}")
            
            self._config_file_paths = env_files
            
            # 创建设置实例
            if env_files:
                # 使用找到的配置文件
                self._settings = Settings(_env_file=env_files)
            else:
                # 仅使用环境变量
                logger.warning("未找到配置文件，仅使用环境变量")
                self._settings = Settings()
            
            # 验证配置
            self._settings.validate_configuration()
            
            logger.info(f"配置加载成功，环境: {self._settings.environment}")
            logger.info(f"启用的功能: {', '.join(self._settings.get_enabled_features())}")
            
            return self._settings
            
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            raise ConfigurationError(f"配置加载失败: {e}")
    
    def get_settings(self) -> Settings:
        """获取当前配置实例"""
        if self._settings is None:
            self._settings = self.load_settings()
        return self._settings
    
    def reload_settings(self) -> Settings:
        """重新加载配置"""
        logger.info("重新加载配置...")
        self._settings = None
        return self.load_settings()
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要信息"""
        if self._settings is None:
            return {"status": "not_loaded"}
        
        return {
            "status": "loaded",
            "environment": self._settings.environment,
            "config_files": self._config_file_paths,
            "zentao_base_url": self._settings.get_zentao_base_url(),
            "enabled_features": self._settings.get_enabled_features(),
            "cache_enabled": self._settings.enable_cache,
            "auth_enabled": self._settings.enable_auth,
            "debug_mode": self._settings.is_development_mode(),
        }
    
    def validate_runtime_config(self) -> List[str]:
        """验证运行时配置，返回警告列表"""
        warnings = []
        
        if self._settings is None:
            warnings.append("配置未加载")
            return warnings
        
        # 检查生产环境配置
        if self._settings.environment == "online":
            if self._settings.log_level == "DEBUG":
                warnings.append("生产环境不建议使用DEBUG日志级别")
            
            if not self._settings.enable_auth:
                warnings.append("生产环境建议启用认证")
            
            if self._settings.enable_debug_mode:
                warnings.append("生产环境不应启用调试模式")
        
        # 检查性能配置
        if self._settings.max_concurrent_requests > 500:
            warnings.append("并发请求数设置过高，可能影响系统稳定性")
        
        if self._settings.request_timeout > 120:
            warnings.append("请求超时时间过长，可能影响用户体验")
        
        # 检查缓存配置
        if self._settings.enable_cache and self._settings.cache_ttl < 60:
            warnings.append("缓存TTL过短，可能影响缓存效果")
        
        return warnings


# 全局配置管理器实例
config_manager = ConfigManager()

# 全局配置实例（向后兼容）
settings = config_manager.get_settings()