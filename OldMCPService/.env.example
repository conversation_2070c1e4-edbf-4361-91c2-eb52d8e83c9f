# ==================== 禅道API配置 ====================

# ==================== MCP服务配置 ====================
MCP_TOKEN=your-secret-token
# 传输协议: stdio, http
MCP_TRANSPORT=stdio
MCP_HOST=localhost
MCP_PORT=8000

# ==================== 环境配置 ====================
# 可选值: beta, online
ENVIRONMENT=beta

# API域名配置
BETA_DOMAIN=newzentao-api.beta1.fn
PREVIEW_DOMAIN=stage-newzentao-api.idc1.fn
ONLINE_DOMAIN=newzentao-api.idc1.fn

# ==================== 缓存配置 ====================
ENABLE_CACHE=true
CACHE_TTL=300
CACHE_MAX_SIZE=1000
# 缓存策略: LRU, LFU, FIFO
CACHE_STRATEGY=LRU

# 静态数据缓存TTL配置(秒)
CACHE_TTL_DEPARTMENTS=3600
CACHE_TTL_PROJECTS=1800
CACHE_TTL_USERS=900

# ==================== 性能配置 ====================
REQUEST_TIMEOUT=30
MAX_CONCURRENT_REQUESTS=100
CONNECTION_POOL_SIZE=20

# 重试配置
ENABLE_RETRY=true
MAX_RETRIES=3
RETRY_BACKOFF_FACTOR=1.0
RETRY_MAX_DELAY=60

# 批量处理配置
BATCH_SIZE=50
ENABLE_BATCH_OPTIMIZATION=true

# ==================== 安全配置 ====================
ENABLE_AUTH=false
TOKEN_MIN_LENGTH=16

# 请求限制配置
ENABLE_RATE_LIMIT=false
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# 数据脱敏配置
ENABLE_DATA_MASKING=false
MASK_SENSITIVE_FIELDS=password,token,email

# ==================== 日志配置 ====================
# 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO
LOG_DIR=logs
# 日志格式: text, json
LOG_FORMAT=text
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# 结构化日志配置
ENABLE_STRUCTURED_LOGGING=false
LOG_REQUEST_ID=true
LOG_PERFORMANCE_METRICS=true

# ==================== 监控配置 ====================
ENABLE_METRICS=false
METRICS_PORT=9090
METRICS_PATH=/metrics

# 健康检查配置
ENABLE_HEALTH_CHECK=true
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=5

# ==================== 功能开关 ====================
ENABLE_LEGACY_TOOLS=false
ENABLE_EXPERIMENTAL_FEATURES=false
ENABLE_DEBUG_MODE=false

# 工具功能开关
ENABLE_DIRECT_TOOLS=true
ENABLE_ANALYSIS_TOOLS=true
ENABLE_BUSINESS_TOOLS=false