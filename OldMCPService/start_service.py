#!/usr/bin/env python3
"""
Zentao MCP Backend Service 启动脚本
"""
import os
import sys
import subprocess
import time
from pathlib import Path

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version < (3, 10):
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        print("   需要Python 3.10或更高版本")
        return False
    
    print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查虚拟环境
    venv_path = Path(".venv")
    if not venv_path.exists():
        print("❌ 未找到虚拟环境 (.venv)")
        return False
    
    print("✅ 虚拟环境存在")
    
    # 检查主要文件
    backend_main = Path("zentao-mcp-backend-service/main.py")
    if not backend_main.exists():
        print("❌ 未找到后端服务主文件")
        return False
    
    print("✅ 后端服务文件存在")
    return True

def install_dependencies():
    """安装依赖"""
    print("📦 检查并安装依赖...")
    
    try:
        # 使用uv安装依赖
        result = subprocess.run([
            "uv", "pip", "install", "-e", "zentao-mcp-backend-service/"
        ], capture_output=True, text=True, cwd=".")
        
        if result.returncode != 0:
            print(f"❌ 依赖安装失败: {result.stderr}")
            return False
        
        print("✅ 依赖安装完成")
        return True
        
    except FileNotFoundError:
        print("❌ 未找到uv命令，请先安装uv")
        return False
    except Exception as e:
        print(f"❌ 依赖安装异常: {e}")
        return False

def start_backend_service():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    
    # 切换到后端服务目录
    backend_dir = Path("zentao-mcp-backend-service")
    if not backend_dir.exists():
        print("❌ 后端服务目录不存在")
        return False
    
    # 构建启动命令
    python_path = Path(".venv/bin/python3").absolute()
    if not python_path.exists():
        python_path = Path(".venv/bin/python").absolute()
    
    if not python_path.exists():
        print("❌ 虚拟环境Python解释器不存在")
        return False
    
    cmd = [
        str(python_path),
        "-m", "uvicorn",
        "main:app",
        "--host", "0.0.0.0",
        "--port", "8000",
        "--reload"
    ]
    
    print(f"📋 执行命令: {' '.join(cmd)}")
    print(f"📁 工作目录: {backend_dir.absolute()}")
    
    try:
        # 启动服务
        process = subprocess.Popen(
            cmd,
            cwd=backend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print("⏳ 等待服务启动...")
        
        # 实时输出日志
        for line in process.stdout:
            print(f"📝 {line.strip()}")
            
            # 检查是否启动成功
            if "Uvicorn running on" in line:
                print("✅ 服务启动成功!")
                break
            elif "ERROR" in line and "Could not import" in line:
                print("❌ 服务启动失败!")
                process.terminate()
                return False
        
        # 保持进程运行
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 收到中断信号，正在关闭服务...")
            process.terminate()
            process.wait()
            print("✅ 服务已关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ 启动服务异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 Zentao MCP Backend Service 启动器")
    print("=" * 60)
    
    # 检查环境
    if not check_environment():
        print("❌ 环境检查失败，退出")
        sys.exit(1)
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败，退出")
        sys.exit(1)
    
    # 启动服务
    if not start_backend_service():
        print("❌ 服务启动失败，退出")
        sys.exit(1)

if __name__ == "__main__":
    main()