#!/usr/bin/env python3
"""
测试配置管理和认证系统的脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from zentao_mcp.config import config_manager, settings
from zentao_mcp.auth import get_token_validator, get_session_manager, create_auth_middleware
from zentao_mcp.client import ZentaoClient
from zentao_mcp.exceptions import ConfigurationError, AuthenticationError


async def test_config_management():
    """测试配置管理功能"""
    print("=" * 50)
    print("测试配置管理功能")
    print("=" * 50)
    
    try:
        # 测试配置加载
        print("1. 测试配置加载...")
        config_summary = config_manager.get_config_summary()
        print(f"配置状态: {config_summary['status']}")
        print(f"环境: {config_summary['environment']}")
        print(f"禅道基础URL: {config_summary['zentao_base_url']}")
        print(f"启用的功能: {', '.join(config_summary['enabled_features'])}")
        
        # 测试配置验证
        print("\n2. 测试配置验证...")
        try:
            settings.validate_configuration()
            print("✓ 配置验证通过")
        except ConfigurationError as e:
            print(f"✗ 配置验证失败: {e}")
        
        # 测试运行时配置检查
        print("\n3. 测试运行时配置检查...")
        warnings = config_manager.validate_runtime_config()
        if warnings:
            print("配置警告:")
            for warning in warnings:
                print(f"  - {warning}")
        else:
            print("✓ 无配置警告")
        
        # 测试配置方法
        print("\n4. 测试配置方法...")
        print(f"缓存TTL (部门): {settings.get_cache_ttl_for_resource('departments')}秒")
        print(f"缓存TTL (项目): {settings.get_cache_ttl_for_resource('projects')}秒")
        print(f"是否开发模式: {settings.is_development_mode()}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置管理测试失败: {e}")
        return False


async def test_authentication():
    """测试认证功能"""
    print("\n" + "=" * 50)
    print("测试认证功能")
    print("=" * 50)
    
    try:
        # 测试Token验证器
        print("1. 测试Token验证器...")
        validator = get_token_validator()
        
        # 测试有效Token（如果认证启用）
        if settings.enable_auth:
            test_token = settings.mcp_token
            is_valid = validator.validate_token(test_token)
            print(f"✓ Token验证结果: {is_valid}")
        else:
            print("✓ 认证已禁用，跳过Token验证")
        
        # 测试Token提取
        print("\n2. 测试Token提取...")
        test_context = {
            "authorization": "Bearer test-token-123",
            "x-api-key": "api-key-456",
            "x-forwarded-for": "*************"
        }
        
        extracted_token = validator.extract_token_from_context(test_context)
        client_ip = validator.get_client_ip(test_context)
        print(f"提取的Token: {extracted_token}")
        print(f"客户端IP: {client_ip}")
        
        return True
        
    except Exception as e:
        print(f"✗ 认证测试失败: {e}")
        return False


async def test_session_management():
    """测试会话管理功能"""
    print("\n" + "=" * 50)
    print("测试会话管理功能")
    print("=" * 50)
    
    try:
        # 测试会话管理器
        print("1. 测试会话管理器...")
        session_manager = get_session_manager()
        
        # 获取会话信息
        session_info = session_manager.get_session_info()
        print(f"会话状态: {session_info}")
        
        # 测试会话刷新
        print("\n2. 测试会话刷新...")
        refreshed = await session_manager.refresh_session_if_needed()
        print(f"会话是否刷新: {refreshed}")
        
        return True
        
    except Exception as e:
        print(f"✗ 会话管理测试失败: {e}")
        return False


async def test_client_integration():
    """测试客户端集成"""
    print("\n" + "=" * 50)
    print("测试客户端集成")
    print("=" * 50)
    
    try:
        # 创建客户端实例
        print("1. 创建客户端实例...")
        client = ZentaoClient()
        
        # 测试初始化
        print("2. 测试客户端初始化...")
        await client.initialize()
        print("✓ 客户端初始化成功")
        
        # 获取会话信息
        print("\n3. 获取会话信息...")
        session_info = client.get_session_info()
        print(f"客户端会话信息: {session_info}")
        
        # 关闭客户端
        print("\n4. 关闭客户端...")
        await client.close()
        print("✓ 客户端关闭成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 客户端集成测试失败: {e}")
        return False


async def test_auth_middleware():
    """测试认证中间件"""
    print("\n" + "=" * 50)
    print("测试认证中间件")
    print("=" * 50)
    
    try:
        # 创建认证中间件
        print("1. 创建认证中间件...")
        auth_middleware = create_auth_middleware()
        
        # 获取统计信息
        print("2. 获取中间件统计...")
        stats = auth_middleware.get_stats()
        print(f"中间件统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"✗ 认证中间件测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("开始测试配置管理和认证系统...")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(await test_config_management())
    test_results.append(await test_authentication())
    test_results.append(await test_session_management())
    test_results.append(await test_client_integration())
    test_results.append(await test_auth_middleware())
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有测试通过！")
        return 0
    else:
        print("✗ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)