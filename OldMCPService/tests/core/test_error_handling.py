#!/usr/bin/env python3
"""
测试错误处理和日志系统
"""

import asyncio
import sys
import os
import tempfile
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from zentao_mcp.error_handler import (
    ErrorHandler, RetryConfig, ErrorClassifier, 
    with_error_handling, with_timeout
)
from zentao_mcp.exceptions import (
    ZentaoAPIError, NetworkError, TimeoutError,
    AuthenticationError, ValidationError
)
from zentao_mcp.logging_config import (
    setup_logging, get_logger, request_tracker, 
    performance_metrics, get_request_id, set_request_context
)
from zentao_mcp.health_check import health_checker


async def test_error_classification():
    """测试错误分类"""
    print("=== 测试错误分类 ===")
    
    classifier = ErrorClassifier()
    
    # 测试可重试错误
    network_error = NetworkError("连接失败")
    timeout_error = TimeoutError("请求超时")
    api_error_500 = ZentaoAPIError("服务器错误", status_code=500)
    
    assert classifier.is_retryable_error(network_error), "网络错误应该可重试"
    assert classifier.is_retryable_error(timeout_error), "超时错误应该可重试"
    assert classifier.is_retryable_error(api_error_500), "500错误应该可重试"
    
    # 测试不可重试错误
    auth_error = AuthenticationError("认证失败")
    validation_error = ValidationError("参数错误")
    api_error_404 = ZentaoAPIError("资源不存在", status_code=404)
    
    assert not classifier.is_retryable_error(auth_error), "认证错误不应该重试"
    assert not classifier.is_retryable_error(validation_error), "验证错误不应该重试"
    assert not classifier.is_retryable_error(api_error_404), "404错误不应该重试"
    
    print("✓ 错误分类测试通过")


async def test_retry_mechanism():
    """测试重试机制"""
    print("=== 测试重试机制 ===")
    
    retry_config = RetryConfig(max_retries=3, base_delay=0.1)
    error_handler = ErrorHandler(retry_config)
    
    # 测试成功的函数
    call_count = 0
    
    async def success_func():
        nonlocal call_count
        call_count += 1
        return {"result": "success", "call_count": call_count}
    
    result = await error_handler.handle_with_retry(success_func)
    assert result["result"] == "success", "成功函数应该返回正确结果"
    assert call_count == 1, "成功函数应该只调用一次"
    
    # 测试重试后成功的函数
    call_count = 0
    
    async def retry_success_func():
        nonlocal call_count
        call_count += 1
        if call_count < 3:
            raise NetworkError("网络错误")
        return {"result": "success", "call_count": call_count}
    
    result = await error_handler.handle_with_retry(retry_success_func)
    assert result["result"] == "success", "重试后成功的函数应该返回正确结果"
    assert call_count == 3, "应该重试2次后成功"
    
    # 测试最终失败的函数
    call_count = 0
    
    async def always_fail_func():
        nonlocal call_count
        call_count += 1
        raise NetworkError("网络错误")
    
    try:
        await error_handler.handle_with_retry(always_fail_func)
        assert False, "应该抛出异常"
    except NetworkError:
        assert call_count == 4, "应该调用4次（初始1次+重试3次）"
    
    print("✓ 重试机制测试通过")


async def test_error_handling_decorator():
    """测试错误处理装饰器"""
    print("=== 测试错误处理装饰器 ===")
    
    retry_config = RetryConfig(max_retries=2, base_delay=0.1)
    
    @with_error_handling(retry_config)
    async def decorated_func(should_fail: bool = False):
        if should_fail:
            raise NetworkError("网络错误")
        return {"result": "success"}
    
    # 测试成功情况
    result = await decorated_func(should_fail=False)
    assert result["result"] == "success", "装饰器应该正常处理成功情况"
    
    # 测试失败情况
    try:
        await decorated_func(should_fail=True)
        assert False, "应该抛出异常"
    except NetworkError:
        pass  # 预期的异常
    
    print("✓ 错误处理装饰器测试通过")


def test_structured_logging():
    """测试结构化日志"""
    print("=== 测试结构化日志 ===")
    
    # 创建临时日志目录
    with tempfile.TemporaryDirectory() as temp_dir:
        # 设置日志系统
        setup_logging(
            log_level="DEBUG",
            log_dir=temp_dir,
            json_format=True,
            enable_file_logging=True,
            enable_console_logging=False
        )
        
        logger = get_logger("test_logger")
        
        # 设置请求上下文
        request_id = get_request_id()
        set_request_context(request_id, "test_user")
        
        # 记录不同级别的日志
        logger.debug("调试信息", extra={"test_field": "debug_value"})
        logger.info("信息日志", extra={"test_field": "info_value"})
        logger.warning("警告信息", extra={"test_field": "warning_value"})
        logger.error("错误信息", extra={"test_field": "error_value"})
        
        # 检查日志文件
        log_file = Path(temp_dir) / "zentao_mcp.log"
        assert log_file.exists(), "日志文件应该存在"
        
        # 读取并验证日志内容
        with open(log_file, 'r', encoding='utf-8') as f:
            log_lines = f.readlines()
        
        assert len(log_lines) >= 4, "应该有至少4行日志"
        
        # 验证JSON格式
        test_log_count = 0
        for line in log_lines:
            try:
                log_data = json.loads(line.strip())
                assert "timestamp" in log_data, "日志应该包含时间戳"
                assert "level" in log_data, "日志应该包含级别"
                assert "message" in log_data, "日志应该包含消息"
                
                # 只检查测试日志器的日志
                if log_data.get("logger") == "test_logger":
                    assert "request_id" in log_data, "测试日志应该包含请求ID"
                    assert log_data["request_id"] == request_id, "请求ID应该正确"
                    assert "user_id" in log_data, "测试日志应该包含用户ID"
                    assert log_data["user_id"] == "test_user", "用户ID应该正确"
                    test_log_count += 1
                    
            except json.JSONDecodeError:
                assert False, f"日志行不是有效的JSON: {line}"
        
        assert test_log_count >= 4, "应该有至少4条测试日志"
    
    print("✓ 结构化日志测试通过")


async def test_request_tracking():
    """测试请求追踪"""
    print("=== 测试请求追踪 ===")
    
    # 开始请求追踪
    request_id = get_request_id()
    request_tracker.start_request(
        request_id=request_id,
        method="GET",
        url="http://test.com/api/test",
        user_id="test_user"
    )
    
    # 检查活跃请求
    active_requests = request_tracker.get_active_requests()
    assert request_id in active_requests, "应该有活跃请求"
    assert active_requests[request_id]["method"] == "GET", "请求方法应该正确"
    
    # 模拟一些处理时间
    await asyncio.sleep(0.1)
    
    # 结束请求追踪
    request_tracker.end_request(
        request_id=request_id,
        status_code=200
    )
    
    # 检查请求已清理
    active_requests = request_tracker.get_active_requests()
    assert request_id not in active_requests, "请求应该已清理"
    
    print("✓ 请求追踪测试通过")


async def test_performance_metrics():
    """测试性能指标"""
    print("=== 测试性能指标 ===")
    
    # 重置指标
    performance_metrics.reset_metrics()
    
    # 记录一些请求
    performance_metrics.record_request(duration=1.0, success=True, cache_hit=True)
    performance_metrics.record_request(duration=2.5, success=True, cache_hit=False)
    performance_metrics.record_request(duration=0.5, success=False, retries=2)
    
    # 获取指标
    metrics = performance_metrics.get_metrics()
    
    assert metrics["request_count"] == 3, "请求数量应该正确"
    assert metrics["error_count"] == 1, "错误数量应该正确"
    assert metrics["cache_hits"] == 1, "缓存命中数应该正确"
    assert metrics["cache_misses"] == 2, "缓存未命中数应该正确"
    assert metrics["retry_count"] == 2, "重试次数应该正确"
    assert metrics["slow_requests"] == 1, "慢请求数应该正确"
    
    # 检查计算指标
    assert abs(metrics["average_duration"] - 1.33) < 0.1, "平均响应时间应该正确"
    assert abs(metrics["error_rate"] - 0.33) < 0.1, "错误率应该正确"
    assert abs(metrics["cache_hit_rate"] - 0.33) < 0.1, "缓存命中率应该正确"
    
    print("✓ 性能指标测试通过")


async def test_health_checker():
    """测试健康检查"""
    print("=== 测试健康检查 ===")
    
    # 检查所有组件
    components = await health_checker.check_all_components()
    
    assert len(components) > 0, "应该有组件被检查"
    
    # 获取健康报告
    health_report = health_checker.get_health_report()
    
    assert "overall_status" in health_report, "应该有整体状态"
    assert "components" in health_report, "应该有组件状态"
    assert "summary" in health_report, "应该有摘要信息"
    assert "performance_metrics" in health_report, "应该有性能指标"
    
    print("✓ 健康检查测试通过")


async def main():
    """主测试函数"""
    print("开始测试错误处理和日志系统...")
    
    try:
        # 运行所有测试
        await test_error_classification()
        await test_retry_mechanism()
        await test_error_handling_decorator()
        test_structured_logging()
        await test_request_tracking()
        await test_performance_metrics()
        await test_health_checker()
        
        print("\n🎉 所有测试通过！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())