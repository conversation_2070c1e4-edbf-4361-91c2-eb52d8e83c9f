#!/usr/bin/env python3
"""
缓存系统测试脚本

测试缓存的基本功能，包括存储、获取、过期和统计
"""

import asyncio
import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from zentao_mcp.cache import get_cache, cached_call, cache_key


async def test_basic_cache():
    """测试基本缓存功能"""
    print("=== 测试基本缓存功能 ===")
    
    cache = get_cache()
    
    # 测试存储和获取
    cache.set("test_key", "test_value", ttl=5)
    value = cache.get("test_key")
    print(f"存储后获取: {value}")
    assert value == "test_value", "缓存存储/获取失败"
    
    # 测试不存在的键
    value = cache.get("non_existent_key")
    print(f"不存在的键: {value}")
    assert value is None, "不存在的键应该返回None"
    
    # 测试统计信息
    stats = cache.get_stats()
    print(f"缓存统计: {stats}")
    assert stats['hits'] >= 1, "命中次数应该大于等于1"
    assert stats['misses'] >= 1, "未命中次数应该大于等于1"
    
    print("✓ 基本缓存功能测试通过")


async def test_cache_expiration():
    """测试缓存过期功能"""
    print("\n=== 测试缓存过期功能 ===")
    
    cache = get_cache()
    
    # 设置短期缓存
    cache.set("expire_test", "expire_value", ttl=1)
    
    # 立即获取应该成功
    value = cache.get("expire_test")
    print(f"立即获取: {value}")
    assert value == "expire_value", "立即获取应该成功"
    
    # 等待过期
    print("等待缓存过期...")
    await asyncio.sleep(1.5)
    
    # 过期后获取应该返回None
    value = cache.get("expire_test")
    print(f"过期后获取: {value}")
    assert value is None, "过期后应该返回None"
    
    print("✓ 缓存过期功能测试通过")


async def test_cached_call():
    """测试缓存装饰器功能"""
    print("\n=== 测试缓存装饰器功能 ===")
    
    call_count = 0
    
    async def expensive_function(param):
        nonlocal call_count
        call_count += 1
        print(f"执行昂贵函数，参数: {param}, 调用次数: {call_count}")
        await asyncio.sleep(0.1)  # 模拟耗时操作
        return f"result_{param}_{call_count}"
    
    # 第一次调用
    key = cache_key("expensive", "param1")
    result1 = await cached_call(key, expensive_function, "param1", ttl=5)
    print(f"第一次调用结果: {result1}")
    
    # 第二次调用应该从缓存获取
    result2 = await cached_call(key, expensive_function, "param1", ttl=5)
    print(f"第二次调用结果: {result2}")
    
    # 结果应该相同，但函数只调用了一次
    assert result1 == result2, "缓存结果应该相同"
    assert call_count == 1, "函数应该只调用一次"
    
    # 不同参数应该调用函数
    key2 = cache_key("expensive", "param2")
    result3 = await cached_call(key2, expensive_function, "param2", ttl=5)
    print(f"不同参数调用结果: {result3}")
    assert call_count == 2, "不同参数应该调用函数"
    
    print("✓ 缓存装饰器功能测试通过")


async def test_cache_cleanup():
    """测试缓存清理功能"""
    print("\n=== 测试缓存清理功能 ===")
    
    cache = get_cache()
    
    # 添加一些过期项
    cache.set("cleanup1", "value1", ttl=1)
    cache.set("cleanup2", "value2", ttl=2)
    cache.set("cleanup3", "value3", ttl=10)  # 不会过期
    
    print("添加了3个缓存项")
    stats_before = cache.get_stats()
    print(f"清理前统计: size={stats_before['size']}")
    
    # 等待部分过期
    await asyncio.sleep(2.5)  # 等待足够长时间让cleanup1和cleanup2都过期
    
    # 执行清理
    cleaned = cache.cleanup_expired()
    print(f"清理了 {cleaned} 个过期项")
    
    stats_after = cache.get_stats()
    print(f"清理后统计: size={stats_after['size']}")
    
    # 验证清理效果
    assert cache.get("cleanup1") is None, "cleanup1应该被清理"
    assert cache.get("cleanup2") is None, "cleanup2应该被清理"
    assert cache.get("cleanup3") == "value3", "cleanup3不应该被清理"
    
    print("✓ 缓存清理功能测试通过")


async def main():
    """主测试函数"""
    print("开始缓存系统测试...\n")
    
    try:
        await test_basic_cache()
        await test_cache_expiration()
        await test_cached_call()
        await test_cache_cleanup()
        
        print("\n🎉 所有缓存测试通过！")
        
        # 显示最终统计
        cache = get_cache()
        final_stats = cache.get_stats()
        print(f"\n最终缓存统计: {final_stats}")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)