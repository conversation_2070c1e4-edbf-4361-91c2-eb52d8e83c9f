"""
认证模块测试

测试Token认证、会话管理和安全配置管理功能
"""

import pytest
import time
import asyncio
from unittest.mock import MagicMock, AsyncMock, patch
from zentao_mcp.auth import (
    SessionInfo, SessionManager, TokenValidator, 
    validate_token, authenticate_request, AuthenticationMiddleware,
    RetryableAuthenticator
)
from zentao_mcp.exceptions import AuthenticationError, ConfigurationError


class TestSessionInfo:
    """会话信息测试"""
    
    def test_session_info_creation(self):
        """测试会话信息创建"""
        session = SessionInfo(session_id="test_session")
        
        assert session.session_id == "test_session"
        assert session.is_valid is True
        assert session.retry_count == 0
        assert isinstance(session.created_at, float)
        assert isinstance(session.last_used, float)
    
    def test_session_expiration(self):
        """测试会话过期检查"""
        # 创建已过期的会话
        session = SessionInfo(
            session_id="expired_session",
            expires_at=time.time() - 100  # 100秒前过期
        )
        
        assert session.is_expired() is True
        
        # 创建未过期的会话
        session = SessionInfo(
            session_id="valid_session",
            expires_at=time.time() + 100  # 100秒后过期
        )
        
        assert session.is_expired() is False
    
    def test_session_update_last_used(self):
        """测试更新最后使用时间"""
        session = SessionInfo()
        original_time = session.last_used
        
        time.sleep(0.01)  # 等待一小段时间
        session.update_last_used()
        
        assert session.last_used > original_time
    
    def test_session_mark_invalid(self):
        """测试标记会话无效"""
        session = SessionInfo()
        assert session.is_valid is True
        
        session.mark_invalid()
        assert session.is_valid is False
    
    def test_session_increment_retry(self):
        """测试增加重试次数"""
        session = SessionInfo()
        assert session.retry_count == 0
        
        session.increment_retry()
        assert session.retry_count == 1
        
        session.increment_retry()
        assert session.retry_count == 2


class TestSessionManager:
    """会话管理器测试"""
    
    @pytest.fixture
    def mock_api_client(self):
        """模拟API客户端"""
        client = AsyncMock()
        client._login = AsyncMock(return_value="test_session_id")
        return client
    
    def test_session_manager_creation(self):
        """测试会话管理器创建"""
        manager = SessionManager()
        
        assert manager._api_client is None
        assert manager._session_ttl == 3600
        assert manager._max_retry_attempts == 3  # 默认值
    
    def test_set_api_client(self, mock_api_client):
        """测试设置API客户端"""
        manager = SessionManager()
        manager.set_api_client(mock_api_client)
        
        assert manager._api_client == mock_api_client
    
    @pytest.mark.asyncio
    async def test_get_valid_session_new_login(self, mock_api_client):
        """测试获取有效会话 - 新登录"""
        manager = SessionManager(mock_api_client)
        
        session_id = await manager.get_valid_session()
        
        assert session_id == "test_session_id"
        assert manager._session_info.session_id == "test_session_id"
        assert manager._session_info.is_valid is True
        mock_api_client._login.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_valid_session_reuse_existing(self, mock_api_client):
        """测试获取有效会话 - 重用现有会话"""
        manager = SessionManager(mock_api_client)
        
        # 第一次获取会话
        session_id1 = await manager.get_valid_session()
        
        # 第二次获取会话（应该重用）
        session_id2 = await manager.get_valid_session()
        
        assert session_id1 == session_id2
        assert mock_api_client._login.call_count == 1  # 只调用一次登录
    
    @pytest.mark.asyncio
    async def test_get_valid_session_expired_renewal(self, mock_api_client):
        """测试获取有效会话 - 过期后重新登录"""
        manager = SessionManager(mock_api_client)
        
        # 第一次获取会话
        session_id1 = await manager.get_valid_session()
        
        # 手动设置会话过期
        manager._session_info.expires_at = time.time() - 100
        
        # 第二次获取会话（应该重新登录）
        session_id2 = await manager.get_valid_session()
        
        assert session_id1 == session_id2  # 会话ID相同（模拟返回相同ID）
        assert mock_api_client._login.call_count == 2  # 调用两次登录
    
    @pytest.mark.asyncio
    async def test_perform_login_retry_on_failure(self):
        """测试登录失败重试机制"""
        mock_client = AsyncMock()
        # 前两次失败，第三次成功
        mock_client._login.side_effect = [
            Exception("网络错误"),
            Exception("服务器错误"),
            "success_session_id"
        ]
        
        manager = SessionManager(mock_client)
        manager._max_retry_attempts = 3
        
        session_id = await manager.get_valid_session()
        
        assert session_id == "success_session_id"
        assert mock_client._login.call_count == 3
    
    @pytest.mark.asyncio
    async def test_perform_login_max_retries_exceeded(self):
        """测试登录重试次数超限"""
        mock_client = AsyncMock()
        mock_client._login.side_effect = Exception("持续失败")
        
        manager = SessionManager(mock_client)
        manager._max_retry_attempts = 2
        
        with pytest.raises(AuthenticationError) as exc_info:
            await manager.get_valid_session()
        
        assert "登录失败，已重试 2 次" in str(exc_info.value)
        assert mock_client._login.call_count == 3  # 初始尝试 + 2次重试
    
    @pytest.mark.asyncio
    async def test_invalidate_session(self, mock_api_client):
        """测试使会话无效"""
        manager = SessionManager(mock_api_client)
        
        # 获取有效会话
        await manager.get_valid_session()
        assert manager._session_info.is_valid is True
        
        # 使会话无效
        await manager.invalidate_session()
        assert manager._session_info.is_valid is False
    
    @pytest.mark.asyncio
    async def test_refresh_session_if_needed(self, mock_api_client):
        """测试按需刷新会话"""
        manager = SessionManager(mock_api_client)
        
        # 获取有效会话
        await manager.get_valid_session()
        
        # 设置会话即将过期（剩余时间少于5分钟）
        manager._session_info.expires_at = time.time() + 200  # 200秒后过期
        
        # 刷新会话
        refreshed = await manager.refresh_session_if_needed()
        
        assert refreshed is True
        assert mock_api_client._login.call_count == 2  # 初始登录 + 刷新登录
    
    def test_get_session_info(self, mock_api_client):
        """测试获取会话信息摘要"""
        manager = SessionManager(mock_api_client)
        
        info = manager.get_session_info()
        
        assert "has_session" in info
        assert "is_valid" in info
        assert "is_expired" in info
        assert "created_at" in info
        assert "last_used" in info
        assert "retry_count" in info


class TestTokenValidator:
    """Token验证器测试"""
    
    @pytest.fixture
    def mock_settings(self):
        """模拟设置"""
        with patch('zentao_mcp.auth.settings') as mock_settings:
            mock_settings.enable_auth = True
            mock_settings.mcp_token = "test-secret-token-123456"
            mock_settings.token_min_length = 16
            yield mock_settings
    
    def test_token_validator_creation(self, mock_settings):
        """测试Token验证器创建"""
        validator = TokenValidator()
        
        assert validator._max_failed_attempts == 5
        assert validator._lockout_duration == 300
        assert isinstance(validator._failed_attempts, dict)
    
    def test_validate_config_success(self, mock_settings):
        """测试配置验证成功"""
        # 不应该抛出异常
        validator = TokenValidator()
        assert validator is not None
    
    def test_validate_config_missing_token(self, mock_settings):
        """测试配置验证 - 缺少Token"""
        mock_settings.mcp_token = None
        
        with pytest.raises(ConfigurationError) as exc_info:
            TokenValidator()
        
        assert "必须正确配置MCP_TOKEN" in str(exc_info.value)
    
    def test_validate_config_short_token(self, mock_settings):
        """测试配置验证 - Token太短"""
        mock_settings.mcp_token = "short"
        mock_settings.token_min_length = 16
        
        with pytest.raises(ConfigurationError) as exc_info:
            TokenValidator()
        
        assert "Token长度不能少于16位" in str(exc_info.value)
    
    def test_validate_token_success(self, mock_settings):
        """测试Token验证成功"""
        validator = TokenValidator()
        
        result = validator.validate_token("test-secret-token-123456")
        
        assert result is True
    
    def test_validate_token_failure(self, mock_settings):
        """测试Token验证失败"""
        validator = TokenValidator()
        
        result = validator.validate_token("wrong-token")
        
        assert result is False
    
    def test_validate_token_empty(self, mock_settings):
        """测试空Token验证"""
        validator = TokenValidator()
        
        result = validator.validate_token("")
        
        assert result is False
    
    def test_validate_token_auth_disabled(self, mock_settings):
        """测试认证禁用时的Token验证"""
        mock_settings.enable_auth = False
        validator = TokenValidator()
        
        result = validator.validate_token("any-token")
        
        assert result is True
    
    def test_failed_attempts_tracking(self, mock_settings):
        """测试失败尝试跟踪"""
        validator = TokenValidator()
        client_ip = "*************"
        
        # 多次失败尝试
        for _ in range(3):
            validator.validate_token("wrong-token", client_ip)
        
        assert client_ip in validator._failed_attempts
        assert len(validator._failed_attempts[client_ip]) == 3
    
    def test_lockout_mechanism(self, mock_settings):
        """测试锁定机制"""
        validator = TokenValidator()
        client_ip = "*************"
        
        # 达到最大失败次数
        for _ in range(validator._max_failed_attempts):
            validator.validate_token("wrong-token", client_ip)
        
        # 下一次尝试应该被锁定
        result = validator.validate_token("test-secret-token-123456", client_ip)
        
        assert result is False  # 被锁定，即使Token正确也返回False
    
    def test_clear_failed_attempts_on_success(self, mock_settings):
        """测试成功验证后清除失败记录"""
        validator = TokenValidator()
        client_ip = "*************"
        
        # 几次失败尝试
        for _ in range(3):
            validator.validate_token("wrong-token", client_ip)
        
        assert client_ip in validator._failed_attempts
        
        # 成功验证
        validator.validate_token("test-secret-token-123456", client_ip)
        
        assert client_ip not in validator._failed_attempts
    
    def test_extract_token_from_context_bearer(self, mock_settings):
        """测试从上下文提取Bearer Token"""
        validator = TokenValidator()
        context_meta = {
            "authorization": "Bearer test-token-123"
        }
        
        token = validator.extract_token_from_context(context_meta)
        
        assert token == "test-token-123"
    
    def test_extract_token_from_context_api_key(self, mock_settings):
        """测试从上下文提取API Key"""
        validator = TokenValidator()
        context_meta = {
            "x-api-key": "api-key-456"
        }
        
        token = validator.extract_token_from_context(context_meta)
        
        assert token == "api-key-456"
    
    def test_extract_token_from_context_none(self, mock_settings):
        """测试从上下文提取Token - 无Token"""
        validator = TokenValidator()
        context_meta = {}
        
        token = validator.extract_token_from_context(context_meta)
        
        assert token is None
    
    def test_get_client_ip(self, mock_settings):
        """测试获取客户端IP"""
        validator = TokenValidator()
        context_meta = {
            "x-forwarded-for": "*************, ********",
            "x-real-ip": "*************"
        }
        
        ip = validator.get_client_ip(context_meta)
        
        # 应该返回第一个IP（去除逗号后的部分）
        assert ip == "*************"


class TestAuthenticationMiddleware:
    """认证中间件测试"""
    
    @pytest.fixture
    def mock_context(self):
        """模拟上下文"""
        ctx = MagicMock()
        ctx.meta = {
            "authorization": "Bearer test-token-123"
        }
        return ctx
    
    @pytest.fixture
    def mock_settings(self):
        """模拟设置"""
        with patch('zentao_mcp.auth.settings') as mock_settings:
            mock_settings.enable_auth = True
            mock_settings.mcp_token = "test-token-123"
            mock_settings.token_min_length = 10
            yield mock_settings
    
    @pytest.mark.asyncio
    async def test_middleware_success(self, mock_context, mock_settings):
        """测试中间件成功处理"""
        middleware = AuthenticationMiddleware()
        
        async def mock_call_next(ctx):
            return "success_result"
        
        result = await middleware(mock_context, mock_call_next)
        
        assert result == "success_result"
        assert mock_context.auth_token == "test-token-123"
        assert mock_context.authenticated is True
        assert hasattr(mock_context, 'request_id')
    
    @pytest.mark.asyncio
    async def test_middleware_auth_failure(self, mock_context, mock_settings):
        """测试中间件认证失败"""
        middleware = AuthenticationMiddleware()
        mock_context.meta = {"authorization": "Bearer wrong-token"}
        
        async def mock_call_next(ctx):
            return "success_result"
        
        with pytest.raises(AuthenticationError):
            await middleware(mock_context, mock_call_next)
    
    def test_get_stats(self, mock_settings):
        """测试获取统计信息"""
        middleware = AuthenticationMiddleware()
        
        stats = middleware.get_stats()
        
        assert "request_count" in stats
        assert "uptime_seconds" in stats
        assert "requests_per_second" in stats
        assert "auth_enabled" in stats


class TestRetryableAuthenticator:
    """可重试认证器测试"""
    
    @pytest.mark.asyncio
    async def test_authenticate_with_retry_success(self):
        """测试重试认证成功"""
        authenticator = RetryableAuthenticator(max_retries=2, retry_delay=0.01)
        
        async def mock_auth_func():
            return "auth_success"
        
        result = await authenticator.authenticate_with_retry(mock_auth_func)
        
        assert result == "auth_success"
    
    @pytest.mark.asyncio
    async def test_authenticate_with_retry_eventual_success(self):
        """测试重试认证最终成功"""
        authenticator = RetryableAuthenticator(max_retries=2, retry_delay=0.01)
        
        call_count = 0
        async def mock_auth_func():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise AuthenticationError("临时失败")
            return "auth_success"
        
        result = await authenticator.authenticate_with_retry(mock_auth_func)
        
        assert result == "auth_success"
        assert call_count == 3
    
    @pytest.mark.asyncio
    async def test_authenticate_with_retry_max_retries_exceeded(self):
        """测试重试认证超过最大次数"""
        authenticator = RetryableAuthenticator(max_retries=2, retry_delay=0.01)
        
        async def mock_auth_func():
            raise AuthenticationError("持续失败")
        
        with pytest.raises(AuthenticationError) as exc_info:
            await authenticator.authenticate_with_retry(mock_auth_func)
        
        assert "认证失败，已重试 2 次" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_authenticate_with_retry_non_auth_error(self):
        """测试重试认证遇到非认证错误"""
        authenticator = RetryableAuthenticator(max_retries=2, retry_delay=0.01)
        
        async def mock_auth_func():
            raise ValueError("非认证错误")
        
        with pytest.raises(AuthenticationError) as exc_info:
            await authenticator.authenticate_with_retry(mock_auth_func)
        
        assert "认证处理失败" in str(exc_info.value)


class TestAuthenticationFunctions:
    """认证函数测试"""
    
    @pytest.fixture
    def mock_settings(self):
        """模拟设置"""
        with patch('zentao_mcp.auth.settings') as mock_settings:
            mock_settings.enable_auth = True
            mock_settings.mcp_token = "test-token-123"
            mock_settings.token_min_length = 10
            yield mock_settings
    
    def test_validate_token_function(self, mock_settings):
        """测试validate_token函数"""
        result = validate_token("test-token-123")
        assert result is True
        
        result = validate_token("wrong-token")
        assert result is False
    
    def test_authenticate_request_success(self, mock_settings):
        """测试authenticate_request函数成功"""
        context_meta = {
            "authorization": "Bearer test-token-123"
        }
        
        token = authenticate_request(context_meta)
        
        assert token == "test-token-123"
    
    def test_authenticate_request_missing_token(self, mock_settings):
        """测试authenticate_request函数缺少Token"""
        context_meta = {}
        
        with pytest.raises(AuthenticationError) as exc_info:
            authenticate_request(context_meta)
        
        assert "缺少认证Token" in str(exc_info.value)
    
    def test_authenticate_request_invalid_token(self, mock_settings):
        """测试authenticate_request函数无效Token"""
        context_meta = {
            "authorization": "Bearer wrong-token"
        }
        
        with pytest.raises(AuthenticationError) as exc_info:
            authenticate_request(context_meta)
        
        assert "无效的认证Token" in str(exc_info.value)
    
    def test_authenticate_request_auth_disabled(self, mock_settings):
        """测试authenticate_request函数认证禁用"""
        mock_settings.enable_auth = False
        context_meta = {}
        
        token = authenticate_request(context_meta)
        
        assert token == "auth_disabled"


class TestAuthenticationIntegration:
    """认证集成测试"""
    
    @pytest.mark.asyncio
    async def test_full_authentication_flow(self):
        """测试完整认证流程"""
        # 模拟完整的认证流程
        with patch('zentao_mcp.auth.settings') as mock_settings:
            mock_settings.enable_auth = True
            mock_settings.mcp_token = "integration-test-token"
            mock_settings.token_min_length = 10
            
            # 创建认证组件
            validator = TokenValidator()
            middleware = AuthenticationMiddleware()
            
            # 模拟请求上下文
            ctx = MagicMock()
            ctx.meta = {
                "authorization": "Bearer integration-test-token",
                "x-real-ip": "*************"
            }
            
            # 模拟下一个处理器
            async def mock_call_next(context):
                assert context.authenticated is True
                assert context.auth_token == "integration-test-token"
                return "integration_success"
            
            # 执行认证流程
            result = await middleware(ctx, mock_call_next)
            
            assert result == "integration_success"
    
    @pytest.mark.asyncio
    async def test_concurrent_authentication(self):
        """测试并发认证"""
        with patch('zentao_mcp.auth.settings') as mock_settings:
            mock_settings.enable_auth = True
            mock_settings.mcp_token = "concurrent-test-token"
            mock_settings.token_min_length = 10
            
            middleware = AuthenticationMiddleware()
            
            async def create_auth_task(task_id):
                ctx = MagicMock()
                ctx.meta = {
                    "authorization": "Bearer concurrent-test-token",
                    "x-real-ip": f"192.168.1.{task_id}"
                }
                
                async def mock_call_next(context):
                    return f"task_{task_id}_success"
                
                return await middleware(ctx, mock_call_next)
            
            # 创建多个并发认证任务
            tasks = [create_auth_task(i) for i in range(10)]
            results = await asyncio.gather(*tasks)
            
            # 验证所有任务都成功
            for i, result in enumerate(results):
                assert result == f"task_{i}_success"
