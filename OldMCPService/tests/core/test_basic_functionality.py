"""
基本功能测试

验证测试套件的核心功能是否正常工作
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock


class TestBasicFunctionality:
    """基本功能测试类"""
    
    def test_imports_work(self):
        """测试基本导入是否正常"""
        # 测试主要模块导入
        from zentao_mcp.main import ZentaoMCPServer
        from zentao_mcp.client import ZentaoClient
        from zentao_mcp.config import settings
        from zentao_mcp.exceptions import ZentaoAPIError, ValidationError
        
        assert ZentaoMCPServer is not None
        assert ZentaoClient is not None
        assert settings is not None
        assert ZentaoAPIError is not None
        assert ValidationError is not None
    
    def test_server_creation(self):
        """测试服务器创建"""
        from zentao_mcp.main import ZentaoMCPServer
        
        server = ZentaoMCPServer("test-server")
        assert server is not None
        assert server.name == "test-server"
        assert hasattr(server, 'mcp')
        assert hasattr(server, '_client_context')
    
    def test_client_creation(self):
        """测试客户端创建"""
        from zentao_mcp.client import ZentaoClient
        
        client = ZentaoClient()
        assert client is not None
    
    def test_config_loading(self):
        """测试配置加载"""
        from zentao_mcp.config import settings
        
        assert hasattr(settings, 'log_level')
    
    def test_exception_classes(self):
        """测试异常类"""
        from zentao_mcp.exceptions import ZentaoAPIError, ValidationError, ZentaoMCPError
        
        # 测试异常创建
        api_error = ZentaoAPIError("API错误")
        validation_error = ValidationError("验证错误")
        mcp_error = ZentaoMCPError("MCP错误")
        
        assert str(api_error) == "API错误"
        assert str(validation_error) == "验证错误"
        assert str(mcp_error) == "MCP错误"
    
    @pytest.mark.asyncio
    async def test_async_functionality(self):
        """测试异步功能"""
        # 创建异步Mock
        mock_client = AsyncMock()
        mock_client.get_all_departments_async.return_value = {
            "rsCode": "00000000",
            "msg": "正常返回",
            "body": [{"id": 1, "name": "测试部门"}]
        }
        
        # 测试异步调用
        result = await mock_client.get_all_departments_async()
        assert result["rsCode"] == "00000000"
        assert len(result["body"]) == 1
        assert result["body"][0]["name"] == "测试部门"
    
    def test_mock_functionality(self):
        """测试Mock功能"""
        # 测试Mock创建和配置
        mock_obj = MagicMock()
        mock_obj.test_method.return_value = "test_result"
        
        result = mock_obj.test_method()
        assert result == "test_result"
        mock_obj.test_method.assert_called_once()
    
    def test_data_structures(self):
        """测试数据结构处理"""
        # 测试禅道API响应格式
        sample_response = {
            "rsCode": "00000000",
            "msg": "正常返回",
            "body": [
                {"id": 1, "name": "项目A", "status": "doing"},
                {"id": 2, "name": "项目B", "status": "done"}
            ]
        }
        
        # 验证响应格式
        assert "rsCode" in sample_response
        assert "msg" in sample_response
        assert "body" in sample_response
        assert isinstance(sample_response["body"], list)
        assert len(sample_response["body"]) == 2
    
    def test_data_analysis_logic(self):
        """测试数据分析逻辑"""
        # 测试基本的数据分析功能
        sample_stories = [
            {"id": 1, "status": "active", "estimate": 8.0},
            {"id": 2, "status": "closed", "estimate": 12.0},
            {"id": 3, "status": "active", "estimate": 16.0}
        ]
        
        # 计算统计信息
        total_count = len(sample_stories)
        active_count = sum(1 for story in sample_stories if story["status"] == "active")
        closed_count = sum(1 for story in sample_stories if story["status"] == "closed")
        total_estimate = sum(story.get("estimate", 0.0) for story in sample_stories)
        
        # 验证计算结果
        assert total_count == 3
        assert active_count == 2
        assert closed_count == 1
        assert total_estimate == 36.0
    
    def test_error_handling_logic(self):
        """测试错误处理逻辑"""
        from zentao_mcp.exceptions import ZentaoAPIError
        
        # 测试错误响应处理
        error_response = {
            "rsCode": "99999999",
            "msg": "系统错误",
            "body": None
        }
        
        # 模拟错误处理逻辑
        if error_response.get("rsCode") != "00000000":
            error_msg = error_response.get("msg", "未知错误")
            # 这里在实际代码中会抛出异常
            assert error_msg == "系统错误"
    
    def test_performance_considerations(self):
        """测试性能相关考虑"""
        import time
        
        # 测试大数据集处理性能
        large_dataset = [{"id": i, "value": f"item_{i}"} for i in range(1000)]
        
        start_time = time.time()
        
        # 模拟数据处理
        processed_count = 0
        for item in large_dataset:
            if item["id"] % 2 == 0:
                processed_count += 1
        
        processing_time = time.time() - start_time
        
        # 验证处理结果和性能
        assert processed_count == 500  # 一半的数据
        assert processing_time < 0.1   # 处理时间应该很快
        assert len(large_dataset) == 1000


class TestToolsArchitecture:
    """工具架构测试"""
    
    def test_direct_tools_import(self):
        """测试直接工具导入"""
        from zentao_mcp.tools.direct.department import register_department_direct_tools
        from zentao_mcp.tools.direct.project import register_project_direct_tools
        from zentao_mcp.tools.direct.story import register_story_direct_tools
        
        assert callable(register_department_direct_tools)
        assert callable(register_project_direct_tools)
        assert callable(register_story_direct_tools)
    
    def test_analysis_tools_import(self):
        """测试分析工具导入"""
        from zentao_mcp.tools.analysis.project import register_project_analysis_tools
        from zentao_mcp.tools.analysis.story import register_story_analysis_tools
        from zentao_mcp.tools.analysis.bug import register_bug_analysis_tools
        
        assert callable(register_project_analysis_tools)
        assert callable(register_story_analysis_tools)
        assert callable(register_bug_analysis_tools)
    
    def test_system_tools_import(self):
        """测试系统工具导入"""
        from zentao_mcp.tools.system.health import register_health_tools
        
        assert callable(register_health_tools)
    
    def test_tools_registration(self):
        """测试工具注册机制"""
        from zentao_mcp.tools import register_all_tools
        
        # 创建模拟MCP实例
        mock_mcp = MagicMock()
        tools_registered = []
        
        def capture_tool(func):
            tools_registered.append(func)
            return func
        
        mock_mcp.tool.return_value = capture_tool
        
        # 注册所有工具
        register_all_tools(mock_mcp)
        
        # 验证工具注册
        assert len(tools_registered) > 0  # 应该注册了一些工具
        
        # 验证所有注册的工具都是可调用的
        for tool_func in tools_registered:
            assert callable(tool_func)


class TestResourcesArchitecture:
    """资源架构测试"""
    
    def test_resources_import(self):
        """测试资源导入"""
        from zentao_mcp.resources.department import register_department_resources
        from zentao_mcp.resources.project import register_project_resources
        from zentao_mcp.resources.story import register_story_resources
        
        assert callable(register_department_resources)
        assert callable(register_project_resources)
        assert callable(register_story_resources)


class TestCacheAndAuth:
    """缓存和认证测试"""
    
    def test_cache_import(self):
        """测试缓存模块导入"""
        from zentao_mcp.cache import MemoryCache
        
        assert MemoryCache is not None
    
    def test_auth_import(self):
        """测试认证模块导入"""
        from zentao_mcp.auth import authenticate_request, AuthenticationError
        
        assert callable(authenticate_request)
        assert AuthenticationError is not None


class TestHealthCheck:
    """健康检查测试"""
    
    def test_health_check_import(self):
        """测试健康检查导入"""
        from zentao_mcp.health_check import health_checker
        
        assert health_checker is not None
        assert hasattr(health_checker, 'check_all_components')
        assert hasattr(health_checker, 'start_periodic_checks')
        assert hasattr(health_checker, 'stop_periodic_checks')


class TestLogging:
    """日志测试"""
    
    def test_logging_import(self):
        """测试日志模块导入"""
        from zentao_mcp.logging_config import setup_logging, get_logger
        
        assert callable(setup_logging)
        assert callable(get_logger)
    
    def test_logger_creation(self):
        """测试日志器创建"""
        from zentao_mcp.logging_config import get_logger
        
        logger = get_logger("test_logger")
        assert logger is not None
        assert logger.name == "test_logger"


class TestIntegrationReadiness:
    """集成就绪性测试"""
    
    @pytest.mark.asyncio
    async def test_server_startup_components(self):
        """测试服务器启动组件"""
        from zentao_mcp.main import ZentaoMCPServer
        
        server = ZentaoMCPServer("test-server")
        
        # 验证服务器具有必要的组件
        assert hasattr(server, 'mcp')
        assert hasattr(server, '_client_context')
        assert hasattr(server, 'register_resources')
        assert hasattr(server, 'register_tools')
        assert hasattr(server, 'startup')
        assert hasattr(server, 'shutdown')
    
    def test_mcp_tools_structure(self):
        """测试MCP工具结构"""
        # 验证工具模块结构
        import zentao_mcp.tools.direct
        import zentao_mcp.tools.analysis
        import zentao_mcp.tools.system
        
        assert zentao_mcp.tools.direct is not None
        assert zentao_mcp.tools.analysis is not None
        assert zentao_mcp.tools.system is not None
    
    def test_configuration_completeness(self):
        """测试配置完整性"""
        from zentao_mcp.config import settings
        
        # 验证关键配置项存在
        required_settings = [
            'log_level',
            'log_dir',
            'cache_ttl',
            'mcp_transport'
        ]
        
        for setting in required_settings:
            assert hasattr(settings, setting), f"缺少配置项: {setting}"