#!/usr/bin/env python3
"""
快速HTTP连通性验证脚本

直接运行此脚本可以快速测试禅道API的连通性，无需运行完整的pytest套件
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from zentao_mcp.client import ZentaoClient
from zentao_mcp.config import settings
from zentao_mcp.exceptions import ZentaoAPIError, NetworkError, TimeoutError


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class ConnectivityChecker:
    """连通性检查器"""
    
    def __init__(self):
        self.client = None
        self.results = {}
        
    async def initialize(self):
        """初始化客户端"""
        try:
            logger.info("正在初始化禅道客户端...")
            self.client = ZentaoClient()
            await self.client.initialize()
            logger.info(f"客户端初始化成功，基础URL: {self.client.base_url}")
            return True
        except Exception as e:
            logger.error(f"客户端初始化失败: {e}")
            return False
    
    async def test_basic_connectivity(self):
        """测试基本连通性"""
        logger.info("=== 测试基本连通性 ===")
        
        tests = [
            ("部门列表", lambda: self.client.get_all_departments_async(parse_model=False)),
            ("项目列表", lambda: self.client.get_all_projects_async(parse_model=False)),
        ]
        
        for test_name, test_func in tests:
            try:
                logger.info(f"测试 {test_name}...")
                response = await test_func()
                
                if isinstance(response, dict):
                    rs_code = response.get("rsCode")
                    msg = response.get("msg", "")
                    body_count = len(response.get("body", []))
                    
                    if rs_code == "00000000":
                        logger.info(f"✅ {test_name} 成功: 返回 {body_count} 条记录")
                        self.results[test_name] = {"status": "success", "count": body_count}
                    else:
                        logger.warning(f"⚠️  {test_name} 返回错误: {rs_code} - {msg}")
                        self.results[test_name] = {"status": "api_error", "code": rs_code, "msg": msg}
                elif isinstance(response, list):
                    # 如果返回的是模型列表，也算成功
                    logger.info(f"✅ {test_name} 成功: 返回 {len(response)} 条记录（模型格式）")
                    self.results[test_name] = {"status": "success", "count": len(response)}
                else:
                    logger.error(f"❌ {test_name} 返回格式异常: {type(response)}")
                    self.results[test_name] = {"status": "format_error", "type": str(type(response))}
                    
            except Exception as e:
                logger.error(f"❌ {test_name} 异常: {type(e).__name__}: {e}")
                self.results[test_name] = {"status": "exception", "error": str(e)}
    
    async def test_specific_endpoints(self):
        """测试特定端点"""
        logger.info("=== 测试特定端点 ===")
        
        # 先获取一些基础数据用于后续测试
        projects_response = await self.client.get_all_projects_async(parse_model=False)
        test_project_id = None
        
        if isinstance(projects_response, dict) and projects_response.get("rsCode") == "00000000":
            projects = projects_response.get("body", [])
            if projects:
                test_project_id = projects[0].get("id")
                logger.info(f"使用测试项目ID: {test_project_id}")
        elif isinstance(projects_response, list) and projects_response:
            # 如果返回的是模型列表
            test_project_id = getattr(projects_response[0], 'id', None)
            logger.info(f"使用测试项目ID: {test_project_id}")
        
        # 测试具体接口
        if test_project_id:
            endpoints = [
                ("项目任务", lambda: self.client.get_tasks_by_project_async(test_project_id)),
                ("项目需求", lambda: self.client.get_stories_by_project_async(test_project_id)),
                ("项目Bug", lambda: self.client.get_bugs_by_project_async(test_project_id)),
            ]
            
            for endpoint_name, endpoint_func in endpoints:
                try:
                    logger.info(f"测试 {endpoint_name}...")
                    response = await endpoint_func()
                    
                    if isinstance(response, dict):
                        if response.get("rsCode") == "00000000":
                            count = len(response.get("body", []))
                            logger.info(f"✅ {endpoint_name} 成功: 返回 {count} 条记录")
                            self.results[endpoint_name] = {"status": "success", "count": count}
                        else:
                            rs_code = response.get("rsCode")
                            msg = response.get("msg", "")
                            logger.warning(f"⚠️  {endpoint_name} 返回错误: {rs_code} - {msg}")
                            self.results[endpoint_name] = {"status": "api_error", "code": rs_code, "msg": msg}
                    elif isinstance(response, list):
                        logger.info(f"✅ {endpoint_name} 成功: 返回 {len(response)} 条记录（模型格式）")
                        self.results[endpoint_name] = {"status": "success", "count": len(response)}
                    else:
                        logger.error(f"❌ {endpoint_name} 返回格式异常: {type(response)}")
                        self.results[endpoint_name] = {"status": "format_error", "type": str(type(response))}
                        
                except Exception as e:
                    logger.error(f"❌ {endpoint_name} 异常: {type(e).__name__}: {e}")
                    self.results[endpoint_name] = {"status": "exception", "error": str(e)}
        else:
            logger.warning("无法获取测试项目ID，跳过特定端点测试")
    
    async def test_batch_operations(self):
        """测试批量操作"""
        logger.info("=== 测试批量操作 ===")
        
        batch_tests = [
            ("需求存在性检查", lambda: self.client.check_story_exists_async(["28673", "12345"])),
            ("需求工时查询", lambda: self.client.get_story_info_async(["28673"])),
        ]
        
        for test_name, test_func in batch_tests:
            try:
                logger.info(f"测试 {test_name}...")
                response = await test_func()
                
                if response.get("rsCode") == "00000000":
                    logger.info(f"✅ {test_name} 成功")
                    self.results[test_name] = {"status": "success"}
                else:
                    rs_code = response.get("rsCode")
                    msg = response.get("msg", "")
                    logger.warning(f"⚠️  {test_name} 返回错误: {rs_code} - {msg}")
                    self.results[test_name] = {"status": "api_error", "code": rs_code, "msg": msg}
                    
            except Exception as e:
                logger.error(f"❌ {test_name} 异常: {type(e).__name__}: {e}")
                self.results[test_name] = {"status": "exception", "error": str(e)}
    
    async def test_performance(self):
        """测试性能"""
        logger.info("=== 测试性能 ===")
        
        import time
        
        # 测试单个请求响应时间
        start_time = time.time()
        response = await self.client.get_all_departments_async()
        single_request_time = time.time() - start_time
        
        logger.info(f"单个请求响应时间: {single_request_time:.3f}秒")
        
        # 测试并发请求
        start_time = time.time()
        tasks = [
            self.client.get_all_departments_async(),
            self.client.get_all_projects_async(),
        ]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        concurrent_time = time.time() - start_time
        
        successful_concurrent = sum(
            1 for result in results 
            if not isinstance(result, Exception)
        )
        
        logger.info(f"并发请求时间: {concurrent_time:.3f}秒")
        logger.info(f"并发请求成功率: {successful_concurrent}/{len(tasks)}")
        
        self.results["性能测试"] = {
            "single_request_time": single_request_time,
            "concurrent_time": concurrent_time,
            "concurrent_success_rate": successful_concurrent / len(tasks)
        }
    
    async def cleanup(self):
        """清理资源"""
        if self.client and hasattr(self.client, '_client'):
            await self.client._client.aclose()
            logger.info("客户端连接已关闭")
    
    def print_summary(self):
        """打印测试摘要"""
        logger.info("=== 测试摘要 ===")
        
        successful_tests = 0
        total_tests = 0
        
        for test_name, result in self.results.items():
            if test_name == "性能测试":
                continue
                
            total_tests += 1
            status = result.get("status")
            
            if status == "success":
                successful_tests += 1
                logger.info(f"✅ {test_name}: 成功")
            elif status == "api_error":
                logger.warning(f"⚠️  {test_name}: API错误 ({result.get('code')})")
            elif status == "exception":
                logger.error(f"❌ {test_name}: 异常")
            else:
                logger.error(f"❌ {test_name}: 其他错误")
        
        success_rate = successful_tests / total_tests if total_tests > 0 else 0
        logger.info(f"总体成功率: {successful_tests}/{total_tests} ({success_rate:.1%})")
        
        # 输出性能信息
        if "性能测试" in self.results:
            perf = self.results["性能测试"]
            logger.info(f"单请求响应时间: {perf.get('single_request_time', 0):.3f}秒")
            logger.info(f"并发请求响应时间: {perf.get('concurrent_time', 0):.3f}秒")
            logger.info(f"并发成功率: {perf.get('concurrent_success_rate', 0):.1%}")


async def main():
    """主函数"""
    logger.info("开始HTTP连通性检查...")
    logger.info(f"当前环境: {settings.environment}")
    logger.info(f"禅道基础URL: {settings.get_zentao_base_url()}")
    
    checker = ConnectivityChecker()
    
    try:
        # 初始化
        if not await checker.initialize():
            logger.error("初始化失败，退出测试")
            return
        
        # 执行各种测试
        await checker.test_basic_connectivity()
        await checker.test_specific_endpoints()
        await checker.test_batch_operations()
        await checker.test_performance()
        
        # 打印摘要
        checker.print_summary()
        
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
    finally:
        await checker.cleanup()
        logger.info("HTTP连通性检查完成")


def run_connectivity_check():
    """运行连通性检查的便捷函数"""
    try:
        asyncio.run(main())
    except Exception as e:
        logger.error(f"运行连通性检查失败: {e}")
        return False
    return True


if __name__ == "__main__":
    # 检查是否有命令行参数指定环境
    if len(sys.argv) > 1:
        env = sys.argv[1]
        if env in ["beta", "online"]:
            settings.environment = env
            logger.info(f"使用命令行指定的环境: {env}")
        else:
            logger.warning(f"无效的环境参数: {env}，使用默认环境: {settings.environment}")
    
    # 运行测试
    success = run_connectivity_check()
    sys.exit(0 if success else 1)