#!/usr/bin/env python3
"""
测试健康检查功能
"""

import asyncio
import os
import sys
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from zentao_mcp.health_check import health_checker
from zentao_mcp.logging_config import setup_logging
from zentao_mcp.config import settings


async def test_health_check():
    """测试健康检查功能"""
    
    # 设置日志
    setup_logging(
        log_level="INFO",
        json_format=False,
        enable_file_logging=True,
        enable_console_logging=True
    )
    
    logger = logging.getLogger("test_health_check")
    
    print("=" * 50)
    print("测试禅道MCP健康检查功能")
    print("=" * 50)
    
    try:
        # 执行健康检查
        logger.info("开始执行健康检查...")
        print("\n1. 执行健康检查...")
        
        components = await health_checker.check_all_components()
        
        print(f"\n2. 检查结果:")
        print(f"   - 整体状态: {health_checker.overall_status.value}")
        print(f"   - 组件数量: {len(components)}")
        
        for name, component in components.items():
            status_icon = "✅" if component.status.value == "healthy" else "❌" if component.status.value == "unhealthy" else "⚠️"
            print(f"   - {name}: {status_icon} {component.status.value} - {component.message}")
            
            if component.details:
                for key, value in component.details.items():
                    if key == "response_time":
                        print(f"     └─ 响应时间: {value:.3f}秒")
                    elif key == "error":
                        print(f"     └─ 错误: {value}")
        
        # 获取健康报告
        print(f"\n3. 获取详细健康报告...")
        health_report = health_checker.get_health_report()
        
        print(f"   - 最后检查时间: {health_report.get('last_check', 'N/A')}")
        print(f"   - 健康组件: {health_report['summary']['healthy']}")
        print(f"   - 异常组件: {health_report['summary']['unhealthy']}")
        print(f"   - 降级组件: {health_report['summary']['degraded']}")
        print(f"   - 未知组件: {health_report['summary']['unknown']}")
        
        # 检查日志文件
        log_dir = settings.log_dir
        health_log_file = os.path.join(log_dir, "zentao_mcp_health.log")
        
        print(f"\n4. 检查日志文件:")
        print(f"   - 日志目录: {log_dir}")
        print(f"   - 健康检查日志: {health_log_file}")
        
        if os.path.exists(health_log_file):
            file_size = os.path.getsize(health_log_file)
            print(f"   - 日志文件大小: {file_size} 字节")
            
            # 显示最后几行日志
            with open(health_log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    print(f"   - 最后几行日志:")
                    for line in lines[-3:]:
                        print(f"     {line.strip()}")
        else:
            print(f"   - ⚠️ 健康检查日志文件不存在")
        
        print(f"\n✅ 健康检查测试完成!")
        print(f"📝 详细的API请求日志已记录到: {health_log_file}")
        print(f"🖥️ 控制台只显示连通性检查结果，不显示详细请求日志")
        
    except Exception as e:
        logger.error(f"健康检查测试失败: {e}", exc_info=True)
        print(f"\n❌ 测试失败: {e}")
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(test_health_check())
    sys.exit(0 if success else 1)