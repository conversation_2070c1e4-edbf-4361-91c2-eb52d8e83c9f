#!/usr/bin/env python3
"""
性能和并发测试脚本

测试缓存系统、并发处理和性能优化功能
"""

import asyncio
import time
import sys
import os
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from zentao_mcp.cache import (
    get_cache, cached_call, ZentaoCacheKeys, ZentaoCacheTags,
    cache_key_hash, get_cache_stats
)
from zentao_mcp.config import settings


async def test_cache_performance():
    """测试缓存性能"""
    print("=== 测试缓存性能 ===")
    
    cache = get_cache()
    cache.clear()  # 清空缓存
    
    # 测试大量数据存储和检索
    test_data = {f"key_{i}": f"value_{i}" * 100 for i in range(1000)}
    
    # 存储性能测试
    start_time = time.time()
    for key, value in test_data.items():
        cache.set(key, value, ttl=300)
    store_time = time.time() - start_time
    
    print(f"存储1000个项目耗时: {store_time:.3f}s")
    
    # 检索性能测试
    start_time = time.time()
    hit_count = 0
    for key in test_data.keys():
        if cache.get(key) is not None:
            hit_count += 1
    retrieve_time = time.time() - start_time
    
    print(f"检索1000个项目耗时: {retrieve_time:.3f}s")
    print(f"缓存命中率: {hit_count/len(test_data)*100:.1f}%")
    
    # 显示缓存统计
    stats = cache.get_stats()
    print(f"缓存统计: 大小={stats['size']}, 命中率={stats['hit_rate']}%, 内存使用={stats['memory_usage_mb']}MB")
    
    print("✓ 缓存性能测试通过")


async def test_concurrent_cache_access():
    """测试并发缓存访问"""
    print("\n=== 测试并发缓存访问 ===")
    
    cache = get_cache()
    cache.clear()
    
    call_count = 0
    
    async def expensive_operation(param: str):
        nonlocal call_count
        call_count += 1
        await asyncio.sleep(0.1)  # 模拟耗时操作
        return f"result_{param}_{call_count}"
    
    # 创建多个并发任务访问相同的缓存键
    tasks = []
    for i in range(20):
        key = ZentaoCacheKeys.departments()  # 使用相同的键
        task = asyncio.create_task(
            cached_call(key, expensive_operation, "departments", ttl=60)
        )
        tasks.append(task)
    
    start_time = time.time()
    results = await asyncio.gather(*tasks)
    concurrent_time = time.time() - start_time
    
    print(f"20个并发任务完成时间: {concurrent_time:.3f}s")
    print(f"实际函数调用次数: {call_count}")
    print(f"所有结果相同: {len(set(results)) == 1}")
    
    # 验证缓存效果
    stats = cache.get_stats()
    print(f"缓存命中: {stats['hits']}, 未命中: {stats['misses']}")
    
    assert call_count == 1, "函数应该只被调用一次"
    assert len(set(results)) == 1, "所有结果应该相同"
    
    print("✓ 并发缓存访问测试通过")


async def test_cache_tags_and_invalidation():
    """测试缓存标签和失效功能"""
    print("\n=== 测试缓存标签和失效功能 ===")
    
    cache = get_cache()
    cache.clear()
    
    # 添加带标签的缓存项
    project_id = 123
    dept_id = 456
    
    cache.set(
        ZentaoCacheKeys.project_stories(project_id),
        {"stories": ["story1", "story2"]},
        ttl=300,
        tags=[ZentaoCacheTags.STORIES, ZentaoCacheTags.project_related(project_id)]
    )
    
    cache.set(
        ZentaoCacheKeys.project_tasks(project_id),
        {"tasks": ["task1", "task2"]},
        ttl=300,
        tags=[ZentaoCacheTags.TASKS, ZentaoCacheTags.project_related(project_id)]
    )
    
    cache.set(
        ZentaoCacheKeys.department_users(dept_id),
        {"users": ["user1", "user2"]},
        ttl=300,
        tags=[ZentaoCacheTags.USERS, ZentaoCacheTags.department_related(dept_id)]
    )
    
    print(f"添加了3个带标签的缓存项")
    
    # 验证缓存项存在
    assert cache.get(ZentaoCacheKeys.project_stories(project_id)) is not None
    assert cache.get(ZentaoCacheKeys.project_tasks(project_id)) is not None
    assert cache.get(ZentaoCacheKeys.department_users(dept_id)) is not None
    
    # 根据项目标签删除缓存
    project_tag = ZentaoCacheTags.project_related(project_id)
    deleted_count = cache.delete_by_tag(project_tag)
    print(f"根据项目标签删除了 {deleted_count} 个缓存项")
    
    # 验证项目相关缓存被删除，部门缓存仍存在
    assert cache.get(ZentaoCacheKeys.project_stories(project_id)) is None
    assert cache.get(ZentaoCacheKeys.project_tasks(project_id)) is None
    assert cache.get(ZentaoCacheKeys.department_users(dept_id)) is not None
    
    print("✓ 缓存标签和失效功能测试通过")


async def test_cache_memory_management():
    """测试缓存内存管理"""
    print("\n=== 测试缓存内存管理 ===")
    
    # 创建一个小容量的缓存用于测试
    from zentao_mcp.cache import MemoryCache, CacheStrategy
    
    test_cache = MemoryCache(
        max_size=10,  # 最大10个项目
        default_ttl=300,
        strategy=CacheStrategy.LRU,
        max_memory_mb=1  # 最大1MB内存
    )
    
    # 添加超过容量限制的项目
    for i in range(15):
        large_data = "x" * 1000  # 1KB数据
        test_cache.set(f"key_{i}", large_data, ttl=300)
    
    stats = test_cache.get_stats()
    print(f"缓存大小: {stats['size']}/{stats['max_size']}")
    print(f"内存使用: {stats['memory_usage_mb']}MB")
    print(f"淘汰次数: {stats['evictions']}")
    
    # 验证LRU策略
    assert stats['size'] <= 10, "缓存大小不应超过限制"
    assert stats['evictions'] > 0, "应该有淘汰发生"
    
    # 验证最近访问的项目仍在缓存中
    assert test_cache.get("key_14") is not None, "最新的项目应该在缓存中"
    assert test_cache.get("key_0") is None, "最旧的项目应该被淘汰"
    
    print("✓ 缓存内存管理测试通过")


async def test_cache_key_generation():
    """测试缓存键生成"""
    print("\n=== 测试缓存键生成 ===")
    
    # 测试基本键生成
    key1 = ZentaoCacheKeys.departments()
    key2 = ZentaoCacheKeys.project_stories(123)
    key3 = ZentaoCacheKeys.bugs_by_time_range("2024-01-01", "2024-01-31")
    
    print(f"部门键: {key1}")
    print(f"项目需求键: {key2}")
    print(f"时间范围Bug键: {key3}")
    
    # 验证键的唯一性
    assert key1 != key2 != key3, "不同类型的键应该不同"
    
    # 测试哈希键生成
    long_key = "very_long_key_" * 50
    hash_key = cache_key_hash("test", long_key, {"param": "value"})
    print(f"哈希键长度: {len(hash_key)}")
    
    assert len(hash_key) == 32, "MD5哈希应该是32字符"
    
    print("✓ 缓存键生成测试通过")


async def test_cache_cleanup_automation():
    """测试缓存自动清理"""
    print("\n=== 测试缓存自动清理 ===")
    
    # 创建一个新的缓存实例用于测试
    from zentao_mcp.cache import MemoryCache, CacheStrategy
    
    test_cache = MemoryCache(
        max_size=100,  # 足够大的容量
        default_ttl=300,
        strategy=CacheStrategy.LRU
    )
    
    # 添加一些短期缓存项
    for i in range(5):
        test_cache.set(f"short_key_{i}", f"value_{i}", ttl=1)  # 1秒过期
    
    # 添加一些长期缓存项
    for i in range(5):
        test_cache.set(f"long_key_{i}", f"value_{i}", ttl=300)  # 5分钟过期
    
    print(f"添加了10个缓存项")
    initial_stats = test_cache.get_stats()
    print(f"初始缓存大小: {initial_stats['size']}")
    
    # 等待短期缓存过期
    await asyncio.sleep(1.5)
    
    # 手动触发清理
    cleaned = test_cache.cleanup_expired()
    print(f"清理了 {cleaned} 个过期项")
    
    final_stats = test_cache.get_stats()
    print(f"清理后缓存大小: {final_stats['size']}")
    print(f"过期清理次数: {final_stats['expired_cleanups']}")
    
    # 验证长期缓存项仍然存在
    long_items_exist = sum(1 for i in range(5) if test_cache.get(f"long_key_{i}") is not None)
    short_items_exist = sum(1 for i in range(5) if test_cache.get(f"short_key_{i}") is not None)
    
    print(f"长期缓存项存在: {long_items_exist}/5")
    print(f"短期缓存项存在: {short_items_exist}/5")
    
    assert long_items_exist == 5, "所有长期缓存项应该存在"
    assert short_items_exist == 0, "所有短期缓存项应该被清理"
    assert cleaned == 5, "应该清理5个过期项"
    
    print("✓ 缓存自动清理测试通过")


async def main():
    """主测试函数"""
    print("开始性能和并发测试...\n")
    
    try:
        await test_cache_performance()
        await test_concurrent_cache_access()
        await test_cache_tags_and_invalidation()
        await test_cache_memory_management()
        await test_cache_key_generation()
        await test_cache_cleanup_automation()
        
        print("\n🎉 所有性能和并发测试通过！")
        
        # 显示最终统计
        final_stats = get_cache_stats()
        print(f"\n最终缓存统计:")
        for key, value in final_stats.items():
            print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)