#!/usr/bin/env python3
"""
客户端缓存集成测试

测试ZentaoClient与缓存系统的集成
"""

import asyncio
import sys
import os
import time
from unittest.mock import AsyncMock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from zentao_mcp.client import ZentaoClient
from zentao_mcp.cache import get_cache, get_cache_stats
from zentao_mcp.config import settings


async def test_client_cache_integration():
    """测试客户端缓存集成"""
    print("=== 测试客户端缓存集成 ===")
    
    # 清空缓存
    cache = get_cache()
    cache.clear()
    
    # 创建客户端实例
    client = ZentaoClient()
    
    # Mock HTTP响应
    mock_response_data = {
        "rsCode": "00000000",
        "msg": "正常返回",
        "body": [
            {"id": 1, "name": "部门1", "parent": 0, "path": "/部门1/", "grade": 1},
            {"id": 2, "name": "部门2", "parent": 1, "path": "/部门1/部门2/", "grade": 2}
        ]
    }
    
    # 模拟API调用
    with patch.object(client, '_make_request', new_callable=AsyncMock) as mock_request:
        mock_request.return_value = mock_response_data
        
        # 第一次调用 - 应该调用API
        start_time = time.time()
        result1 = await client.get_all_departments_async(parse_model=False)
        first_call_time = time.time() - start_time
        
        # 第二次调用 - 应该从缓存获取
        start_time = time.time()
        result2 = await client.get_all_departments_async(parse_model=False)
        second_call_time = time.time() - start_time
        
        # 验证结果
        assert result1 == result2, "两次调用结果应该相同"
        assert mock_request.call_count == 1, "API应该只被调用一次"
        assert second_call_time < first_call_time, "第二次调用应该更快（从缓存获取）"
        
        print(f"第一次调用耗时: {first_call_time:.4f}s")
        print(f"第二次调用耗时: {second_call_time:.4f}s")
        print(f"API调用次数: {mock_request.call_count}")
        
        # 检查缓存统计
        stats = get_cache_stats()
        print(f"缓存命中: {stats['hits']}, 未命中: {stats['misses']}")
        assert stats['hits'] >= 1, "应该有缓存命中"
    
    await client.close()
    print("✓ 客户端缓存集成测试通过")


async def test_client_batch_requests():
    """测试客户端批量请求"""
    print("\n=== 测试客户端批量请求 ===")
    
    client = ZentaoClient()
    
    # 准备批量请求
    requests = [
        {"method": "GET", "endpoint": "apiData/getAllDept"},
        {"method": "GET", "endpoint": "apiData/getAllProject"},
        {"method": "POST", "endpoint": "apiData/getBugListByTimeRange", "data": {"startDate": "2024-01-01", "endDate": "2024-01-31"}}
    ]
    
    # Mock HTTP响应
    mock_responses = [
        {"rsCode": "00000000", "msg": "部门数据", "body": []},
        {"rsCode": "00000000", "msg": "项目数据", "body": []},
        {"rsCode": "00000000", "msg": "Bug数据", "body": []}
    ]
    
    with patch.object(client, '_make_request', new_callable=AsyncMock) as mock_request:
        mock_request.side_effect = mock_responses
        
        start_time = time.time()
        results = await client.batch_request(requests)
        batch_time = time.time() - start_time
        
        assert len(results) == 3, "应该返回3个结果"
        assert mock_request.call_count == 3, "应该调用3次API"
        
        print(f"批量请求耗时: {batch_time:.4f}s")
        print(f"API调用次数: {mock_request.call_count}")
        print(f"成功请求数: {len([r for r in results if 'error' not in r])}")
    
    await client.close()
    print("✓ 客户端批量请求测试通过")


async def test_client_performance_stats():
    """测试客户端性能统计"""
    print("\n=== 测试客户端性能统计 ===")
    
    client = ZentaoClient()
    
    # 获取性能统计
    stats = client.get_performance_stats()
    
    print("客户端性能统计:")
    for key, value in stats.items():
        if key == 'cache_stats':
            print(f"  {key}:")
            for cache_key, cache_value in value.items():
                print(f"    {cache_key}: {cache_value}")
        else:
            print(f"  {key}: {value}")
    
    # 验证统计信息
    assert 'total_requests' in stats, "应该包含总请求数"
    assert 'cache_stats' in stats, "应该包含缓存统计"
    assert 'concurrent_limit' in stats, "应该包含并发限制"
    
    await client.close()
    print("✓ 客户端性能统计测试通过")


async def test_cache_invalidation():
    """测试缓存失效"""
    print("\n=== 测试缓存失效 ===")
    
    cache = get_cache()
    cache.clear()
    
    client = ZentaoClient()
    
    # 模拟项目数据
    project_id = 123
    mock_stories = {"rsCode": "00000000", "body": [{"id": 1, "title": "需求1"}]}
    mock_tasks = {"rsCode": "00000000", "body": [{"id": 1, "name": "任务1"}]}
    
    with patch.object(client, '_make_request', new_callable=AsyncMock) as mock_request:
        mock_request.side_effect = [mock_stories, mock_tasks]
        
        # 调用项目相关API，填充缓存
        await client.get_stories_by_project_async(project_id)
        await client.get_tasks_by_project_async(project_id)
        
        # 验证缓存中有数据
        initial_stats = get_cache_stats()
        print(f"缓存填充后大小: {initial_stats['size']}")
        assert initial_stats['size'] > 0, "缓存应该有数据"
        
        # 清理项目相关缓存
        from zentao_mcp.cache import invalidate_project_cache
        cleared_count = invalidate_project_cache(project_id)
        
        # 验证缓存被清理
        final_stats = get_cache_stats()
        print(f"缓存清理后大小: {final_stats['size']}")
        print(f"清理的缓存项数: {cleared_count}")
        
        assert cleared_count > 0, "应该清理了一些缓存项"
    
    await client.close()
    print("✓ 缓存失效测试通过")


async def main():
    """主测试函数"""
    print("开始客户端缓存集成测试...\n")
    
    try:
        await test_client_cache_integration()
        await test_client_batch_requests()
        await test_client_performance_stats()
        await test_cache_invalidation()
        
        print("\n🎉 所有客户端缓存集成测试通过！")
        
        # 显示最终缓存统计
        final_stats = get_cache_stats()
        print(f"\n最终缓存统计:")
        for key, value in final_stats.items():
            print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)