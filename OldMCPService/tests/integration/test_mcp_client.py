#!/usr/bin/env python3
"""
MCP客户端测试脚本
用于测试禅道MCP服务器的功能
"""

import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client


async def test_mcp_server():
    """测试MCP服务器功能"""
    print("🧪 开始测试禅道MCP服务器...")
    
    # 创建服务器参数
    server_params = StdioServerParameters(
        command="uv",
        args=["run", "python", "-m", "zentao_mcp.main"],
        env=None
    )
    
    try:
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                print("✅ 成功连接到MCP服务器")
                
                # 初始化会话
                await session.initialize()
                print("✅ 会话初始化完成")
                
                # 测试1: 列出所有工具
                print("\n📋 测试1: 列出所有工具")
                tools = await session.list_tools()
                print(f"发现 {len(tools.tools)} 个工具:")
                for tool in tools.tools:
                    print(f"  - {tool.name}: {tool.description}")
                
                # 测试2: 列出所有资源模板
                print("\n📋 测试2: 列出所有资源模板")
                resources = await session.list_resource_templates()
                print(f"发现 {len(resources.resourceTemplates)} 个资源模板:")
                for resource in resources.resourceTemplates:
                    print(f"  - {resource.uriTemplate}: {resource.description}")
                
                # 测试3: 调用一个简单的工具（如果有的话）
                if tools.tools:
                    print("\n🔧 测试3: 调用工具")
                    first_tool = tools.tools[0]
                    print(f"尝试调用工具: {first_tool.name}")
                    
                    try:
                        # 这里需要根据具体工具的参数来调用
                        # 暂时跳过，因为需要具体的参数
                        print("⚠️  跳过工具调用测试（需要具体参数）")
                    except Exception as e:
                        print(f"❌ 工具调用失败: {e}")
                
                # 测试4: 读取一个资源（如果有的话）
                if resources.resourceTemplates:
                    print("\n📖 测试4: 读取资源")
                    first_resource = resources.resourceTemplates[0]
                    print(f"尝试读取资源模板: {first_resource.uriTemplate}")
                    
                    try:
                        # 这里需要根据具体资源的URI模板来读取
                        # 暂时跳过，因为需要具体的URI参数
                        print("⚠️  跳过资源读取测试（需要具体URI参数）")
                    except Exception as e:
                        print(f"❌ 资源读取失败: {e}")
                
                print("\n✅ MCP服务器测试完成")
                
    except Exception as e:
        print(f"❌ MCP服务器测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_specific_functionality():
    """测试特定功能"""
    print("\n🎯 测试特定功能...")
    
    server_params = StdioServerParameters(
        command="uv",
        args=["run", "python", "-m", "zentao_mcp.main"],
        env=None
    )
    
    try:
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                
                # 测试获取部门列表资源
                print("\n📋 测试获取部门列表...")
                try:
                    result = await session.read_resource("zentao://departments/all")
                    print("✅ 成功获取部门列表资源")
                    print(f"资源内容: {result}")
                except Exception as e:
                    print(f"❌ 获取部门列表失败: {e}")
                
                # 测试获取项目列表资源
                print("\n📋 测试获取项目列表...")
                try:
                    result = await session.read_resource("zentao://projects/all")
                    print("✅ 成功获取项目列表资源")
                    print(f"资源内容: {result}")
                except Exception as e:
                    print(f"❌ 获取项目列表失败: {e}")
                
    except Exception as e:
        print(f"❌ 特定功能测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🚀 禅道MCP客户端测试工具")
    
    if len(sys.argv) > 1 and sys.argv[1] == "specific":
        # 测试特定功能
        asyncio.run(test_specific_functionality())
    else:
        # 基础测试
        asyncio.run(test_mcp_server())


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        import traceback
        traceback.print_exc()