"""
集成测试模块

测试MCP服务器的端到端功能，包括与真实禅道API的集成
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from fastmcp import Context

from zentao_mcp.main import ZentaoMCPServer
from zentao_mcp.client import ZentaoClient
from zentao_mcp.exceptions import ZentaoAPIError


class TestMCPServerIntegration:
    """MCP服务器集成测试"""
    
    @pytest.fixture
    def mock_zentao_client(self):
        """模拟禅道客户端"""
        client = AsyncMock(spec=ZentaoClient)
        
        # 模拟部门数据
        client.get_all_departments_async.return_value = {
            "rsCode": "00000000",
            "msg": "正常返回",
            "body": [
                {"id": 1, "name": "开发部门", "parent": 0},
                {"id": 2, "name": "测试部门", "parent": 0}
            ]
        }
        
        # 模拟项目数据
        client.get_all_projects_async.return_value = {
            "rsCode": "00000000",
            "msg": "正常返回",
            "body": [
                {"id": 143, "name": "测试项目", "status": "doing"},
                {"id": 144, "name": "演示项目", "status": "done"}
            ]
        }
        
        return client
    
    @pytest.mark.asyncio
    async def test_server_creation(self):
        """测试服务器创建"""
        server = ZentaoMCPServer("test-server")
        assert server is not None
        assert hasattr(server, '_client_context')
        assert server.name == "test-server"
    
    @pytest.mark.asyncio
    async def test_department_tools_integration(self, mock_zentao_client):
        """测试部门工具集成"""
        server = ZentaoMCPServer("test-server")
        
        # 模拟客户端上下文
        async def mock_client_context():
            yield mock_zentao_client
        
        with patch.object(server, '_client_context', return_value=mock_client_context()):
            # 这里可以测试具体的工具调用
            # 由于工具注册在服务器创建时完成，我们主要验证集成正常
            assert mock_zentao_client is not None


class TestEndToEndWorkflow:
    """端到端工作流测试"""
    
    @pytest.fixture
    def sample_workflow_data(self):
        """示例工作流数据"""
        return {
            "project_id": 143,
            "story_ids": ["28673", "28674"],
            "task_ids": [371999, 372000],
            "bug_ids": [184213, 184214],
            "dept_id": 41,
            "time_range": {
                "start_date": "2025-01-01",
                "end_date": "2025-01-31"
            }
        }
    
    @pytest.mark.asyncio
    async def test_project_analysis_workflow(self, sample_workflow_data):
        """测试项目分析工作流"""
        # 模拟完整的项目分析流程
        project_id = sample_workflow_data["project_id"]
        
        # 1. 获取项目基本信息
        project_info = {
            "id": project_id,
            "name": "测试项目",
            "status": "doing"
        }
        
        # 2. 获取项目需求
        stories_data = [
            {
                "id": 28673,
                "title": "用户登录功能",
                "status": "active",
                "estimate": 16.0
            },
            {
                "id": 28674,
                "title": "用户注册功能", 
                "status": "closed",
                "estimate": 12.0
            }
        ]
        
        # 3. 获取项目任务
        tasks_data = [
            {
                "id": 371999,
                "name": "实现登录接口",
                "status": "doing",
                "estimate": 8.0,
                "consumed": 6.0,
                "left": 2.0
            },
            {
                "id": 372000,
                "name": "登录页面开发",
                "status": "done",
                "estimate": 8.0,
                "consumed": 8.0,
                "left": 0.0
            }
        ]
        
        # 4. 获取项目Bug
        bugs_data = [
            {
                "id": 184213,
                "title": "登录页面异常",
                "status": "active",
                "severity": 1
            }
        ]
        
        # 5. 执行项目分析
        from zentao_mcp.tools.analysis.project import _analyze_project_data
        
        analysis_result = _analyze_project_data(
            project_info, stories_data, tasks_data, bugs_data
        )
        
        # 验证分析结果
        assert analysis_result["overview"]["total_stories"] == 2
        assert analysis_result["overview"]["total_tasks"] == 2
        assert analysis_result["overview"]["total_bugs"] == 1
        assert analysis_result["workload_summary"]["estimated_hours"] == 44.0
        assert analysis_result["workload_summary"]["consumed_hours"] == 14.0
        assert analysis_result["workload_summary"]["remaining_hours"] == 2.0
    
    @pytest.mark.asyncio
    async def test_bug_analysis_workflow(self, sample_workflow_data):
        """测试Bug分析工作流"""
        # 模拟Bug分析流程
        bugs_data = [
            {
                "id": 184213,
                "title": "登录页面异常",
                "status": "active",
                "severity": 1,
                "projectname": "测试项目",
                "assignedTo": "developer1"
            },
            {
                "id": 184214,
                "title": "注册功能错误",
                "status": "resolved",
                "severity": 2,
                "projectname": "测试项目",
                "assignedTo": "developer2"
            }
        ]
        
        # 执行Bug统计分析
        status_stats = {}
        severity_stats = {}
        
        for bug in bugs_data:
            status = bug.get("status", "active")
            severity = bug.get("severity", 0)
            
            status_stats[status] = status_stats.get(status, 0) + 1
            severity_stats[severity] = severity_stats.get(severity, 0) + 1
        
        # 验证分析结果
        assert status_stats["active"] == 1
        assert status_stats["resolved"] == 1
        assert severity_stats[1] == 1  # 严重级别
        assert severity_stats[2] == 1  # 一般级别
    
    @pytest.mark.asyncio
    async def test_story_workload_workflow(self, sample_workflow_data):
        """测试需求工时分析工作流"""
        story_ids = sample_workflow_data["story_ids"]
        
        # 模拟需求工时数据
        stories_data = [
            {
                "id": 28673,
                "title": "用户登录功能",
                "status": "active",
                "stage": "developing",
                "estimate": 16.0,
                "pri": 1
            },
            {
                "id": 28674,
                "title": "用户注册功能",
                "status": "closed", 
                "stage": "released",
                "estimate": 12.0,
                "pri": 2
            }
        ]
        
        # 执行工时统计
        total_estimate = sum(story.get("estimate", 0.0) for story in stories_data)
        completed_estimate = sum(
            story.get("estimate", 0.0) 
            for story in stories_data 
            if story.get("status") == "closed"
        )
        active_estimate = sum(
            story.get("estimate", 0.0)
            for story in stories_data
            if story.get("status") == "active"
        )
        
        completion_rate = (completed_estimate / total_estimate * 100) if total_estimate > 0 else 0
        
        # 验证工时分析结果
        assert total_estimate == 28.0
        assert completed_estimate == 12.0
        assert active_estimate == 16.0
        assert round(completion_rate, 2) == 42.86


class TestErrorHandlingIntegration:
    """错误处理集成测试"""
    
    @pytest.mark.asyncio
    async def test_api_error_handling(self):
        """测试API错误处理"""
        client = AsyncMock(spec=ZentaoClient)
        
        # 模拟API错误
        client.get_all_departments_async.side_effect = Exception("网络连接失败")
        
        # 测试错误处理
        try:
            await client.get_all_departments_async()
            assert False, "应该抛出异常"
        except Exception as e:
            assert "网络连接失败" in str(e)
    
    @pytest.mark.asyncio
    async def test_invalid_response_handling(self):
        """测试无效响应处理"""
        client = AsyncMock(spec=ZentaoClient)
        
        # 模拟无效响应
        client.get_all_departments_async.return_value = {
            "rsCode": "99999999",
            "msg": "系统错误",
            "body": None
        }
        
        response = await client.get_all_departments_async()
        
        # 验证错误响应处理
        assert response["rsCode"] != "00000000"
        assert response["body"] is None
    
    @pytest.mark.asyncio
    async def test_timeout_handling(self):
        """测试超时处理"""
        client = AsyncMock(spec=ZentaoClient)
        
        # 模拟超时
        client.get_all_departments_async.side_effect = asyncio.TimeoutError("请求超时")
        
        try:
            await client.get_all_departments_async()
            assert False, "应该抛出超时异常"
        except asyncio.TimeoutError as e:
            assert "请求超时" in str(e)


class TestPerformanceIntegration:
    """性能集成测试"""
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self):
        """测试并发请求处理"""
        client = AsyncMock(spec=ZentaoClient)
        
        # 模拟并发响应
        client.get_all_departments_async.return_value = {
            "rsCode": "00000000",
            "msg": "正常返回",
            "body": [{"id": 1, "name": "测试部门"}]
        }
        
        # 创建多个并发任务
        tasks = []
        for i in range(10):
            task = asyncio.create_task(client.get_all_departments_async())
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks)
        
        # 验证所有请求都成功
        assert len(results) == 10
        for result in results:
            assert result["rsCode"] == "00000000"
    
    @pytest.mark.asyncio
    async def test_large_data_processing(self):
        """测试大数据处理"""
        # 生成大量测试数据
        large_dataset = []
        for i in range(1000):
            large_dataset.append({
                "id": i,
                "title": f"需求{i}",
                "status": "active" if i % 2 == 0 else "closed",
                "estimate": float(i % 20 + 1)
            })
        
        # 测试数据处理性能
        import time
        start_time = time.time()
        
        # 执行数据统计
        total_estimate = sum(item.get("estimate", 0.0) for item in large_dataset)
        status_stats = {}
        
        for item in large_dataset:
            status = item.get("status", "active")
            status_stats[status] = status_stats.get(status, 0) + 1
        
        processing_time = time.time() - start_time
        
        # 验证处理结果和性能
        assert len(large_dataset) == 1000
        assert total_estimate > 0
        assert len(status_stats) == 2
        assert processing_time < 1.0  # 处理时间应该小于1秒


class TestDataConsistencyIntegration:
    """数据一致性集成测试"""
    
    @pytest.mark.asyncio
    async def test_story_task_consistency(self):
        """测试需求-任务数据一致性"""
        # 模拟需求数据
        stories_data = [
            {"id": 1, "title": "用户登录功能", "status": "active"},
            {"id": 2, "title": "用户注册功能", "status": "closed"}
        ]
        
        # 模拟任务数据
        tasks_data = [
            {"id": 101, "name": "实现登录接口", "story": 1, "status": "doing"},
            {"id": 102, "name": "登录页面开发", "story": 1, "status": "done"},
            {"id": 103, "name": "注册接口开发", "story": 2, "status": "done"},
            {"id": 104, "name": "孤立任务", "story": 999, "status": "doing"}  # 孤立任务
        ]
        
        # 检查数据一致性
        story_ids = {story["id"] for story in stories_data}
        orphaned_tasks = []
        
        for task in tasks_data:
            story_id = task.get("story", 0)
            if story_id not in story_ids and story_id != 0:
                orphaned_tasks.append(task)
        
        # 验证一致性检查结果
        assert len(orphaned_tasks) == 1
        assert orphaned_tasks[0]["id"] == 104
        assert orphaned_tasks[0]["story"] == 999
    
    @pytest.mark.asyncio
    async def test_project_data_consistency(self):
        """测试项目数据一致性"""
        # 模拟项目数据
        project_data = {
            "id": 143,
            "name": "测试项目",
            "status": "doing"
        }
        
        # 模拟项目相关数据
        project_stories = [
            {"id": 1, "project": 143, "title": "功能A"},
            {"id": 2, "project": 143, "title": "功能B"}
        ]
        
        project_tasks = [
            {"id": 101, "project": 143, "name": "任务A"},
            {"id": 102, "project": 143, "name": "任务B"}
        ]
        
        project_bugs = [
            {"id": 201, "project": 143, "title": "Bug A"},
            {"id": 202, "project": 143, "title": "Bug B"}
        ]
        
        # 验证项目关联数据一致性
        project_id = project_data["id"]
        
        assert all(story["project"] == project_id for story in project_stories)
        assert all(task["project"] == project_id for task in project_tasks)
        assert all(bug["project"] == project_id for bug in project_bugs)
        
        # 统计项目数据
        project_summary = {
            "project_info": project_data,
            "stories_count": len(project_stories),
            "tasks_count": len(project_tasks),
            "bugs_count": len(project_bugs)
        }
        
        assert project_summary["stories_count"] == 2
        assert project_summary["tasks_count"] == 2
        assert project_summary["bugs_count"] == 2