"""
Bug工具测试模块

测试Bug相关的MCP工具功能
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from zentao_mcp.tools.direct.bug import register_bug_direct_tools
from zentao_mcp.tools.analysis.bug import register_bug_analysis_tools, _get_severity_name
from zentao_mcp.exceptions import ZentaoAPIError, ValidationError


class TestBugTools:
    """Bug工具测试类"""
    
    @pytest.fixture
    def mock_mcp(self):
        """创建模拟的MCP实例"""
        mcp = MagicMock()
        mcp.tool = MagicMock()
        return mcp
    
    @pytest.fixture
    def mock_context_and_client(self):
        """创建模拟的上下文和客户端"""
        context = MagicMock()
        server = MagicMock()
        client = AsyncMock()
        
        # 设置客户端上下文管理器
        async def client_context():
            yield client
        
        server._client_context = MagicMock(return_value=client_context())
        context.server = server
        return context, client
    
    @pytest.fixture
    def sample_bug_data(self):
        """示例Bug数据"""
        return {
            "id": 123,
            "title": "测试Bug标题",
            "status": "active",
            "severity": 2,
            "project": 456,
            "projectname": "测试项目",
            "assignedTo": "testuser",
            "assignedToEmpid": "EMP001",
            "openedBy": "reporter",
            "openedByEmpid": "EMP002",
            "openedDate": "2025-01-15 10:30:00",
            "resolution": "",
            "environment": "Beta",
            "steps": "1. 打开页面\n2. 点击按钮\n3. 观察错误"
        }
    
    def test_register_bug_tools(self, mock_mcp):
        """测试Bug工具注册"""
        register_bug_direct_tools(mock_mcp)
        register_bug_analysis_tools(mock_mcp)
        
        # 验证工具注册调用
        assert mock_mcp.tool.call_count >= 3  # 至少注册了3个工具
    
    @pytest.mark.asyncio
    @patch('zentao_mcp.tools.client_utils.get_zentao_client')
    async def test_query_bugs_by_time_range_success(self, mock_get_client, sample_bug_data):
        """测试根据时间范围查询Bug成功"""
        # 创建mock客户端
        mock_client = AsyncMock()
        
        # 模拟API响应
        api_response = {
            "rsCode": "00000000",
            "msg": "正常返回",
            "body": [sample_bug_data]
        }
        mock_client.get_bugs_by_time_range_async.return_value = api_response
        
        # 设置mock上下文管理器
        mock_get_client.return_value.__aenter__.return_value = mock_client
        
        # 导入并调用实际的工具函数
        from zentao_mcp.tools.legacy.bug import register_bug_tools
        
        # 创建一个临时的MCP实例来获取工具函数
        temp_mcp = MagicMock()
        tool_functions = {}
        
        def mock_tool_decorator():
            def decorator(func):
                tool_functions[func.__name__] = func
                return func
            return decorator
        
        temp_mcp.tool = mock_tool_decorator
        register_bug_tools(temp_mcp)
        
        # 调用查询函数
        query_func = tool_functions['query_bugs_by_time_range']
        result = await query_func("2025-01-01", "2025-01-31")
        
        # 验证结果
        assert result["success"] is True
        assert result["total_bugs"] == 1
        assert len(result["bugs"]) == 1
        assert result["bugs"][0]["id"] == 123
        assert result["bugs"][0]["title"] == "测试Bug标题"
        assert result["start_date"] == "2025-01-01"
        assert result["end_date"] == "2025-01-31"
        
        # 验证API调用
        mock_client.get_bugs_by_time_range_async.assert_called_once_with("2025-01-01", "2025-01-31", parse_model=False)
    
    @pytest.mark.asyncio
    async def test_query_bugs_by_time_range_invalid_date(self, mock_context_and_client):
        """测试时间范围查询Bug - 无效日期格式"""
        context, client = mock_context_and_client
        
        # 直接测试函数逻辑
        async def test_query_bugs_by_time_range(start_date: str, end_date: str, ctx):
            try:
                start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                end_dt = datetime.strptime(end_date, "%Y-%m-%d")
                
                if start_dt > end_dt:
                    raise ValidationError("开始日期不能晚于结束日期")
                    
            except ValueError as e:
                raise ValidationError(f"日期格式错误，应为YYYY-MM-DD格式: {str(e)}")
            
            return {"success": True}
        
        # 调用函数并验证异常
        with pytest.raises(ValidationError):
            await test_query_bugs_by_time_range("invalid-date", "2025-01-31", context)
    
    @pytest.mark.asyncio
    async def test_query_bugs_by_time_range_start_after_end(self, mock_context_and_client):
        """测试时间范围查询Bug - 开始日期晚于结束日期"""
        context, client = mock_context_and_client
        
        # 直接测试函数逻辑
        async def test_query_bugs_by_time_range(start_date: str, end_date: str, ctx):
            try:
                start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                end_dt = datetime.strptime(end_date, "%Y-%m-%d")
                
                if start_dt > end_dt:
                    raise ValidationError("开始日期不能晚于结束日期")
                    
            except ValueError as e:
                raise ValidationError(f"日期格式错误，应为YYYY-MM-DD格式: {str(e)}")
            
            return {"success": True}
        
        # 调用函数并验证异常
        with pytest.raises(ValidationError):
            await test_query_bugs_by_time_range("2025-01-31", "2025-01-01", context)
    
    @pytest.mark.asyncio
    @patch('zentao_mcp.tools.client_utils.get_zentao_client')
    async def test_analyze_bugs_by_dept_and_time_success(self, mock_get_client, sample_bug_data):
        """测试按部门和时间段统计Bug成功"""
        # 创建mock客户端
        mock_client = AsyncMock()
        
        # 模拟API响应
        bugs_response = {
            "rsCode": "00000000",
            "msg": "正常返回",
            "body": [sample_bug_data]
        }
        
        dept_response = {
            "rsCode": "00000000",
            "msg": "正常返回",
            "body": [
                {"id": 10, "name": "开发部门"}
            ]
        }
        
        mock_client.get_bugs_by_time_range_and_dept_async.return_value = bugs_response
        mock_client.get_all_departments_async.return_value = dept_response
        
        # 设置mock上下文管理器
        mock_get_client.return_value.__aenter__.return_value = mock_client
        
        # 导入并调用实际的工具函数
        from zentao_mcp.tools.legacy.bug import register_bug_tools
        
        # 创建一个临时的MCP实例来获取工具函数
        temp_mcp = MagicMock()
        tool_functions = {}
        
        def mock_tool_decorator():
            def decorator(func):
                tool_functions[func.__name__] = func
                return func
            return decorator
        
        temp_mcp.tool = mock_tool_decorator
        register_bug_tools(temp_mcp)
        
        # 调用分析函数
        analyze_func = tool_functions['analyze_bugs_by_dept_and_time']
        result = await analyze_func("2025-01-01", "2025-01-31", 10)
        
        # 验证结果
        assert result["success"] is True
        assert result["dept_id"] == 10
        assert result["dept_name"] == "开发部门"
        assert result["analysis"]["summary"]["total_bugs"] == 1
        assert len(result["bugs"]) == 1
        
        # 验证API调用
        mock_client.get_bugs_by_time_range_and_dept_async.assert_called_once_with("2025-01-01", "2025-01-31", 10)
        mock_client.get_all_departments_async.assert_called_once_with(parse_model=False)
    
    def test_get_severity_name(self):
        """测试严重程度名称获取"""
        assert _get_severity_name(0) == "冒烟"
        assert _get_severity_name(1) == "严重"
        assert _get_severity_name(2) == "一般"
        assert _get_severity_name(3) == "次要"
        assert _get_severity_name(4) == "低级"
        assert _get_severity_name(99) == "未知"
    
    @pytest.mark.asyncio
    async def test_filter_bugs_by_criteria_with_status_filter(self):
        """测试根据状态过滤Bug"""
        # 准备测试数据
        bugs_data = [
            {"id": 1, "status": "active", "severity": 2, "projectname": "项目A", "assignedTo": "user1"},
            {"id": 2, "status": "resolved", "severity": 1, "projectname": "项目B", "assignedTo": "user2"},
            {"id": 3, "status": "closed", "severity": 3, "projectname": "项目A", "assignedTo": "user1"},
            {"id": 4, "status": "active", "severity": 2, "projectname": "项目C", "assignedTo": "user3"}
        ]
        
        # 直接测试过滤逻辑
        status_filter = ["active"]
        filtered_bugs = []
        
        for bug in bugs_data:
            if status_filter and bug.get("status") not in status_filter:
                continue
            filtered_bugs.append(bug)
        
        # 验证结果
        assert len(filtered_bugs) == 2
        assert all(bug["status"] == "active" for bug in filtered_bugs)
    
    @pytest.mark.asyncio
    async def test_filter_bugs_by_criteria_with_severity_filter(self):
        """测试根据严重程度过滤Bug"""
        # 准备测试数据
        bugs_data = [
            {"id": 1, "status": "active", "severity": 2, "projectname": "项目A", "assignedTo": "user1"},
            {"id": 2, "status": "resolved", "severity": 1, "projectname": "项目B", "assignedTo": "user2"},
            {"id": 3, "status": "closed", "severity": 3, "projectname": "项目A", "assignedTo": "user1"},
            {"id": 4, "status": "active", "severity": 1, "projectname": "项目C", "assignedTo": "user3"}
        ]
        
        # 直接测试过滤逻辑
        severity_filter = [1]  # 只要严重级别
        filtered_bugs = []
        
        for bug in bugs_data:
            if severity_filter and bug.get("severity") not in severity_filter:
                continue
            filtered_bugs.append(bug)
        
        # 验证结果
        assert len(filtered_bugs) == 2
        assert all(bug["severity"] == 1 for bug in filtered_bugs)
    
    @pytest.mark.asyncio
    async def test_filter_bugs_by_criteria_with_multiple_filters(self):
        """测试根据多个条件过滤Bug"""
        # 准备测试数据
        bugs_data = [
            {"id": 1, "status": "active", "severity": 2, "projectname": "项目A", "assignedTo": "user1"},
            {"id": 2, "status": "active", "severity": 1, "projectname": "项目B", "assignedTo": "user2"},
            {"id": 3, "status": "closed", "severity": 1, "projectname": "项目A", "assignedTo": "user1"},
            {"id": 4, "status": "active", "severity": 1, "projectname": "项目A", "assignedTo": "user1"}
        ]
        
        # 直接测试过滤逻辑
        status_filter = ["active"]
        severity_filter = [1]
        project_filter = ["项目A"]
        
        filtered_bugs = []
        
        for bug in bugs_data:
            # 状态过滤
            if status_filter and bug.get("status") not in status_filter:
                continue
            
            # 严重程度过滤
            if severity_filter and bug.get("severity") not in severity_filter:
                continue
            
            # 项目过滤
            if project_filter:
                project_name = bug.get("projectname", "")
                if not any(project in project_name for project in project_filter):
                    continue
            
            filtered_bugs.append(bug)
        
        # 验证结果
        assert len(filtered_bugs) == 1
        assert filtered_bugs[0]["id"] == 4
        assert filtered_bugs[0]["status"] == "active"
        assert filtered_bugs[0]["severity"] == 1
        assert "项目A" in filtered_bugs[0]["projectname"]