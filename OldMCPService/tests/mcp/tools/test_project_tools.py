"""
项目工具测试模块
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from zentao_mcp.tools.direct.project import register_project_direct_tools
from zentao_mcp.tools.analysis.project import (
    register_project_analysis_tools,
    _analyze_project_data,
    _analyze_stories,
    _analyze_tasks,
    _analyze_bugs,
    _calculate_project_progress,
    _calculate_project_health
)
from zentao_mcp.tools.project import _generate_health_recommendations


class TestProjectAnalysis:
    """项目分析功能测试"""
    
    def test_analyze_stories_empty(self):
        """测试空需求数据分析"""
        result = _analyze_stories([])
        
        assert result["total_count"] == 0
        assert result["status_distribution"] == {}
        assert result["total_estimate"] == 0.0
    
    def test_analyze_stories_with_data(self):
        """测试有数据的需求分析"""
        stories_data = [
            {
                "id": 1,
                "status": "active",
                "stage": "developing",
                "pri": 1,
                "estimate": 8.0
            },
            {
                "id": 2,
                "status": "closed",
                "stage": "released",
                "pri": 2,
                "estimate": 16.0
            }
        ]
        
        result = _analyze_stories(stories_data)
        
        assert result["total_count"] == 2
        assert result["status_distribution"]["active"] == 1
        assert result["status_distribution"]["closed"] == 1
        assert result["stage_distribution"]["developing"] == 1
        assert result["stage_distribution"]["released"] == 1
        assert result["priority_distribution"][1] == 1
        assert result["priority_distribution"][2] == 1
        assert result["total_estimate"] == 24.0
    
    def test_analyze_tasks_empty(self):
        """测试空任务数据分析"""
        result = _analyze_tasks([])
        
        assert result["total_count"] == 0
        assert result["status_distribution"] == {}
        assert result["total_estimate"] == 0.0
        assert result["total_consumed"] == 0.0
        assert result["total_left"] == 0.0
    
    def test_analyze_tasks_with_data(self):
        """测试有数据的任务分析"""
        tasks_data = [
            {
                "id": 1,
                "status": "doing",
                "type": "devel",
                "estimate": 8.0,
                "consumed": 6.0,
                "left": 2.0
            },
            {
                "id": 2,
                "status": "done",
                "type": "test",
                "estimate": 4.0,
                "consumed": 4.0,
                "left": 0.0
            }
        ]
        
        result = _analyze_tasks(tasks_data)
        
        assert result["total_count"] == 2
        assert result["status_distribution"]["doing"] == 1
        assert result["status_distribution"]["done"] == 1
        assert result["type_distribution"]["devel"] == 1
        assert result["type_distribution"]["test"] == 1
        assert result["total_estimate"] == 12.0
        assert result["total_consumed"] == 10.0
        assert result["total_left"] == 2.0
    
    def test_analyze_bugs_empty(self):
        """测试空Bug数据分析"""
        result = _analyze_bugs([])
        
        assert result["total_count"] == 0
        assert result["status_distribution"] == {}
    
    def test_analyze_bugs_with_data(self):
        """测试有数据的Bug分析"""
        bugs_data = [
            {
                "id": 1,
                "status": "active",
                "severity": 1,
                "pri": 1
            },
            {
                "id": 2,
                "status": "resolved",
                "severity": 2,
                "pri": 2
            }
        ]
        
        result = _analyze_bugs(bugs_data)
        
        assert result["total_count"] == 2
        assert result["status_distribution"]["active"] == 1
        assert result["status_distribution"]["resolved"] == 1
        assert result["severity_distribution"][1] == 1
        assert result["severity_distribution"][2] == 1
        assert result["priority_distribution"][1] == 1
        assert result["priority_distribution"][2] == 1
    
    def test_calculate_project_progress(self):
        """测试项目进度计算"""
        story_stats = {
            "total_count": 10,
            "status_distribution": {"closed": 6, "active": 4}
        }
        
        task_stats = {
            "total_count": 20,
            "status_distribution": {"done": 12, "doing": 8},
            "total_estimate": 100.0,
            "total_consumed": 80.0
        }
        
        result = _calculate_project_progress(story_stats, task_stats)
        
        assert result["story_progress"] == 60.0  # 6/10 * 100
        assert result["task_progress"] == 60.0   # 12/20 * 100
        assert result["workload_progress"] == 80.0  # 80/100 * 100
        # 综合进度: 60*0.4 + 60*0.4 + 80*0.2 = 64.0
        assert result["overall_progress"] == 64.0
    
    def test_calculate_project_health_excellent(self):
        """测试优秀项目健康度计算"""
        story_stats = {"total_count": 10}
        task_stats = {
            "total_count": 20,
            "total_estimate": 100.0,
            "total_consumed": 90.0,
            "status_distribution": {"done": 18, "doing": 2}
        }
        bug_stats = {"total_count": 1}  # Bug率 1/10 = 10%
        progress = {"overall_progress": 90.0}
        
        result = _calculate_project_health(story_stats, task_stats, bug_stats, progress)
        
        assert result["level"] == "优秀"
        assert result["score"] >= 80
        assert "factors" in result
    
    def test_calculate_project_health_poor(self):
        """测试较差项目健康度计算"""
        story_stats = {"total_count": 10}
        task_stats = {
            "total_count": 20,
            "total_estimate": 100.0,
            "total_consumed": 200.0,  # 超时100%
            "status_distribution": {"done": 6, "doing": 14}  # 大量进行中任务
        }
        bug_stats = {"total_count": 15}  # Bug率 15/10 = 150%
        progress = {"overall_progress": 30.0}  # 进度较慢
        
        result = _calculate_project_health(story_stats, task_stats, bug_stats, progress)
        
        assert result["level"] in ["需要关注", "一般"]
        assert result["score"] < 70
        assert "factors" in result
    
    def test_generate_health_recommendations_good(self):
        """测试良好项目的健康建议"""
        health_factors = {
            "scores": {
                "requirements_completeness": 90,
                "task_execution": 85,
                "quality_control": 95,
                "project_status": 80
            },
            "stories_count": 10,
            "tasks_count": 20,
            "active_bugs": 0,
            "project_status": "doing"
        }
        
        recommendations = _generate_health_recommendations(health_factors)
        
        assert len(recommendations) == 1
        assert "项目整体状况良好" in recommendations[0]
    
    def test_generate_health_recommendations_poor(self):
        """测试较差项目的健康建议"""
        health_factors = {
            "scores": {
                "requirements_completeness": 30,  # < 50
                "task_execution": 40,             # < 50
                "quality_control": 60,            # < 70
                "project_status": 60
            },
            "stories_count": 0,  # 没有需求
            "tasks_count": 0,    # 没有任务
            "active_bugs": 5,    # 有活跃Bug
            "project_status": "wait"
        }
        
        recommendations = _generate_health_recommendations(health_factors)
        
        assert len(recommendations) == 5
        assert any("建议添加项目需求" in rec for rec in recommendations)
        assert any("建议创建具体的执行任务" in rec for rec in recommendations)
        assert any("建议优先处理" in rec and "Bug" in rec for rec in recommendations)
        assert any("项目处于等待状态" in rec for rec in recommendations)
    
    def test_analyze_project_data_integration(self):
        """测试项目数据分析集成功能"""
        project_info = {
            "id": 1,
            "name": "测试项目",
            "status": "doing"
        }
        
        stories_data = [
            {"id": 1, "status": "active", "stage": "developing", "pri": 1, "estimate": 8.0},
            {"id": 2, "status": "closed", "stage": "released", "pri": 2, "estimate": 16.0}
        ]
        
        tasks_data = [
            {"id": 1, "status": "doing", "type": "devel", "estimate": 8.0, "consumed": 6.0, "left": 2.0},
            {"id": 2, "status": "done", "type": "test", "estimate": 4.0, "consumed": 4.0, "left": 0.0}
        ]
        
        bugs_data = [
            {"id": 1, "status": "active", "severity": 1, "pri": 1}
        ]
        
        result = _analyze_project_data(project_info, stories_data, tasks_data, bugs_data)
        
        # 验证概览信息
        assert result["overview"]["total_stories"] == 2
        assert result["overview"]["total_tasks"] == 2
        assert result["overview"]["total_bugs"] == 1
        assert result["overview"]["project_status"] == "doing"
        
        # 验证各项分析结果存在
        assert "story_analysis" in result
        assert "task_analysis" in result
        assert "bug_analysis" in result
        assert "workload_summary" in result
        
        # 验证工时汇总
        assert result["workload_summary"]["estimated_hours"] == 36.0  # 24 + 12
        assert result["workload_summary"]["consumed_hours"] == 10.0
        assert result["workload_summary"]["remaining_hours"] == 2.0


@pytest.mark.asyncio
class TestProjectToolsIntegration:
    """项目工具集成测试"""
    
    async def test_project_summary_analysis_success(self):
        """测试项目汇总分析成功场景"""
        # 这里可以添加完整的集成测试
        # 由于需要模拟复杂的异步上下文，暂时跳过
        pass
    
    async def test_project_summary_analysis_project_not_found(self):
        """测试项目不存在的场景"""
        # 这里可以添加项目不存在的测试
        pass


class TestDataRelationTools:
    """数据关联查询工具测试"""
    
    def test_story_task_relation_empty_data(self):
        """测试空数据的需求-任务关联分析"""
        # 这里可以添加具体的测试逻辑
        # 由于涉及复杂的异步调用，暂时跳过具体实现
        pass
    
    def test_bug_to_story_tracking_conversion_analysis(self):
        """测试Bug转需求追踪的转换分析"""
        # 模拟Bug数据
        bugs_data = [
            {
                "id": 1,
                "title": "登录功能异常",
                "status": "active",
                "story": 10,  # 直接关联需求
                "severity": 1,
                "pri": 1
            },
            {
                "id": 2,
                "title": "用户注册问题",
                "status": "resolved",
                "story": 0,  # 无直接关联
                "severity": 2,
                "pri": 2
            }
        ]
        
        stories_data = [
            {
                "id": 10,
                "title": "用户登录功能开发",
                "status": "active",
                "stage": "developing"
            },
            {
                "id": 11,
                "title": "用户注册功能优化",
                "status": "closed",
                "stage": "released"
            }
        ]
        
        # 这里可以添加具体的关联分析逻辑测试
        # 验证Bug与需求的关联关系识别
        assert len(bugs_data) == 2
        assert len(stories_data) == 2
    
    def test_personnel_workload_calculation(self):
        """测试人员工作量计算逻辑"""
        # 模拟人员工作数据
        personnel_stats = {
            "user1": {
                "stories_created": 5,
                "stories_assigned": 3,
                "tasks_assigned": 8,
                "bugs_assigned": 2,
                "total_story_estimate": 40.0,
                "total_task_estimate": 32.0,
                "total_task_consumed": 28.0,
                "total_task_left": 4.0
            }
        }
        
        # 计算工作量指标
        stats = personnel_stats["user1"]
        total_estimate = stats["total_story_estimate"] + stats["total_task_estimate"]
        total_consumed = stats["total_task_consumed"]
        
        # 验证计算逻辑
        assert total_estimate == 72.0  # 40 + 32
        assert total_consumed == 28.0
        
        # 计算工作效率
        efficiency = (total_consumed / total_estimate * 100) if total_estimate > 0 else 0
        assert round(efficiency, 2) == 38.89  # 28/72 * 100
        
        # 计算工作负荷评分
        workload_score = (
            stats["stories_assigned"] * 2 +
            stats["tasks_assigned"] * 1 +
            stats["bugs_assigned"] * 1.5
        )
        assert workload_score == 17.0  # 3*2 + 8*1 + 2*1.5
    
    def test_workload_analysis_metrics(self):
        """测试工作量分析指标计算"""
        # 测试多个用户的工作量对比
        users_data = [
            {
                "user_account": "developer1",
                "metrics": {"workload_score": 25.0}
            },
            {
                "user_account": "developer2", 
                "metrics": {"workload_score": 15.0}
            },
            {
                "user_account": "tester1",
                "metrics": {"workload_score": 20.0}
            }
        ]
        
        # 按工作负荷排序
        sorted_users = sorted(users_data, key=lambda x: x["metrics"]["workload_score"], reverse=True)
        
        assert sorted_users[0]["user_account"] == "developer1"
        assert sorted_users[1]["user_account"] == "tester1"
        assert sorted_users[2]["user_account"] == "developer2"
        
        # 计算平均工作负荷
        total_workload = sum(u["metrics"]["workload_score"] for u in users_data)
        avg_workload = total_workload / len(users_data)
        
        assert total_workload == 60.0
        assert avg_workload == 20.0


@pytest.mark.asyncio
class TestDataRelationToolsIntegration:
    """数据关联工具集成测试"""
    
    async def test_story_task_relation_query_integration(self):
        """测试需求-任务关联查询集成功能"""
        # 这里可以添加完整的集成测试
        # 由于需要模拟复杂的异步上下文，暂时跳过
        pass
    
    async def test_bug_to_story_tracking_integration(self):
        """测试Bug转需求追踪集成功能"""
        # 这里可以添加完整的集成测试
        pass
    
    async def test_personnel_workload_analysis_integration(self):
        """测试人员工作量分析集成功能"""
        # 这里可以添加完整的集成测试
        pass