"""
分析增强层工具测试模块

测试基于直接接口提供数据聚合和分析功能的工具
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from zentao_mcp.exceptions import ZentaoAPIError, ValidationError


class TestStoryAnalysisTools:
    """需求分析增强工具测试"""
    
    @pytest.fixture
    def mock_server_context(self):
        """模拟服务器上下文"""
        server = MagicMock()
        client = AsyncMock()
        
        async def client_context():
            yield client
        
        server._client_context = MagicMock(return_value=client_context())
        return server, client
    
    @pytest.fixture
    def sample_stories_data(self):
        """示例需求数据"""
        return [
            {
                "id": 1,
                "title": "用户登录功能",
                "estimate": 16.0,
                "status": "active",
                "stage": "developing",
                "pri": 1
            },
            {
                "id": 2,
                "title": "用户注册功能",
                "estimate": 12.0,
                "status": "closed",
                "stage": "released",
                "pri": 2
            }
        ]
    
    @pytest.mark.asyncio
    async def test_analyze_story_workload_by_story_ids(self, mock_server_context, sample_stories_data):
        """测试根据需求ID分析工时统计"""
        server, client = mock_server_context
        
        # 模拟API响应
        api_response = {
            "rsCode": "00000000",
            "msg": "正常返回",
            "body": sample_stories_data
        }
        client.get_story_info_async.return_value = api_response
        
        with patch('zentao_mcp.main.server', server):
            from zentao_mcp.tools.analysis.story import register_story_analysis_tools
            
            mcp = MagicMock()
            tool_func = None
            
            def capture_tool(func):
                nonlocal tool_func
                tool_func = func
                return func
            
            mcp.tool.return_value = capture_tool
            register_story_analysis_tools(mcp)
            
            # 调用工具函数
            result = await tool_func(story_ids=["1", "2"])
            
            # 验证结果
            assert result["success"] is True
            assert result["total_stories"] == 2
            assert result["analysis"]["total_estimate_hours"] == 28.0
            assert result["analysis"]["completed_estimate_hours"] == 12.0
            assert result["analysis"]["active_estimate_hours"] == 16.0
            assert result["analysis"]["completion_rate"] == 42.86  # 12/28*100
            
            # 验证API调用
            client.get_story_info_async.assert_called_once_with(["1", "2"])
    
    @pytest.mark.asyncio
    async def test_analyze_story_workload_by_project_id(self, mock_server_context, sample_stories_data):
        """测试根据项目ID分析工时统计"""
        server, client = mock_server_context
        
        api_response = {
            "rsCode": "00000000",
            "msg": "正常返回",
            "body": sample_stories_data
        }
        client.get_stories_by_project_async.return_value = api_response
        
        with patch('zentao_mcp.tools.analysis.story.server', server):
            from zentao_mcp.tools.analysis.story import register_story_analysis_tools
            
            mcp = MagicMock()
            tool_func = None
            
            def capture_tool(func):
                nonlocal tool_func
                tool_func = func
                return func
            
            mcp.tool.return_value = capture_tool
            register_story_analysis_tools(mcp)
            
            # 调用工具函数
            result = await tool_func(project_id=123)
            
            # 验证结果
            assert result["success"] is True
            assert result["total_stories"] == 2
            assert result["analysis"]["total_estimate_hours"] == 28.0
            
            # 验证API调用
            client.get_stories_by_project_async.assert_called_once_with(123)
    
    @pytest.mark.asyncio
    async def test_analyze_story_workload_validation_error(self, mock_server_context):
        """测试参数验证错误"""
        server, client = mock_server_context
        
        with patch('zentao_mcp.tools.analysis.story.server', server):
            from zentao_mcp.tools.analysis.story import register_story_analysis_tools
            
            mcp = MagicMock()
            tool_func = None
            
            def capture_tool(func):
                nonlocal tool_func
                tool_func = func
                return func
            
            mcp.tool.return_value = capture_tool
            register_story_analysis_tools(mcp)
            
            # 调用工具函数，不提供必需参数
            with pytest.raises(ValidationError) as exc_info:
                await tool_func()
            
            assert "必须提供story_ids或project_id参数" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_analyze_story_workload_no_data(self, mock_server_context):
        """测试没有找到需求数据的情况"""
        server, client = mock_server_context
        
        api_response = {
            "rsCode": "00000000",
            "msg": "正常返回",
            "body": []
        }
        client.get_story_info_async.return_value = api_response
        
        with patch('zentao_mcp.tools.analysis.story.server', server):
            from zentao_mcp.tools.analysis.story import register_story_analysis_tools
            
            mcp = MagicMock()
            tool_func = None
            
            def capture_tool(func):
                nonlocal tool_func
                tool_func = func
                return func
            
            mcp.tool.return_value = capture_tool
            register_story_analysis_tools(mcp)
            
            # 调用工具函数
            result = await tool_func(story_ids=["999"])
            
            # 验证结果
            assert result["success"] is False
            assert result["total_stories"] == 0
            assert "没有找到需要分析的需求" in result["message"]
    
    @pytest.mark.asyncio
    async def test_analyze_story_workload_api_error(self, mock_server_context):
        """测试API错误情况"""
        server, client = mock_server_context
        
        api_response = {
            "rsCode": "99999999",
            "msg": "系统错误",
            "body": []
        }
        client.get_story_info_async.return_value = api_response
        
        with patch('zentao_mcp.tools.analysis.story.server', server):
            from zentao_mcp.tools.analysis.story import register_story_analysis_tools
            
            mcp = MagicMock()
            tool_func = None
            
            def capture_tool(func):
                nonlocal tool_func
                tool_func = func
                return func
            
            mcp.tool.return_value = capture_tool
            register_story_analysis_tools(mcp)
            
            # 调用工具函数
            result = await tool_func(story_ids=["1"])
            
            # 验证结果
            assert result["success"] is False
            assert result["total_stories"] == 0


class TestBugAnalysisTools:
    """Bug分析增强工具测试"""
    
    @pytest.fixture
    def sample_bugs_data(self):
        """示例Bug数据"""
        return [
            {
                "id": 1,
                "title": "登录页面异常",
                "status": "active",
                "severity": 1,
                "projectname": "项目A",
                "assignedTo": "developer1"
            },
            {
                "id": 2,
                "title": "注册功能错误",
                "status": "resolved",
                "severity": 2,
                "projectname": "项目B",
                "assignedTo": "developer2"
            }
        ]
    
    @pytest.mark.asyncio
    async def test_filter_bugs_by_status(self, sample_bugs_data):
        """测试根据状态过滤Bug"""
        # 直接测试过滤逻辑
        status_filter = ["active"]
        filtered_bugs = []
        
        for bug in sample_bugs_data:
            if status_filter and bug.get("status") not in status_filter:
                continue
            filtered_bugs.append(bug)
        
        # 验证结果
        assert len(filtered_bugs) == 1
        assert filtered_bugs[0]["status"] == "active"
        assert filtered_bugs[0]["id"] == 1
    
    @pytest.mark.asyncio
    async def test_filter_bugs_by_severity(self, sample_bugs_data):
        """测试根据严重程度过滤Bug"""
        severity_filter = [1]  # 只要严重级别
        filtered_bugs = []
        
        for bug in sample_bugs_data:
            if severity_filter and bug.get("severity") not in severity_filter:
                continue
            filtered_bugs.append(bug)
        
        # 验证结果
        assert len(filtered_bugs) == 1
        assert filtered_bugs[0]["severity"] == 1
        assert filtered_bugs[0]["id"] == 1
    
    @pytest.mark.asyncio
    async def test_filter_bugs_by_project(self, sample_bugs_data):
        """测试根据项目过滤Bug"""
        project_filter = ["项目A"]
        filtered_bugs = []
        
        for bug in sample_bugs_data:
            if project_filter:
                project_name = bug.get("projectname", "")
                if not any(project in project_name for project in project_filter):
                    continue
            filtered_bugs.append(bug)
        
        # 验证结果
        assert len(filtered_bugs) == 1
        assert "项目A" in filtered_bugs[0]["projectname"]
        assert filtered_bugs[0]["id"] == 1
    
    @pytest.mark.asyncio
    async def test_filter_bugs_by_assignee(self, sample_bugs_data):
        """测试根据指派人过滤Bug"""
        assignee_filter = ["developer1"]
        filtered_bugs = []
        
        for bug in sample_bugs_data:
            if assignee_filter and bug.get("assignedTo") not in assignee_filter:
                continue
            filtered_bugs.append(bug)
        
        # 验证结果
        assert len(filtered_bugs) == 1
        assert filtered_bugs[0]["assignedTo"] == "developer1"
        assert filtered_bugs[0]["id"] == 1
    
    @pytest.mark.asyncio
    async def test_filter_bugs_multiple_criteria(self, sample_bugs_data):
        """测试多条件过滤Bug"""
        status_filter = ["active"]
        severity_filter = [1]
        project_filter = ["项目A"]
        
        filtered_bugs = []
        
        for bug in sample_bugs_data:
            # 状态过滤
            if status_filter and bug.get("status") not in status_filter:
                continue
            
            # 严重程度过滤
            if severity_filter and bug.get("severity") not in severity_filter:
                continue
            
            # 项目过滤
            if project_filter:
                project_name = bug.get("projectname", "")
                if not any(project in project_name for project in project_filter):
                    continue
            
            filtered_bugs.append(bug)
        
        # 验证结果
        assert len(filtered_bugs) == 1
        assert filtered_bugs[0]["id"] == 1
        assert filtered_bugs[0]["status"] == "active"
        assert filtered_bugs[0]["severity"] == 1
        assert "项目A" in filtered_bugs[0]["projectname"]


class TestProjectAnalysisTools:
    """项目分析增强工具测试"""
    
    @pytest.fixture
    def sample_project_data(self):
        """示例项目数据"""
        return {
            "project_info": {
                "id": 1,
                "name": "测试项目",
                "status": "doing"
            },
            "stories": [
                {"id": 1, "status": "active", "stage": "developing", "pri": 1, "estimate": 8.0},
                {"id": 2, "status": "closed", "stage": "released", "pri": 2, "estimate": 16.0}
            ],
            "tasks": [
                {"id": 1, "status": "doing", "type": "devel", "estimate": 8.0, "consumed": 6.0, "left": 2.0},
                {"id": 2, "status": "done", "type": "test", "estimate": 4.0, "consumed": 4.0, "left": 0.0}
            ],
            "bugs": [
                {"id": 1, "status": "active", "severity": 1, "pri": 1}
            ]
        }
    
    def test_project_data_analysis_integration(self, sample_project_data):
        """测试项目数据分析集成功能"""
        from zentao_mcp.tools.project import _analyze_project_data
        
        result = _analyze_project_data(
            sample_project_data["project_info"],
            sample_project_data["stories"],
            sample_project_data["tasks"],
            sample_project_data["bugs"]
        )
        
        # 验证概览信息
        assert result["overview"]["total_stories"] == 2
        assert result["overview"]["total_tasks"] == 2
        assert result["overview"]["total_bugs"] == 1
        assert result["overview"]["project_status"] == "doing"
        
        # 验证各项分析结果存在
        assert "story_analysis" in result
        assert "task_analysis" in result
        assert "bug_analysis" in result
        assert "workload_summary" in result
        
        # 验证工时汇总
        assert result["workload_summary"]["estimated_hours"] == 36.0  # 24 + 12
        assert result["workload_summary"]["consumed_hours"] == 10.0
        assert result["workload_summary"]["remaining_hours"] == 2.0
    
    def test_project_progress_calculation(self):
        """测试项目进度计算"""
        from zentao_mcp.tools.project import _calculate_project_progress
        
        story_stats = {
            "total_count": 10,
            "status_distribution": {"closed": 6, "active": 4}
        }
        
        task_stats = {
            "total_count": 20,
            "status_distribution": {"done": 12, "doing": 8},
            "total_estimate": 100.0,
            "total_consumed": 80.0
        }
        
        result = _calculate_project_progress(story_stats, task_stats)
        
        assert result["story_progress"] == 60.0  # 6/10 * 100
        assert result["task_progress"] == 60.0   # 12/20 * 100
        assert result["workload_progress"] == 80.0  # 80/100 * 100
        # 综合进度: 60*0.4 + 60*0.4 + 80*0.2 = 64.0
        assert result["overall_progress"] == 64.0
    
    def test_project_health_calculation_excellent(self):
        """测试优秀项目健康度计算"""
        from zentao_mcp.tools.project import _calculate_project_health
        
        story_stats = {"total_count": 10}
        task_stats = {
            "total_count": 20,
            "total_estimate": 100.0,
            "total_consumed": 90.0
        }
        bug_stats = {"total_count": 1}  # Bug率 1/30 = 3.3%
        progress = {"overall_progress": 90.0}
        
        result = _calculate_project_health(story_stats, task_stats, bug_stats, progress)
        
        assert result["health_level"] == "优秀"
        assert result["overall_score"] >= 90
        assert result["factors"]["progress_health"] == 100
        assert result["factors"]["bug_health"] == 100
        assert result["factors"]["workload_health"] == 100
    
    def test_project_health_calculation_poor(self):
        """测试较差项目健康度计算"""
        from zentao_mcp.tools.project import _calculate_project_health
        
        story_stats = {"total_count": 10}
        task_stats = {
            "total_count": 20,
            "total_estimate": 100.0,
            "total_consumed": 200.0  # 超时100%
        }
        bug_stats = {"total_count": 15}  # Bug率 15/30 = 50%
        progress = {"overall_progress": 30.0}  # 进度较慢
        
        result = _calculate_project_health(story_stats, task_stats, bug_stats, progress)
        
        assert result["health_level"] in ["较差", "差"]
        assert result["overall_score"] < 70
        assert result["factors"]["progress_health"] == 40
        assert result["factors"]["bug_health"] == 20
        assert result["factors"]["workload_health"] == 20


class TestDataRelationAnalysisTools:
    """数据关联分析工具测试"""
    
    def test_story_task_relation_analysis(self):
        """测试需求-任务关联分析"""
        # 模拟需求和任务数据
        stories_data = [
            {"id": 1, "title": "用户登录功能", "status": "active"},
            {"id": 2, "title": "用户注册功能", "status": "closed"}
        ]
        
        tasks_data = [
            {"id": 1, "name": "实现登录接口", "story": 1, "status": "doing"},
            {"id": 2, "name": "登录页面开发", "story": 1, "status": "done"},
            {"id": 3, "name": "注册接口开发", "story": 2, "status": "done"}
        ]
        
        # 分析关联关系
        story_task_map = {}
        for task in tasks_data:
            story_id = task.get("story", 0)
            if story_id not in story_task_map:
                story_task_map[story_id] = []
            story_task_map[story_id].append(task)
        
        # 验证关联关系
        assert len(story_task_map[1]) == 2  # 需求1有2个任务
        assert len(story_task_map[2]) == 1  # 需求2有1个任务
        
        # 验证任务状态分布
        story1_tasks = story_task_map[1]
        doing_count = sum(1 for task in story1_tasks if task["status"] == "doing")
        done_count = sum(1 for task in story1_tasks if task["status"] == "done")
        
        assert doing_count == 1
        assert done_count == 1
    
    def test_bug_to_story_tracking_analysis(self):
        """测试Bug转需求追踪分析"""
        # 模拟Bug和需求数据
        bugs_data = [
            {"id": 1, "title": "登录功能异常", "status": "active", "story": 10},
            {"id": 2, "title": "注册问题", "status": "resolved", "story": 0}
        ]
        
        stories_data = [
            {"id": 10, "title": "用户登录功能开发", "status": "active"},
            {"id": 11, "title": "用户注册功能优化", "status": "closed"}
        ]
        
        # 分析Bug转需求情况
        direct_linked = 0
        indirect_linked = 0
        unlinked = 0
        
        for bug in bugs_data:
            if bug.get("story", 0) > 0:
                direct_linked += 1
            else:
                # 这里可以添加间接关联逻辑（通过标题匹配等）
                bug_title = bug.get("title", "").lower()
                found_indirect = False
                
                for story in stories_data:
                    story_title = story.get("title", "").lower()
                    if any(keyword in story_title for keyword in bug_title.split() if len(keyword) > 2):
                        indirect_linked += 1
                        found_indirect = True
                        break
                
                if not found_indirect:
                    unlinked += 1
        
        # 验证分析结果
        assert direct_linked == 1  # 1个直接关联
        assert indirect_linked == 1  # 1个间接关联（通过标题匹配）
        assert unlinked == 0  # 0个未关联
    
    def test_personnel_workload_analysis(self):
        """测试人员工作量分析"""
        # 模拟人员工作数据
        personnel_data = {
            "developer1": {
                "stories_created": 5,
                "stories_assigned": 3,
                "tasks_assigned": 8,
                "bugs_assigned": 2,
                "total_story_estimate": 40.0,
                "total_task_estimate": 32.0,
                "total_task_consumed": 28.0,
                "total_task_left": 4.0
            },
            "developer2": {
                "stories_created": 3,
                "stories_assigned": 2,
                "tasks_assigned": 5,
                "bugs_assigned": 1,
                "total_story_estimate": 24.0,
                "total_task_estimate": 20.0,
                "total_task_consumed": 18.0,
                "total_task_left": 2.0
            }
        }
        
        # 计算工作量指标
        for user, stats in personnel_data.items():
            total_estimate = stats["total_story_estimate"] + stats["total_task_estimate"]
            total_consumed = stats["total_task_consumed"]
            
            # 计算工作效率
            efficiency = (total_consumed / total_estimate * 100) if total_estimate > 0 else 0
            
            # 计算工作负荷评分
            workload_score = (
                stats["stories_assigned"] * 2 +
                stats["tasks_assigned"] * 1 +
                stats["bugs_assigned"] * 1.5
            )
            
            # 验证计算结果
            if user == "developer1":
                assert total_estimate == 72.0  # 40 + 32
                assert round(efficiency, 2) == 38.89  # 28/72 * 100
                assert workload_score == 17.0  # 3*2 + 8*1 + 2*1.5
            elif user == "developer2":
                assert total_estimate == 44.0  # 24 + 20
                assert round(efficiency, 2) == 40.91  # 18/44 * 100
                assert workload_score == 10.5  # 2*2 + 5*1 + 1*1.5


class TestAnalysisToolsErrorHandling:
    """分析工具错误处理测试"""
    
    @pytest.mark.asyncio
    async def test_invalid_data_format_handling(self):
        """测试无效数据格式处理"""
        # 模拟无效的需求数据
        invalid_stories_data = [
            {"id": "invalid", "title": None, "estimate": "not_a_number"},
            {"missing_id": True, "status": "active"}
        ]
        
        # 测试数据格式化处理
        valid_stories = []
        for story_data in invalid_stories_data:
            try:
                story = {
                    "id": int(story_data.get("id", 0)),
                    "title": str(story_data.get("title", "")),
                    "estimate": float(story_data.get("estimate", 0.0)),
                    "status": str(story_data.get("status", "active"))
                }
                valid_stories.append(story)
            except (ValueError, TypeError):
                # 跳过无效数据
                continue
        
        # 验证只有有效数据被保留
        assert len(valid_stories) == 0  # 所有数据都无效
    
    @pytest.mark.asyncio
    async def test_empty_response_handling(self):
        """测试空响应处理"""
        # 模拟空响应
        empty_responses = [None, {}, {"body": None}, {"body": []}]
        
        for response in empty_responses:
            # 测试空响应处理逻辑
            if not response or response.get("rsCode") != "00000000":
                result = {
                    "success": False,
                    "message": "获取数据失败",
                    "total_items": 0,
                    "items": []
                }
            else:
                items_data = response.get("body", [])
                if not isinstance(items_data, list):
                    result = {
                        "success": False,
                        "message": "数据格式错误",
                        "total_items": 0,
                        "items": []
                    }
                else:
                    result = {
                        "success": True,
                        "total_items": len(items_data),
                        "items": items_data
                    }
            
            # 验证错误处理结果
            assert result["success"] is False
            assert result["total_items"] == 0
    
    @pytest.mark.asyncio
    async def test_network_error_handling(self):
        """测试网络错误处理"""
        # 模拟网络错误
        network_errors = [
            TimeoutError("请求超时"),
            ConnectionError("连接失败"),
            Exception("未知网络错误")
        ]
        
        for error in network_errors:
            # 测试错误处理逻辑
            try:
                raise error
            except Exception as e:
                error_result = {
                    "success": False,
                    "message": f"网络请求失败: {str(e)}",
                    "error_type": type(e).__name__
                }
            
            # 验证错误处理结果
            assert error_result["success"] is False
            assert "网络请求失败" in error_result["message"]
            assert error_result["error_type"] in ["TimeoutError", "ConnectionError", "Exception"]


class TestAnalysisToolsPerformance:
    """分析工具性能测试"""
    
    def test_large_dataset_processing(self):
        """测试大数据集处理性能"""
        import time
        
        # 生成大量测试数据
        large_stories_data = []
        for i in range(1000):
            large_stories_data.append({
                "id": i,
                "title": f"需求{i}",
                "estimate": float(i % 20 + 1),
                "status": "active" if i % 2 == 0 else "closed",
                "stage": "developing" if i % 3 == 0 else "released",
                "pri": (i % 4) + 1
            })
        
        # 测试处理时间
        start_time = time.time()
        
        # 模拟分析处理
        total_estimate = 0.0
        status_stats = {}
        
        for story in large_stories_data:
            total_estimate += story.get("estimate", 0.0)
            status = story.get("status", "active")
            status_stats[status] = status_stats.get(status, 0) + 1
        
        processing_time = time.time() - start_time
        
        # 验证处理结果和性能
        assert len(large_stories_data) == 1000
        assert total_estimate > 0
        assert len(status_stats) == 2
        assert processing_time < 1.0  # 处理时间应该小于1秒
    
    def test_memory_usage_optimization(self):
        """测试内存使用优化"""
        import sys
        
        # 测试数据结构的内存使用
        small_data = {"id": 1, "title": "test"}
        large_data = {
            "id": 1,
            "title": "test" * 1000,
            "description": "desc" * 1000,
            "extra_data": list(range(1000))
        }
        
        small_size = sys.getsizeof(small_data)
        large_size = sys.getsizeof(large_data)
        
        # 验证内存使用差异
        assert large_size > small_size
        assert small_size < 1024  # 小数据结构应该小于1KB
