"""
直接接口层工具测试模块

测试与禅道API 1:1对应的直接查询接口
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from zentao_mcp.exceptions import ZentaoAPIError


class TestDepartmentDirectTools:
    """部门管理直接接口工具测试"""
    
    @pytest.fixture
    def mock_server_context(self):
        """模拟服务器上下文"""
        server = MagicMock()
        client = AsyncMock()
        
        async def client_context():
            yield client
        
        server._client_context = MagicMock(return_value=client_context())
        return server, client
    
    @pytest.mark.asyncio
    async def test_zentao_get_all_departments_success(self, mock_server_context):
        """测试获取所有部门列表成功"""
        server, client = mock_server_context
        
        # 模拟API响应
        expected_response = {
            "rsCode": "********",
            "msg": "正常返回",
            "body": [
                {"id": 1, "name": "开发部门"},
                {"id": 2, "name": "测试部门"}
            ]
        }
        client.get_all_departments_async.return_value = expected_response
        
        # 模拟全局服务器实例
        with patch('zentao_mcp.tools.direct.department.server', server):
            from zentao_mcp.tools.direct.department import register_department_direct_tools
            
            # 创建模拟MCP实例
            mcp = MagicMock()
            tool_func = None
            
            def capture_tool(func):
                nonlocal tool_func
                tool_func = func
                return func
            
            mcp.tool.return_value = capture_tool
            
            # 注册工具
            register_department_direct_tools(mcp)
            
            # 获取注册的工具函数
            assert tool_func is not None
            
            # 调用工具函数
            result = await tool_func()
            
            # 验证结果
            assert result == expected_response
            client.get_all_departments_async.assert_called_once_with(parse_model=False)
    
    @pytest.mark.asyncio
    async def test_zentao_get_all_departments_api_error(self, mock_server_context):
        """测试获取所有部门列表API错误"""
        server, client = mock_server_context
        
        # 模拟API异常
        client.get_all_departments_async.side_effect = Exception("网络错误")
        
        with patch('zentao_mcp.tools.direct.department.server', server):
            from zentao_mcp.tools.direct.department import register_department_direct_tools
            
            mcp = MagicMock()
            tool_func = None
            
            def capture_tool(func):
                nonlocal tool_func
                tool_func = func
                return func
            
            mcp.tool.return_value = capture_tool
            register_department_direct_tools(mcp)
            
            # 调用工具函数并验证异常
            with pytest.raises(ZentaoAPIError) as exc_info:
                await tool_func()
            
            assert "获取所有部门列表失败" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_zentao_get_users_by_dept_success(self, mock_server_context):
        """测试根据部门查询用户列表成功"""
        server, client = mock_server_context
        
        expected_response = {
            "rsCode": "********",
            "msg": "正常返回",
            "body": [
                {"id": 1, "account": "user1", "realname": "用户1"},
                {"id": 2, "account": "user2", "realname": "用户2"}
            ]
        }
        client.get_users_by_department_async.return_value = expected_response
        
        with patch('zentao_mcp.tools.direct.department.server', server):
            from zentao_mcp.tools.direct.department import register_department_direct_tools
            
            mcp = MagicMock()
            tools = []
            
            def capture_tool(func):
                tools.append(func)
                return func
            
            mcp.tool.return_value = capture_tool
            register_department_direct_tools(mcp)
            
            # 获取第二个注册的工具函数（get_users_by_dept）
            assert len(tools) >= 2
            tool_func = tools[1]
            
            # 调用工具函数
            result = await tool_func(dept_id=10)
            
            # 验证结果
            assert result == expected_response
            client.get_users_by_department_async.assert_called_once_with(10)


class TestProjectDirectTools:
    """项目管理直接接口工具测试"""
    
    @pytest.fixture
    def mock_server_context(self):
        """模拟服务器上下文"""
        server = MagicMock()
        client = AsyncMock()
        
        async def client_context():
            yield client
        
        server._client_context = MagicMock(return_value=client_context())
        return server, client
    
    @pytest.mark.asyncio
    async def test_project_direct_api_calls(self, mock_server_context):
        """测试项目直接接口API调用"""
        server, client = mock_server_context
        
        # 模拟项目列表响应
        projects_response = {
            "rsCode": "********",
            "msg": "正常返回",
            "body": [
                {"id": 1, "name": "项目A", "status": "doing"},
                {"id": 2, "name": "项目B", "status": "done"}
            ]
        }
        client.get_all_projects_async.return_value = projects_response
        
        with patch('zentao_mcp.tools.direct.project.server', server):
            # 这里可以添加具体的项目直接接口测试
            # 由于项目直接接口工具可能还未完全实现，先验证基本结构
            assert server is not None
            assert client is not None


class TestStoryDirectTools:
    """需求管理直接接口工具测试"""
    
    @pytest.fixture
    def sample_story_data(self):
        """示例需求数据"""
        return {
            "id": 123,
            "title": "用户登录功能",
            "status": "active",
            "stage": "developing",
            "pri": 1,
            "estimate": 16.0,
            "project": 456,
            "projectname": "测试项目"
        }
    
    @pytest.mark.asyncio
    async def test_story_direct_data_format(self, sample_story_data):
        """测试需求直接接口数据格式"""
        # 验证数据结构
        assert "id" in sample_story_data
        assert "title" in sample_story_data
        assert "status" in sample_story_data
        assert "estimate" in sample_story_data
        
        # 验证数据类型
        assert isinstance(sample_story_data["id"], int)
        assert isinstance(sample_story_data["title"], str)
        assert isinstance(sample_story_data["estimate"], float)


class TestTaskDirectTools:
    """任务管理直接接口工具测试"""
    
    @pytest.fixture
    def sample_task_data(self):
        """示例任务数据"""
        return {
            "id": 789,
            "name": "实现登录接口",
            "status": "doing",
            "type": "devel",
            "estimate": 8.0,
            "consumed": 6.0,
            "left": 2.0,
            "assignedTo": "developer1",
            "story": 123
        }
    
    @pytest.mark.asyncio
    async def test_task_direct_data_format(self, sample_task_data):
        """测试任务直接接口数据格式"""
        # 验证数据结构
        assert "id" in sample_task_data
        assert "name" in sample_task_data
        assert "status" in sample_task_data
        assert "estimate" in sample_task_data
        assert "consumed" in sample_task_data
        assert "left" in sample_task_data
        
        # 验证工时数据类型
        assert isinstance(sample_task_data["estimate"], float)
        assert isinstance(sample_task_data["consumed"], float)
        assert isinstance(sample_task_data["left"], float)


class TestUserDirectTools:
    """用户管理直接接口工具测试"""
    
    @pytest.fixture
    def sample_user_data(self):
        """示例用户数据"""
        return {
            "id": 101,
            "account": "testuser",
            "realname": "测试用户",
            "email": "<EMAIL>",
            "dept": 10,
            "role": "dev"
        }
    
    @pytest.mark.asyncio
    async def test_user_direct_data_format(self, sample_user_data):
        """测试用户直接接口数据格式"""
        # 验证数据结构
        assert "id" in sample_user_data
        assert "account" in sample_user_data
        assert "realname" in sample_user_data
        assert "dept" in sample_user_data
        
        # 验证数据类型
        assert isinstance(sample_user_data["id"], int)
        assert isinstance(sample_user_data["account"], str)
        assert isinstance(sample_user_data["dept"], int)


class TestDirectToolsErrorHandling:
    """直接接口工具错误处理测试"""
    
    @pytest.mark.asyncio
    async def test_api_response_empty(self):
        """测试API响应为空的情况"""
        server = MagicMock()
        client = AsyncMock()
        
        async def client_context():
            yield client
        
        server._client_context = MagicMock(return_value=client_context())
        client.get_all_departments_async.return_value = None
        
        with patch('zentao_mcp.tools.direct.department.server', server):
            from zentao_mcp.tools.direct.department import register_department_direct_tools
            
            mcp = MagicMock()
            tool_func = None
            
            def capture_tool(func):
                nonlocal tool_func
                tool_func = func
                return func
            
            mcp.tool.return_value = capture_tool
            register_department_direct_tools(mcp)
            
            # 调用工具函数并验证异常
            with pytest.raises(ZentaoAPIError) as exc_info:
                await tool_func()
            
            assert "禅道API响应为空" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_network_timeout_error(self):
        """测试网络超时错误"""
        server = MagicMock()
        client = AsyncMock()
        
        async def client_context():
            yield client
        
        server._client_context = MagicMock(return_value=client_context())
        client.get_all_departments_async.side_effect = TimeoutError("请求超时")
        
        with patch('zentao_mcp.tools.direct.department.server', server):
            from zentao_mcp.tools.direct.department import register_department_direct_tools
            
            mcp = MagicMock()
            tool_func = None
            
            def capture_tool(func):
                nonlocal tool_func
                tool_func = func
                return func
            
            mcp.tool.return_value = capture_tool
            register_department_direct_tools(mcp)
            
            # 调用工具函数并验证异常
            with pytest.raises(ZentaoAPIError) as exc_info:
                await tool_func()
            
            assert "获取所有部门列表失败" in str(exc_info.value)
            assert "请求超时" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_invalid_response_format(self):
        """测试无效响应格式"""
        server = MagicMock()
        client = AsyncMock()
        
        async def client_context():
            yield client
        
        server._client_context = MagicMock(return_value=client_context())
        # 返回无效格式的响应
        client.get_all_departments_async.return_value = "invalid_response"
        
        with patch('zentao_mcp.tools.direct.department.server', server):
            from zentao_mcp.tools.direct.department import register_department_direct_tools
            
            mcp = MagicMock()
            tool_func = None
            
            def capture_tool(func):
                nonlocal tool_func
                tool_func = func
                return func
            
            mcp.tool.return_value = capture_tool
            register_department_direct_tools(mcp)
            
            # 调用工具函数，应该正常返回（因为直接接口不验证格式）
            result = await tool_func()
            assert result == "invalid_response"


class TestDirectToolsIntegration:
    """直接接口工具集成测试"""
    
    @pytest.mark.asyncio
    async def test_multiple_tools_registration(self):
        """测试多个工具的注册"""
        from zentao_mcp.tools.direct.department import register_department_direct_tools
        
        mcp = MagicMock()
        tools_registered = []
        
        def capture_tool(func):
            tools_registered.append(func)
            return func
        
        mcp.tool.return_value = capture_tool
        
        # 注册部门工具
        register_department_direct_tools(mcp)
        
        # 验证工具注册数量
        assert len(tools_registered) >= 2  # 至少注册了2个工具
        
        # 验证工具函数可调用
        for tool_func in tools_registered:
            assert callable(tool_func)
    
    @pytest.mark.asyncio
    async def test_tool_function_signatures(self):
        """测试工具函数签名"""
        from zentao_mcp.tools.direct.department import register_department_direct_tools
        import inspect
        
        mcp = MagicMock()
        tools_registered = []
        
        def capture_tool(func):
            tools_registered.append(func)
            return func
        
        mcp.tool.return_value = capture_tool
        register_department_direct_tools(mcp)
        
        # 验证函数签名
        for tool_func in tools_registered:
            sig = inspect.signature(tool_func)
            # 所有工具函数都应该是异步的
            assert inspect.iscoroutinefunction(tool_func)
            
            # 验证返回类型注解
            if sig.return_annotation != inspect.Signature.empty:
                # 应该返回Dict[str, Any]类型
                assert "Dict" in str(sig.return_annotation) or "dict" in str(sig.return_annotation).lower()