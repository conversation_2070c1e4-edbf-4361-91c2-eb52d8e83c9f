"""
需求资源模块测试

测试需求相关的MCP资源和工具功能
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from fastmcp import Context

from zentao_mcp.resources.story import register_story_resources
from zentao_mcp.tools.direct.story import register_story_direct_tools
from zentao_mcp.tools.analysis.story import register_story_analysis_tools
from zentao_mcp.exceptions import ZentaoAPIError, ResourceNotFoundError


class TestStoryResources:
    """需求资源测试类"""
    
    @pytest.fixture
    def mock_context(self):
        """创建模拟的上下文"""
        context = MagicMock(spec=Context)
        server = MagicMock()
        context.server = server
        return context
    
    @pytest.fixture
    def sample_story_response(self):
        """示例需求响应数据"""
        return {
            "rsCode": "00000000",
            "msg": "正常返回",
            "body": {
                "id": "123",
                "title": "测试需求",
                "product": "1",
                "productname": "测试产品",
                "module": "0",
                "modulename": "",
                "pri": "3",
                "estimate": "8.0",
                "status": "active",
                "stage": "developing",
                "openedBy": "admin",
                "openedByEmpid": "admin001",
                "assignedTo": "developer",
                "assignedToEmpid": "dev001",
                "spec": "这是一个测试需求的规格说明"
            }
        }
    
    @pytest.fixture
    def sample_stories_list_response(self):
        """示例需求列表响应数据"""
        return {
            "rsCode": "00000000",
            "msg": "正常返回",
            "body": [
                {
                    "id": "123",
                    "title": "需求1",
                    "product": "1",
                    "productname": "产品1",
                    "module": "0",
                    "modulename": "",
                    "pri": "3",
                    "estimate": "8.0",
                    "status": "active",
                    "stage": "developing",
                    "openedBy": "admin",
                    "assignedTo": "dev1",
                    "spec": "需求1规格"
                },
                {
                    "id": "124",
                    "title": "需求2",
                    "product": "1",
                    "productname": "产品1",
                    "module": "0",
                    "modulename": "",
                    "pri": "2",
                    "estimate": "16.0",
                    "status": "closed",
                    "stage": "released",
                    "openedBy": "admin",
                    "assignedTo": "dev2",
                    "spec": "需求2规格"
                }
            ]
        }
    
    def test_register_story_resources(self):
        """测试需求资源注册"""
        mock_mcp = MagicMock()
        registered_functions = {}
        
        def mock_resource(uri):
            def decorator(func):
                registered_functions[uri] = func
                return func
            return decorator
        
        mock_mcp.resource = mock_resource
        register_story_resources(mock_mcp)
        
        # 验证注册的资源URI
        expected_uris = [
            "zentao://stories",
            "zentao://story/{story_id}",
            "zentao://project/{project_id}/stories",
            "zentao://stories/by-status/{status}",
            "zentao://stories/by-stage/{stage}",
            "zentao://stories/by-priority/{priority}",
            "zentao://stories/workload-summary"
        ]
        
        for uri in expected_uris:
            assert uri in registered_functions
    
    @pytest.mark.asyncio
    async def test_get_story_detail_success(self, mock_context, sample_story_response):
        """测试获取需求详情成功"""
        # 设置模拟客户端
        mock_client = AsyncMock()
        mock_client.get_story_by_id_async.return_value = sample_story_response
        
        mock_context.server._client_context.return_value.__aenter__.return_value = mock_client
        
        # 注册资源并获取函数
        mock_mcp = MagicMock()
        registered_functions = {}
        
        def mock_resource(uri):
            def decorator(func):
                registered_functions[uri] = func
                return func
            return decorator
        
        mock_mcp.resource = mock_resource
        register_story_resources(mock_mcp)
        
        # 调用详情函数
        get_story_func = registered_functions["zentao://story/{story_id}"]
        result = await get_story_func(123, mock_context)
        
        # 验证结果
        assert result["id"] == 123
        assert result["title"] == "测试需求"
        assert result["estimate"] == 8.0
        assert result["status"] == "active"
        assert result["stage"] == "developing"
        assert result["spec"] == "这是一个测试需求的规格说明"
        
        # 验证API调用
        mock_client.get_story_by_id_async.assert_called_once_with(123)
    
    @pytest.mark.asyncio
    async def test_get_story_detail_not_found(self, mock_context):
        """测试获取不存在的需求详情"""
        # 设置模拟客户端返回错误
        mock_client = AsyncMock()
        mock_client.get_story_by_id_async.return_value = {
            "rsCode": "10000001",
            "msg": "需求不存在"
        }
        
        mock_context.server._client_context.return_value.__aenter__.return_value = mock_client
        
        # 注册资源并获取函数
        mock_mcp = MagicMock()
        registered_functions = {}
        
        def mock_resource(uri):
            def decorator(func):
                registered_functions[uri] = func
                return func
            return decorator
        
        mock_mcp.resource = mock_resource
        register_story_resources(mock_mcp)
        
        # 调用详情函数，查询不存在的需求
        get_story_func = registered_functions["zentao://story/{story_id}"]
        
        with pytest.raises(ResourceNotFoundError):
            await get_story_func(999, mock_context)


class TestStoryTools:
    """需求工具测试类"""
    
    @pytest.fixture
    def mock_context(self):
        """创建模拟的上下文"""
        context = MagicMock(spec=Context)
        server = MagicMock()
        context.server = server
        return context
    
    def test_register_story_tools(self):
        """测试需求工具注册"""
        mock_mcp = MagicMock()
        registered_functions = {}
        
        def mock_tool():
            def decorator(func):
                registered_functions[func.__name__] = func
                return func
            return decorator
        
        mock_mcp.tool = mock_tool
        register_story_tools(mock_mcp)
        
        # 验证注册的工具函数
        expected_tools = [
            "batch_query_stories",
            "validate_story_existence", 
            "analyze_story_workload"
        ]
        
        for tool_name in expected_tools:
            assert tool_name in registered_functions
    
    @pytest.mark.asyncio
    async def test_batch_query_stories_success(self, mock_context):
        """测试批量查询需求成功"""
        # 设置模拟客户端
        mock_client = AsyncMock()
        mock_response = {
            "rsCode": "00000000",
            "body": [
                {
                    "id": "123",
                    "title": "需求1",
                    "product": "1",
                    "productname": "产品1",
                    "module": "0",
                    "modulename": "",
                    "pri": "3",
                    "estimate": "8.0",
                    "status": "active",
                    "stage": "developing",
                    "openedBy": "admin",
                    "assignedTo": "dev1",
                    "spec": "需求1规格"
                },
                {
                    "id": "124",
                    "title": "需求2",
                    "product": "1",
                    "productname": "产品1",
                    "module": "0",
                    "modulename": "",
                    "pri": "2",
                    "estimate": "16.0",
                    "status": "active",
                    "stage": "planned",
                    "openedBy": "admin",
                    "assignedTo": "dev2",
                    "spec": "需求2规格"
                }
            ]
        }
        
        mock_client.get_story_info_async.return_value = mock_response
        mock_context.server._client_context.return_value.__aenter__.return_value = mock_client
        
        # 注册工具并获取函数
        mock_mcp = MagicMock()
        registered_functions = {}
        
        def mock_tool():
            def decorator(func):
                registered_functions[func.__name__] = func
                return func
            return decorator
        
        mock_mcp.tool = mock_tool
        register_story_tools(mock_mcp)
        
        # 调用批量查询函数
        batch_query_func = registered_functions["batch_query_stories"]
        result = await batch_query_func(["123", "124"], mock_context)
        
        # 验证结果
        assert result["success"] is True
        assert result["total_requested"] == 2
        assert result["successful_count"] == 2
        assert result["failed_count"] == 0
        assert len(result["stories"]) == 2
        assert result["stories"][0]["id"] == 123
        assert result["stories"][1]["id"] == 124
        
        # 验证API调用
        mock_client.get_story_info_async.assert_called_once_with(["123", "124"])
    
    @pytest.mark.asyncio
    async def test_validate_story_existence_success(self, mock_context):
        """测试验证需求存在性成功"""
        # 设置模拟客户端
        mock_client = AsyncMock()
        mock_response = {
            "rsCode": "00000000",
            "body": [
                {
                    "id": "123",
                    "title": "存在的需求"
                }
            ]
        }
        
        mock_client.check_story_exists_async.return_value = mock_response
        mock_context.server._client_context.return_value.__aenter__.return_value = mock_client
        
        # 注册工具并获取函数
        mock_mcp = MagicMock()
        registered_functions = {}
        
        def mock_tool():
            def decorator(func):
                registered_functions[func.__name__] = func
                return func
            return decorator
        
        mock_mcp.tool = mock_tool
        register_story_tools(mock_mcp)
        
        # 调用验证函数
        validate_func = registered_functions["validate_story_existence"]
        result = await validate_func(["123", "999"], mock_context)
        
        # 验证结果
        assert result["success"] is True
        assert result["total_checked"] == 2
        assert result["existing_count"] == 1
        assert result["missing_count"] == 1
        assert result["existing_ids"] == ["123"]
        assert result["missing_ids"] == ["999"]
        assert result["existence_rate"] == 50.0
        
        # 验证API调用
        mock_client.check_story_exists_async.assert_called_once_with(["123", "999"])


if __name__ == "__main__":
    pytest.main([__file__])