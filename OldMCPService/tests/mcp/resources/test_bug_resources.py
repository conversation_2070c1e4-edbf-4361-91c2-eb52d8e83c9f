"""
Bug资源测试模块

测试Bug相关的MCP资源功能
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from zentao_mcp.resources.bug import register_bug_resources, _format_bug_data
from zentao_mcp.exceptions import ZentaoAPIError, ResourceNotFoundError, ValidationError


class TestBugResources:
    """Bug资源测试类"""
    
    @pytest.fixture
    def mock_mcp(self):
        """创建模拟的MCP实例"""
        mcp = MagicMock()
        mcp.resource = MagicMock()
        return mcp
    
    @pytest.fixture
    def mock_context_and_client(self):
        """创建模拟的上下文和客户端"""
        context = MagicMock()
        server = MagicMock()
        client = AsyncMock()
        
        # 设置客户端上下文管理器
        async def client_context():
            yield client
        
        server._client_context = MagicMock(return_value=client_context())
        context.server = server
        return context, client
    
    @pytest.fixture
    def sample_bug_data(self):
        """示例Bug数据"""
        return {
            "id": 123,
            "title": "测试Bug标题",
            "status": "active",
            "severity": 2,
            "project": 456,
            "projectname": "测试项目",
            "assignedTo": "testuser",
            "assignedToEmpid": "EMP001",
            "openedBy": "reporter",
            "openedByEmpid": "EMP002",
            "openedDate": "2025-01-15 10:30:00",
            "resolution": "",
            "environment": "Beta",
            "steps": "1. 打开页面\n2. 点击按钮\n3. 观察错误"
        }
    
    def test_register_bug_resources(self, mock_mcp):
        """测试Bug资源注册"""
        register_bug_resources(mock_mcp)
        
        # 验证资源注册调用
        assert mock_mcp.resource.call_count >= 5  # 至少注册了5个资源
        
        # 验证资源URI
        resource_calls = [call[0][0] for call in mock_mcp.resource.call_args_list]
        expected_uris = [
            "zentao://bug/{bug_id}",
            "zentao://bugs",
            "zentao://project/{project_id}/bugs",
            "zentao://bugs/by-status/{status}",
            "zentao://bugs/by-severity/{severity}"
        ]
        
        for uri in expected_uris:
            assert uri in resource_calls
    
    @pytest.mark.asyncio
    async def test_get_bug_detail_success(self, mock_context_and_client, sample_bug_data):
        """测试获取Bug详情成功"""
        context, client = mock_context_and_client
        
        # 模拟API响应
        api_response = {
            "rsCode": "00000000",
            "msg": "正常返回",
            "body": {
                "data": sample_bug_data
            }
        }
        
        # 模拟客户端上下文管理器
        from unittest.mock import AsyncMock
        mock_client_context = AsyncMock()
        mock_client_context.__aenter__ = AsyncMock(return_value=client)
        mock_client_context.__aexit__ = AsyncMock(return_value=None)
        
        # 模拟get_zentao_client函数
        import zentao_mcp.tools.client_utils
        original_get_client = zentao_mcp.tools.client_utils.get_zentao_client
        zentao_mcp.tools.client_utils.get_zentao_client = lambda: mock_client_context
        
        try:
            client.get_bug_detail_async.return_value = api_response
            
            # 测试资源函数
            from zentao_mcp.resources.bug import register_bug_resources
            mock_mcp = MagicMock()
            registered_functions = {}
            
            def mock_resource(uri):
                def decorator(func):
                    registered_functions[uri] = func
                    return func
                return decorator
            
            mock_mcp.resource = mock_resource
            register_bug_resources(mock_mcp)
            
            # 调用get_bug_detail函数
            get_bug_func = registered_functions["zentao://bug/{bug_id}"]
            result = await get_bug_func(123, context)
            
            # 验证结果
            assert result is not None
            assert result["id"] == sample_bug_data["id"]
            assert result["title"] == sample_bug_data["title"]
            
        finally:
            # 恢复原始函数
            zentao_mcp.tools.client_utils.get_zentao_client = original_get_client
        
        # 调用函数
        result = await test_get_bug_detail(123, context)
        
        # 验证结果
        assert result["id"] == 123
        assert result["title"] == "测试Bug标题"
        assert result["status"] == "active"
        assert result["severity"] == 2
        assert result["project"] == 456
        assert result["projectname"] == "测试项目"
        
        # 验证API调用
        client.get_bug_detail_async.assert_called_once_with(123)
    
    @pytest.mark.asyncio
    async def test_get_bug_detail_not_found(self, mock_context_and_client):
        """测试获取不存在的Bug详情"""
        context, client = mock_context_and_client
        
        # 模拟API响应 - Bug不存在
        api_response = {
            "rsCode": "10000001",
            "msg": "Bug不存在",
            "body": {}
        }
        
        # 模拟客户端上下文管理器
        from unittest.mock import AsyncMock
        mock_client_context = AsyncMock()
        mock_client_context.__aenter__ = AsyncMock(return_value=client)
        mock_client_context.__aexit__ = AsyncMock(return_value=None)
        
        # 模拟get_zentao_client函数
        import zentao_mcp.tools.client_utils
        original_get_client = zentao_mcp.tools.client_utils.get_zentao_client
        zentao_mcp.tools.client_utils.get_zentao_client = lambda: mock_client_context
        
        try:
            client.get_bug_detail_async.return_value = api_response
            
            # 测试资源函数
            from zentao_mcp.resources.bug import register_bug_resources
            mock_mcp = MagicMock()
            registered_functions = {}
            
            def mock_resource(uri):
                def decorator(func):
                    registered_functions[uri] = func
                    return func
                return decorator
            
            mock_mcp.resource = mock_resource
            register_bug_resources(mock_mcp)
            
            # 调用get_bug_detail函数并验证异常
            get_bug_func = registered_functions["zentao://bug/{bug_id}"]
            with pytest.raises(ResourceNotFoundError):
                await get_bug_func(999, context)
                
        finally:
            # 恢复原始函数
            zentao_mcp.tools.client_utils.get_zentao_client = original_get_client
    
    @pytest.mark.asyncio
    async def test_get_bugs_by_project_success(self, mock_context_and_client, sample_bug_data):
        """测试根据项目获取Bug列表成功"""
        context, client = mock_context_and_client
        
        # 模拟API响应
        api_response = {
            "rsCode": "00000000",
            "msg": "正常返回",
            "body": [sample_bug_data]
        }
        
        # 模拟客户端上下文管理器
        from unittest.mock import AsyncMock
        mock_client_context = AsyncMock()
        mock_client_context.__aenter__ = AsyncMock(return_value=client)
        mock_client_context.__aexit__ = AsyncMock(return_value=None)
        
        # 模拟get_zentao_client函数
        import zentao_mcp.tools.client_utils
        original_get_client = zentao_mcp.tools.client_utils.get_zentao_client
        zentao_mcp.tools.client_utils.get_zentao_client = lambda: mock_client_context
        
        try:
            client.get_bug_by_project_async.return_value = api_response
            
            # 测试资源函数
            from zentao_mcp.resources.bug import register_bug_resources
            mock_mcp = MagicMock()
            registered_functions = {}
            
            def mock_resource(uri):
                def decorator(func):
                    registered_functions[uri] = func
                    return func
                return decorator
            
            mock_mcp.resource = mock_resource
            register_bug_resources(mock_mcp)
            
            # 调用get_bugs_by_project函数
            get_bugs_func = registered_functions["zentao://project/{project_id}/bugs"]
            result = await get_bugs_func(456, context)
            
            # 验证结果
            assert result is not None
            assert "bugs" in result
            assert "total" in result
            assert result["total"] == 1
            assert len(result["bugs"]) == 1
            
        finally:
            # 恢复原始函数
            zentao_mcp.tools.client_utils.get_zentao_client = original_get_client
        
        # 验证结果
        assert len(result) == 1
        assert result[0]["id"] == 123
        assert result[0]["title"] == "测试Bug标题"
        assert result[0]["project"] == 456
        
        # 验证API调用
        client.get_bugs_by_project_async.assert_called_once_with(456)
    
    def test_format_bug_data_success(self, sample_bug_data):
        """测试Bug数据格式化成功"""
        result = _format_bug_data(sample_bug_data)
        
        # 验证基本字段
        assert result["id"] == 123
        assert result["title"] == "测试Bug标题"
        assert result["status"] == "active"
        assert result["severity"] == 2
        assert result["project"] == 456
        assert result["projectname"] == "测试项目"
        assert result["assignedTo"] == "testuser"
        assert result["assignedToEmpid"] == "EMP001"
        assert result["openedBy"] == "reporter"
        assert result["openedByEmpid"] == "EMP002"
        assert result["resolution"] == ""
        assert result["environment"] == "Beta"
        assert result["steps"] == "1. 打开页面\n2. 点击按钮\n3. 观察错误"
        
        # 验证日期格式化
        assert result["openedDate"] != ""
    
    def test_format_bug_data_with_iso_date(self):
        """测试Bug数据格式化 - ISO日期格式"""
        bug_data = {
            "id": 123,
            "title": "测试Bug",
            "status": "active",
            "severity": 1,
            "project": 456,
            "projectname": "测试项目",
            "assignedTo": "testuser",
            "assignedToEmpid": "EMP001",
            "openedBy": "reporter",
            "openedByEmpid": "EMP002",
            "openedDate": "2025-01-15T10:30:00Z",
            "resolution": "",
            "environment": "Beta",
            "steps": ""
        }
        
        result = _format_bug_data(bug_data)
        
        # 验证日期格式化
        assert result["openedDate"] != ""
        assert "T" in result["openedDate"]  # ISO格式应该包含T
    
    def test_format_bug_data_missing_fields(self):
        """测试Bug数据格式化 - 缺少字段"""
        bug_data = {
            "id": 123,
            "title": "测试Bug"
        }
        
        result = _format_bug_data(bug_data)
        
        # 验证默认值
        assert result["id"] == 123
        assert result["title"] == "测试Bug"
        assert result["status"] == "active"
        assert result["severity"] == 2
        assert result["project"] == 0
        assert result["projectname"] == ""
        assert result["assignedTo"] == ""
        assert result["assignedToEmpid"] == ""
        assert result["openedBy"] == ""
        assert result["openedByEmpid"] == ""
        assert result["openedDate"] == ""
        assert result["resolution"] == ""
        assert result["environment"] == ""
        assert result["steps"] == ""
    
    def test_format_bug_data_invalid_types(self):
        """测试Bug数据格式化 - 无效数据类型"""
        bug_data = {
            "id": "invalid",  # 应该是int
            "title": None,    # 应该是str
            "severity": "high"  # 应该是int
        }
        
        result = _format_bug_data(bug_data)
        
        # 验证错误处理 - 应该返回基本结构
        assert "id" in result
        assert "title" in result
        assert result["title"] == "未知Bug"  # 错误时的默认标题
    
    def test_format_bug_data_with_extra_fields(self):
        """测试Bug数据格式化 - 包含额外字段"""
        bug_data = {
            "id": 123,
            "title": "测试Bug",
            "status": "active",
            "severity": 1,
            "project": 456,
            "projectname": "测试项目",
            "assignedTo": "testuser",
            "assignedToEmpid": "EMP001",
            "openedBy": "reporter",
            "openedByEmpid": "EMP002",
            "openedDate": "2025-01-15 10:30:00",
            "resolution": "",
            "environment": "Beta",
            "steps": "",
            # 额外字段
            "pri": 2,
            "type": "功能缺陷",
            "os": "Windows",
            "browser": "Chrome",
            "resolvedBy": "developer",
            "resolvedDate": "2025-01-16 15:00:00",
            "closedBy": "tester",
            "closedDate": "2025-01-17 09:00:00"
        }
        
        result = _format_bug_data(bug_data)
        
        # 验证额外字段被包含
        assert result["priority"] == 2
        assert result["type"] == "功能缺陷"
        assert result["os"] == "Windows"
        assert result["browser"] == "Chrome"
        assert result["resolvedBy"] == "developer"
        assert result["resolvedDate"] == "2025-01-16 15:00:00"
        assert result["closedBy"] == "tester"
        assert result["closedDate"] == "2025-01-17 09:00:00"