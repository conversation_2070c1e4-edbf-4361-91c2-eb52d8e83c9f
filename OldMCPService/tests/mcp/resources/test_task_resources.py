"""
任务资源模块测试

测试任务相关的MCP资源功能
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from fastmcp import Context

from zentao_mcp.resources.task import register_task_resources
from zentao_mcp.exceptions import ZentaoAPIError, ResourceNotFoundError


class TestTaskResources:
    """任务资源测试类"""
    
    @pytest.fixture
    def mock_context(self):
        """创建模拟上下文"""
        ctx = MagicMock(spec=Context)
        server = MagicMock()
        client = AsyncMock()
        
        # 设置客户端上下文管理器
        from contextlib import asynccontextmanager
        
        @asynccontextmanager
        async def client_context():
            yield client
        
        server._client_context = client_context
        ctx.server = server
        
        return ctx, client
    
    @pytest.fixture
    def mock_projects_response(self):
        """模拟项目列表响应"""
        return {
            "rsCode": "00000000",
            "msg": "正常返回",
            "body": [
                {
                    "id": 1,
                    "name": "测试项目1",
                    "code": "TEST1",
                    "status": "doing"
                },
                {
                    "id": 2,
                    "name": "测试项目2", 
                    "code": "TEST2",
                    "status": "done"
                }
            ]
        }
    
    @pytest.fixture
    def mock_tasks_response(self):
        """模拟任务列表响应"""
        return {
            "rsCode": "00000000",
            "msg": "正常返回",
            "body": {
                "data": [
                    {
                        "id": 1,
                        "name": "开发任务1",
                        "project": 1,
                        "projectname": "测试项目1",
                        "story": 10,
                        "storyname": "用户登录需求",
                        "type": "devel",
                        "status": "doing",
                        "pri": 2,
                        "estimate": 8.0,
                        "consumed": 4.0,
                        "left": 4.0,
                        "assignedTo": "developer1",
                        "assignedToEmpid": "EMP001",
                        "finishedBy": "",
                        "finishedByEmpid": "",
                        "openedBy": "pm1",
                        "openedDate": "2024-01-01 09:00:00"
                    },
                    {
                        "id": 2,
                        "name": "测试任务1",
                        "project": 1,
                        "projectname": "测试项目1",
                        "story": 10,
                        "storyname": "用户登录需求",
                        "type": "test",
                        "status": "wait",
                        "pri": 3,
                        "estimate": 4.0,
                        "consumed": 0.0,
                        "left": 4.0,
                        "assignedTo": "tester1",
                        "assignedToEmpid": "EMP002",
                        "finishedBy": "",
                        "finishedByEmpid": "",
                        "openedBy": "pm1",
                        "openedDate": "2024-01-01 10:00:00"
                    }
                ]
            }
        }
    
    @pytest.fixture
    def mock_task_detail_response(self):
        """模拟任务详情响应"""
        return {
            "rsCode": "00000000",
            "msg": "正常返回",
            "body": {
                "data": {
                    "id": 1,
                    "name": "开发任务1",
                    "project": 1,
                    "projectname": "测试项目1",
                    "story": 10,
                    "storyname": "用户登录需求",
                    "type": "devel",
                    "status": "doing",
                    "pri": 2,
                    "estimate": 8.0,
                    "consumed": 4.0,
                    "left": 4.0,
                    "assignedTo": "developer1",
                    "assignedToEmpid": "EMP001",
                    "finishedBy": "",
                    "finishedByEmpid": "",
                    "openedBy": "pm1",
                    "openedDate": "2024-01-01 09:00:00",
                    "desc": "实现用户登录功能",
                    "version": 1,
                    "keywords": "登录,认证"
                }
            }
        }
    
    @pytest.mark.asyncio
    async def test_list_all_tasks(self, mock_context, mock_projects_response, mock_tasks_response):
        """测试获取所有任务列表"""
        ctx, client = mock_context
        
        # 设置模拟响应
        client.get_all_projects_async.return_value = mock_projects_response
        client.get_tasks_by_project_async.return_value = mock_tasks_response
        
        # 创建MCP应用并注册资源
        mcp = MagicMock()
        resources = {}
        
        def mock_resource(uri):
            def decorator(func):
                resources[uri] = func
                return func
            return decorator
        
        mcp.resource = mock_resource
        register_task_resources(mcp)
        
        # 调用资源方法
        result = await resources["zentao://tasks"](ctx)
        
        # 验证结果
        assert isinstance(result, list)
        assert len(result) == 4  # 2个项目 × 2个任务
        
        # 验证任务数据结构
        task = result[0]
        assert task["id"] == 1
        assert task["name"] == "开发任务1"
        assert task["project"] == 1
        assert task["story"] == 10
        assert task["type"] == "devel"
        assert task["status"] == "doing"
        assert task["estimate"] == 8.0
        assert task["consumed"] == 4.0
        assert task["left"] == 4.0
    
    @pytest.mark.asyncio
    async def test_get_task_detail(self, mock_context, mock_task_detail_response):
        """测试获取任务详情"""
        ctx, client = mock_context
        
        # 设置模拟响应
        client.get_task_by_id_async.return_value = mock_task_detail_response
        
        # 创建MCP应用并注册资源
        mcp = MagicMock()
        resources = {}
        
        def mock_resource(uri):
            def decorator(func):
                resources[uri] = func
                return func
            return decorator
        
        mcp.resource = mock_resource
        register_task_resources(mcp)
        
        # 调用资源方法
        result = await resources["zentao://task/{task_id}"](1, ctx)
        
        # 验证结果
        assert isinstance(result, dict)
        assert result["id"] == 1
        assert result["name"] == "开发任务1"
        assert result["desc"] == "实现用户登录功能"
        assert result["keywords"] == "登录,认证"
        assert result["version"] == 1
    
    @pytest.mark.asyncio
    async def test_get_task_detail_not_found(self, mock_context):
        """测试获取不存在的任务详情"""
        ctx, client = mock_context
        
        # 设置模拟响应 - 任务不存在
        client.get_task_by_id_async.return_value = {
            "rsCode": "10000001",
            "msg": "任务不存在",
            "body": None
        }
        
        # 创建MCP应用并注册资源
        mcp = MagicMock()
        resources = {}
        
        def mock_resource(uri):
            def decorator(func):
                resources[uri] = func
                return func
            return decorator
        
        mcp.resource = mock_resource
        register_task_resources(mcp)
        
        # 调用资源方法，应该抛出异常
        with pytest.raises(ResourceNotFoundError):
            await resources["zentao://task/{task_id}"](999, ctx)
    
    @pytest.mark.asyncio
    async def test_get_tasks_by_project(self, mock_context, mock_tasks_response):
        """测试根据项目ID获取任务列表"""
        ctx, client = mock_context
        
        # 设置模拟响应
        client.get_tasks_by_project_async.return_value = mock_tasks_response
        
        # 创建MCP应用并注册资源
        mcp = MagicMock()
        resources = {}
        
        def mock_resource(uri):
            def decorator(func):
                resources[uri] = func
                return func
            return decorator
        
        mcp.resource = mock_resource
        register_task_resources(mcp)
        
        # 调用资源方法
        result = await resources["zentao://project/{project_id}/tasks"](1, ctx)
        
        # 验证结果
        assert isinstance(result, list)
        assert len(result) == 2
        
        # 验证任务数据
        task1 = result[0]
        assert task1["id"] == 1
        assert task1["project"] == 1
        assert task1["type"] == "devel"
        
        task2 = result[1]
        assert task2["id"] == 2
        assert task2["project"] == 1
        assert task2["type"] == "test"
    
    @pytest.mark.asyncio
    async def test_get_tasks_by_status_invalid(self, mock_context):
        """测试使用无效状态获取任务列表"""
        ctx, client = mock_context
        
        # 创建MCP应用并注册资源
        mcp = MagicMock()
        resources = {}
        
        def mock_resource(uri):
            def decorator(func):
                resources[uri] = func
                return func
            return decorator
        
        mcp.resource = mock_resource
        register_task_resources(mcp)
        
        # 调用资源方法，使用无效状态
        with pytest.raises(ValueError, match="无效的任务状态"):
            await resources["zentao://tasks/by-status/{status}"]("invalid_status", ctx)
    
    @pytest.mark.asyncio
    async def test_get_tasks_by_type_invalid(self, mock_context):
        """测试使用无效类型获取任务列表"""
        ctx, client = mock_context
        
        # 创建MCP应用并注册资源
        mcp = MagicMock()
        resources = {}
        
        def mock_resource(uri):
            def decorator(func):
                resources[uri] = func
                return func
            return decorator
        
        mcp.resource = mock_resource
        register_task_resources(mcp)
        
        # 调用资源方法，使用无效类型
        with pytest.raises(ValueError, match="无效的任务类型"):
            await resources["zentao://tasks/by-type/{task_type}"]("invalid_type", ctx)
    
    @pytest.mark.asyncio
    async def test_get_tasks_by_priority_invalid(self, mock_context):
        """测试使用无效优先级获取任务列表"""
        ctx, client = mock_context
        
        # 创建MCP应用并注册资源
        mcp = MagicMock()
        resources = {}
        
        def mock_resource(uri):
            def decorator(func):
                resources[uri] = func
                return func
            return decorator
        
        mcp.resource = mock_resource
        register_task_resources(mcp)
        
        # 调用资源方法，使用无效优先级
        with pytest.raises(ValueError, match="无效的优先级"):
            await resources["zentao://tasks/by-priority/{priority}"](5, ctx)
    
    @pytest.mark.asyncio
    async def test_api_error_handling(self, mock_context):
        """测试API错误处理"""
        ctx, client = mock_context
        
        # 设置模拟异常
        client.get_all_projects_async.side_effect = Exception("网络连接失败")
        
        # 创建MCP应用并注册资源
        mcp = MagicMock()
        resources = {}
        
        def mock_resource(uri):
            def decorator(func):
                resources[uri] = func
                return func
            return decorator
        
        mcp.resource = mock_resource
        register_task_resources(mcp)
        
        # 调用资源方法，应该抛出ZentaoAPIError
        with pytest.raises(ZentaoAPIError, match="获取任务列表失败"):
            await resources["zentao://tasks"](ctx)