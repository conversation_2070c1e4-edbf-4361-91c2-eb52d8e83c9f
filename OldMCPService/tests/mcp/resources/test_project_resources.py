"""
项目资源模块测试
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from fastmcp import Context

from zentao_mcp.resources.project import register_project_resources
from zentao_mcp.exceptions import ZentaoAPIError, ResourceNotFoundError


class TestProjectResources:
    """项目资源测试类"""
    
    @pytest.fixture
    def mock_mcp(self):
        """创建模拟的MCP实例"""
        mcp = MagicMock()
        mcp.resource = MagicMock()
        return mcp
    
    @pytest.fixture
    def mock_context(self):
        """创建模拟的上下文"""
        context = MagicMock(spec=Context)
        server = MagicMock()
        context.server = server
        return context
    
    @pytest.fixture
    def sample_projects_response(self):
        """示例项目响应数据"""
        return {
            "rsCode": "00000000",
            "msg": "正常返回",
            "body": [
                {
                    "id": 1,
                    "name": "测试项目1",
                    "code": "TEST001",
                    "begin": "2024-01-01",
                    "end": "2024-12-31",
                    "status": "doing",
                    "parent": 0,
                    "openedBy": "admin",
                    "closedDate": None,
                    "closedBy": ""
                },
                {
                    "id": 2,
                    "name": "测试项目2",
                    "code": "TEST002",
                    "begin": "2024-02-01",
                    "end": "2024-11-30",
                    "status": "done",
                    "parent": 0,
                    "openedBy": "user1",
                    "closedDate": "2024-11-30",
                    "closedBy": "admin"
                },
                {
                    "id": 3,
                    "name": "子项目1",
                    "code": "SUB001",
                    "begin": "2024-03-01",
                    "end": "2024-09-30",
                    "status": "wait",
                    "parent": 1,
                    "openedBy": "user2",
                    "closedDate": None,
                    "closedBy": ""
                }
            ]
        }
    
    def test_register_project_resources(self, mock_mcp):
        """测试项目资源注册"""
        register_project_resources(mock_mcp)
        
        # 验证资源注册调用
        assert mock_mcp.resource.call_count >= 6  # 至少注册了6个资源
        
        # 验证注册的资源URI
        registered_uris = [call[0][0] for call in mock_mcp.resource.call_args_list]
        expected_uris = [
            "zentao://projects",
            "zentao://project/{project_id}",
            "zentao://projects/by-status/{status}",
            "zentao://projects/active",
            "zentao://projects/by-parent/{parent_id}",
            "zentao://projects/summary"
        ]
        
        for uri in expected_uris:
            assert uri in registered_uris
    
    @pytest.mark.asyncio
    async def test_list_all_projects_success(self, mock_context, sample_projects_response):
        """测试成功获取所有项目列表"""
        # 设置模拟客户端
        mock_client = AsyncMock()
        mock_client.get_all_projects_async.return_value = sample_projects_response
        
        mock_context.server._client_context.return_value.__aenter__.return_value = mock_client
        
        # 注册资源并获取函数
        mock_mcp = MagicMock()
        registered_functions = {}
        
        def mock_resource(uri):
            def decorator(func):
                registered_functions[uri] = func
                return func
            return decorator
        
        mock_mcp.resource = mock_resource
        register_project_resources(mock_mcp)
        
        # 调用列表函数
        list_projects_func = registered_functions["zentao://projects"]
        result = await list_projects_func(mock_context)
        
        # 验证结果
        assert len(result) == 3
        assert result[0]["id"] == 1
        assert result[0]["name"] == "测试项目1"
        assert result[0]["status"] == "doing"
        assert result[1]["id"] == 2
        assert result[1]["status"] == "done"
        assert result[2]["parent"] == 1
    
    @pytest.mark.asyncio
    async def test_get_project_detail_success(self, mock_context, sample_projects_response):
        """测试成功获取项目详情"""
        # 设置模拟客户端
        mock_client = AsyncMock()
        mock_client.get_all_projects_async.return_value = sample_projects_response
        
        mock_context.server._client_context.return_value.__aenter__.return_value = mock_client
        
        # 注册资源并获取函数
        mock_mcp = MagicMock()
        registered_functions = {}
        
        def mock_resource(uri):
            def decorator(func):
                registered_functions[uri] = func
                return func
            return decorator
        
        mock_mcp.resource = mock_resource
        register_project_resources(mock_mcp)
        
        # 调用详情函数
        get_project_func = registered_functions["zentao://project/{project_id}"]
        result = await get_project_func(1, mock_context)
        
        # 验证结果
        assert result["id"] == 1
        assert result["name"] == "测试项目1"
        assert result["status"] == "doing"
    
    @pytest.mark.asyncio
    async def test_get_project_detail_not_found(self, mock_context, sample_projects_response):
        """测试获取不存在的项目详情"""
        # 设置模拟客户端
        mock_client = AsyncMock()
        mock_client.get_all_projects_async.return_value = sample_projects_response
        
        mock_context.server._client_context.return_value.__aenter__.return_value = mock_client
        
        # 注册资源并获取函数
        mock_mcp = MagicMock()
        registered_functions = {}
        
        def mock_resource(uri):
            def decorator(func):
                registered_functions[uri] = func
                return func
            return decorator
        
        mock_mcp.resource = mock_resource
        register_project_resources(mock_mcp)
        
        # 调用详情函数，查询不存在的项目
        get_project_func = registered_functions["zentao://project/{project_id}"]
        
        with pytest.raises(ResourceNotFoundError):
            await get_project_func(999, mock_context)
    
    @pytest.mark.asyncio
    async def test_get_projects_by_status(self, mock_context, sample_projects_response):
        """测试根据状态获取项目列表"""
        # 设置模拟客户端
        mock_client = AsyncMock()
        mock_client.get_all_projects_async.return_value = sample_projects_response
        
        mock_context.server._client_context.return_value.__aenter__.return_value = mock_client
        
        # 注册资源并获取函数
        mock_mcp = MagicMock()
        registered_functions = {}
        
        def mock_resource(uri):
            def decorator(func):
                registered_functions[uri] = func
                return func
            return decorator
        
        mock_mcp.resource = mock_resource
        register_project_resources(mock_mcp)
        
        # 测试获取doing状态的项目
        get_by_status_func = registered_functions["zentao://projects/by-status/{status}"]
        result = await get_by_status_func("doing", mock_context)
        
        # 验证结果
        assert len(result) == 1
        assert result[0]["id"] == 1
        assert result[0]["status"] == "doing"
        
        # 测试获取done状态的项目
        result = await get_by_status_func("done", mock_context)
        assert len(result) == 1
        assert result[0]["id"] == 2
        assert result[0]["status"] == "done"
    
    @pytest.mark.asyncio
    async def test_get_projects_by_parent(self, mock_context, sample_projects_response):
        """测试根据父级项目获取子项目列表"""
        # 设置模拟客户端
        mock_client = AsyncMock()
        mock_client.get_all_projects_async.return_value = sample_projects_response
        
        mock_context.server._client_context.return_value.__aenter__.return_value = mock_client
        
        # 注册资源并获取函数
        mock_mcp = MagicMock()
        registered_functions = {}
        
        def mock_resource(uri):
            def decorator(func):
                registered_functions[uri] = func
                return func
            return decorator
        
        mock_mcp.resource = mock_resource
        register_project_resources(mock_mcp)
        
        # 调用子项目函数
        get_by_parent_func = registered_functions["zentao://projects/by-parent/{parent_id}"]
        result = await get_by_parent_func(1, mock_context)
        
        # 验证结果
        assert len(result) == 1
        assert result[0]["id"] == 3
        assert result[0]["parent"] == 1
        assert result[0]["name"] == "子项目1"
    
    @pytest.mark.asyncio
    async def test_get_projects_summary(self, mock_context, sample_projects_response):
        """测试获取项目汇总信息"""
        # 设置模拟客户端
        mock_client = AsyncMock()
        mock_client.get_all_projects_async.return_value = sample_projects_response
        
        mock_context.server._client_context.return_value.__aenter__.return_value = mock_client
        
        # 注册资源并获取函数
        mock_mcp = MagicMock()
        registered_functions = {}
        
        def mock_resource(uri):
            def decorator(func):
                registered_functions[uri] = func
                return func
            return decorator
        
        mock_mcp.resource = mock_resource
        register_project_resources(mock_mcp)
        
        # 调用汇总函数
        get_summary_func = registered_functions["zentao://projects/summary"]
        result = await get_summary_func(mock_context)
        
        # 验证结果
        assert result["total_projects"] == 3
        assert result["status_distribution"]["doing"] == 1
        assert result["status_distribution"]["done"] == 1
        assert result["status_distribution"]["wait"] == 1
        assert result["status_distribution"]["suspended"] == 0
        assert result["completion_rate"] == 33.33  # 1/3 * 100
        assert result["active_projects"] == 1
        assert result["completed_projects"] == 1
    
    @pytest.mark.asyncio
    async def test_api_error_handling(self, mock_context):
        """测试API错误处理"""
        # 设置模拟客户端返回错误
        mock_client = AsyncMock()
        mock_client.get_all_projects_async.return_value = {
            "rsCode": "99999999",
            "msg": "API错误",
            "body": []
        }
        
        mock_context.server._client_context.return_value.__aenter__.return_value = mock_client
        
        # 注册资源并获取函数
        mock_mcp = MagicMock()
        registered_functions = {}
        
        def mock_resource(uri):
            def decorator(func):
                registered_functions[uri] = func
                return func
            return decorator
        
        mock_mcp.resource = mock_resource
        register_project_resources(mock_mcp)
        
        # 调用列表函数，应该返回空列表而不是抛出异常
        list_projects_func = registered_functions["zentao://projects"]
        result = await list_projects_func(mock_context)
        
        # 验证结果
        assert result == []
    
    @pytest.mark.asyncio
    async def test_invalid_status_error(self, mock_context, sample_projects_response):
        """测试无效状态参数错误"""
        # 设置模拟客户端
        mock_client = AsyncMock()
        mock_client.get_all_projects_async.return_value = sample_projects_response
        
        mock_context.server._client_context.return_value.__aenter__.return_value = mock_client
        
        # 注册资源并获取函数
        mock_mcp = MagicMock()
        registered_functions = {}
        
        def mock_resource(uri):
            def decorator(func):
                registered_functions[uri] = func
                return func
            return decorator
        
        mock_mcp.resource = mock_resource
        register_project_resources(mock_mcp)
        
        # 调用状态过滤函数，使用无效状态
        get_by_status_func = registered_functions["zentao://projects/by-status/{status}"]
        
        with pytest.raises(ValueError):
            await get_by_status_func("invalid_status", mock_context)