import os
import pytest
import logging
import datetime
import json
from dotenv import load_dotenv
import asyncio
from zentao_mcp.client import ZentaoClient

# 加载环境变量
load_dotenv()

# 存储API请求信息的全局字典
api_requests_info = {}

def pytest_addoption(parser):
    parser.addoption("--env", action="store", default=os.getenv("ENVIRONMENT", "beta"),
                     help="指定测试环境: beta, online")
    parser.addoption("--log-dir", action="store", default="logs",
                     help="指定日志目录")

@pytest.fixture(scope="session")
def environment(request):
    return request.config.getoption("--env")

@pytest.fixture(scope="session")
def log_dir(request):
    return request.config.getoption("--log-dir")

@pytest.fixture(scope="session")
def api_client(environment, log_dir, request):
    """创建API客户端实例"""
    global current_test_log_dir
    
    domains = {
        "beta": os.getenv("BETA_DOMAIN", "newzentao-api.beta1.fn"),
        "online": os.getenv("ONLINE_DOMAIN", "newzentao-api.idc1.fn")
    }
    
    domain = domains.get(environment)
    if not domain:
        raise ValueError(f"未知的环境: {environment}，请使用 beta 或 online")
    
    # 使用当前测试运行的日志目录
    client_log_dir = current_test_log_dir if current_test_log_dir else log_dir
    
    # 创建API客户端，并传入请求回调函数
    client = ZentaoClient(domain, client_log_dir, request_callback=record_api_request)
    return client

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环用于异步测试"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
def test_data():
    """测试数据"""
    return {
        "bug_id": 184213,
        "project_id": 143,
        "task_id": 371999,
        "story_ids": ["28673", "12345"],
        "time_range": {
            "startDate": "2025-04-02 14:04:07",
            "endDate": "2025-07-09 10:54:54"
        },
        "dept_id": 41
    }

# 记录API请求信息的回调函数
def record_api_request(request_id, method, url, headers, data, status_code, response_data):
    """记录API请求信息"""
    api_requests_info[request_id] = {
        "method": method,
        "url": url,
        "headers": headers,
        "data": data,
        "status_code": status_code,
        "response_data": response_data
    }

# 存储当前测试运行的日志目录
current_test_log_dir = None

# 测试会话开始和结束的钩子函数
def pytest_sessionstart(session):
    """测试会话开始时的操作"""
    global current_test_log_dir
    
    # 确保日志目录存在
    log_dir = session.config.getoption("--log-dir")
    os.makedirs(log_dir, exist_ok=True)
    
    # 为每次测试运行创建一个单独的日志文件夹
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    current_test_log_dir = os.path.join(log_dir, f"test_run_{timestamp}")
    os.makedirs(current_test_log_dir, exist_ok=True)
    
    # 创建会话日志
    env = session.config.getoption("--env")
    session_log_file = os.path.join(current_test_log_dir, f"session_{env}.log")
    
    # 配置会话日志记录器
    session_logger = logging.getLogger("pytest_session")
    session_logger.setLevel(logging.INFO)
    
    # 清除已有的处理器
    if session_logger.handlers:
        for handler in session_logger.handlers:
            session_logger.removeHandler(handler)
    
    # 添加文件处理器
    file_handler = logging.FileHandler(session_log_file, encoding='utf-8')
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    session_logger.addHandler(file_handler)
    
    session_logger.info(f"测试会话开始 - 环境: {env}")

def pytest_sessionfinish(session, exitstatus):
    """测试会话结束时的操作"""
    session_logger = logging.getLogger("pytest_session")
    env = session.config.getoption("--env")
    session_logger.info(f"测试会话结束 - 环境: {env}, 退出状态: {exitstatus}")

# 测试用例开始前的钩子函数
@pytest.hookimpl(tryfirst=True)
def pytest_runtest_setup(item):
    # 清除之前的API请求信息
    api_requests_info.clear()

# 测试用例结束后的钩子函数
@pytest.hookimpl(trylast=True)
def pytest_runtest_teardown(item, nextitem):
    # 将API请求信息附加到测试用例
    if hasattr(item, "_report"):
        item._report.api_requests = api_requests_info.copy()

# 修改HTML报告的表头
@pytest.hookimpl(hookwrapper=True)
def pytest_runtest_makereport(item, call):
    outcome = yield
    report = outcome.get_result()
    
    # 保存报告到测试项
    if call.when == "call":
        setattr(item, "_report", report)
        
        # 如果有API请求信息，添加到报告的额外信息中
        if hasattr(item, "_report") and hasattr(item, "api_requests"):
            report.api_requests = getattr(item, "api_requests", {})

def pytest_configure(config):
    """配置pytest，仅在安装了pytest-html插件时添加HTML相关功能"""
    try:
        # 检查是否安装了pytest-html插件
        import pytest_html
        
        # 如果安装了pytest-html，则添加相关钩子函数
        def pytest_html_results_table_header(cells):
            cells.insert(2, '<th>API请求</th>')

        def pytest_html_results_table_row(report, cells):
            # 添加API请求信息
            api_requests_cell = '<td class="api-requests">'
            
            if hasattr(report, "api_requests") and report.api_requests:
                api_requests_cell += '<button onclick="toggleApiRequests(this)">显示API请求</button>'
                api_requests_cell += '<div class="api-requests-details" style="display:none;">'
                
                for req_id, req_info in report.api_requests.items():
                    api_requests_cell += '<div class="api-request">'
                    api_requests_cell += f'<p><strong>请求方法:</strong> {req_info["method"]}</p>'
                    api_requests_cell += f'<p><strong>请求URL:</strong> {req_info["url"]}</p>'
                    
                    # 添加请求数据（入参）
                    if req_info.get("data"):
                        api_requests_cell += '<p><strong>请求数据:</strong></p>'
                        api_requests_cell += f'<pre>{json.dumps(req_info["data"], ensure_ascii=False, indent=2)}</pre>'
                    
                    # 添加响应状态码
                    api_requests_cell += f'<p><strong>响应状态码:</strong> {req_info.get("status_code", "N/A")}</p>'
                    
                    # 添加响应数据（出参）
                    if req_info.get("response_data"):
                        api_requests_cell += '<p><strong>响应数据:</strong></p>'
                        api_requests_cell += f'<pre>{json.dumps(req_info["response_data"], ensure_ascii=False, indent=2)}</pre>'
                    
                    api_requests_cell += '</div>'
                
                api_requests_cell += '</div>'
            else:
                api_requests_cell += '无API请求'
            
            api_requests_cell += '</td>'
            cells.insert(2, api_requests_cell)

        def pytest_html_report_title(report):
            report.title = "禅道API测试报告"
        
        # 动态注册钩子函数
        config.pluginmanager.register(type('HTMLHooks', (), {
            'pytest_html_results_table_header': pytest_html_results_table_header,
            'pytest_html_results_table_row': pytest_html_results_table_row,
            'pytest_html_report_title': pytest_html_report_title
        })())
        
        # 添加自定义CSS和JavaScript
        if hasattr(config, '_html'):
            config._html.style_css += """
            .api-requests-details {
                margin-top: 10px;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #f9f9f9;
                max-height: 300px;
                overflow-y: auto;
            }
            .api-request {
                margin-bottom: 15px;
                padding-bottom: 15px;
                border-bottom: 1px solid #eee;
            }
            .api-request:last-child {
                margin-bottom: 0;
                padding-bottom: 0;
                border-bottom: none;
            }
            pre {
                background-color: #f5f5f5;
                padding: 10px;
                border-radius: 4px;
                overflow-x: auto;
            }
            """
            
            config._html.scripts_js += """
            function toggleApiRequests(button) {
                var details = button.nextElementSibling;
                if (details.style.display === "none") {
                    details.style.display = "block";
                    button.textContent = "隐藏API请求";
                } else {
                    details.style.display = "none";
                    button.textContent = "显示API请求";
                }
            }
            """
    except ImportError:
        # 如果没有安装pytest-html插件，则跳过HTML相关功能
        pass
    except Exception:
        # 其他异常也跳过
        pass

# Mock机制和测试数据管理

@pytest.fixture
def mock_zentao_responses():
    """标准化的禅道API响应Mock数据"""
    return {
        "departments": {
            "rsCode": "********",
            "msg": "正常返回",
            "body": [
                {"id": 1, "name": "开发部门", "parent": 0, "grade": 1},
                {"id": 2, "name": "测试部门", "parent": 0, "grade": 1},
                {"id": 3, "name": "前端组", "parent": 1, "grade": 2},
                {"id": 4, "name": "后端组", "parent": 1, "grade": 2}
            ]
        },
        "projects": {
            "rsCode": "********",
            "msg": "正常返回",
            "body": [
                {
                    "id": 143,
                    "name": "测试项目A",
                    "status": "doing",
                    "begin": "2025-01-01",
                    "end": "2025-12-31",
                    "team": "开发部门"
                },
                {
                    "id": 144,
                    "name": "演示项目B",
                    "status": "done",
                    "begin": "2024-01-01",
                    "end": "2024-12-31",
                    "team": "测试部门"
                }
            ]
        },
        "stories": {
            "rsCode": "********",
            "msg": "正常返回",
            "body": [
                {
                    "id": 28673,
                    "title": "用户登录功能开发",
                    "status": "active",
                    "stage": "developing",
                    "pri": 1,
                    "estimate": 16.0,
                    "project": 143,
                    "projectname": "测试项目A",
                    "openedBy": "developer1",
                    "assignedTo": "developer2"
                },
                {
                    "id": 28674,
                    "title": "用户注册功能开发",
                    "status": "closed",
                    "stage": "released",
                    "pri": 2,
                    "estimate": 12.0,
                    "project": 143,
                    "projectname": "测试项目A",
                    "openedBy": "developer1",
                    "assignedTo": "developer3"
                }
            ]
        },
        "tasks": {
            "rsCode": "********",
            "msg": "正常返回",
            "body": [
                {
                    "id": 371999,
                    "name": "实现登录接口",
                    "status": "doing",
                    "type": "devel",
                    "estimate": 8.0,
                    "consumed": 6.0,
                    "left": 2.0,
                    "assignedTo": "developer2",
                    "story": 28673,
                    "project": 143
                },
                {
                    "id": 372000,
                    "name": "登录页面开发",
                    "status": "done",
                    "type": "devel",
                    "estimate": 8.0,
                    "consumed": 8.0,
                    "left": 0.0,
                    "assignedTo": "developer3",
                    "story": 28673,
                    "project": 143
                }
            ]
        },
        "bugs": {
            "rsCode": "********",
            "msg": "正常返回",
            "body": [
                {
                    "id": 184213,
                    "title": "登录页面显示异常",
                    "status": "active",
                    "severity": 1,
                    "pri": 1,
                    "project": 143,
                    "projectname": "测试项目A",
                    "assignedTo": "developer2",
                    "openedBy": "tester1",
                    "openedDate": "2025-01-15 10:30:00"
                },
                {
                    "id": 184214,
                    "title": "注册功能验证错误",
                    "status": "resolved",
                    "severity": 2,
                    "pri": 2,
                    "project": 143,
                    "projectname": "测试项目A",
                    "assignedTo": "developer3",
                    "openedBy": "tester2",
                    "openedDate": "2025-01-16 14:20:00"
                }
            ]
        },
        "users": {
            "rsCode": "********",
            "msg": "正常返回",
            "body": [
                {
                    "id": 1,
                    "account": "developer1",
                    "realname": "开发者1",
                    "email": "<EMAIL>",
                    "dept": 1,
                    "role": "dev"
                },
                {
                    "id": 2,
                    "account": "developer2",
                    "realname": "开发者2",
                    "email": "<EMAIL>",
                    "dept": 1,
                    "role": "dev"
                },
                {
                    "id": 3,
                    "account": "tester1",
                    "realname": "测试员1",
                    "email": "<EMAIL>",
                    "dept": 2,
                    "role": "qa"
                }
            ]
        }
    }

@pytest.fixture
def mock_error_responses():
    """错误响应Mock数据"""
    return {
        "api_error": {
            "rsCode": "********",
            "msg": "系统内部错误",
            "body": None
        },
        "auth_error": {
            "rsCode": "********",
            "msg": "认证失败",
            "body": None
        },
        "not_found": {
            "rsCode": "********",
            "msg": "数据不存在",
            "body": []
        },
        "permission_denied": {
            "rsCode": "********",
            "msg": "权限不足",
            "body": None
        }
    }

@pytest.fixture
def mock_zentao_client_factory(mock_zentao_responses, mock_error_responses):
    """创建配置好的Mock禅道客户端工厂"""
    def create_mock_client(scenario="success"):
        """
        创建Mock客户端
        
        Args:
            scenario: 测试场景 ("success", "api_error", "auth_error", "not_found", "permission_denied")
        """
        client = AsyncMock()
        
        if scenario == "success":
            # 配置成功响应
            client.get_all_departments_async.return_value = mock_zentao_responses["departments"]
            client.get_all_projects_async.return_value = mock_zentao_responses["projects"]
            client.get_stories_by_project_async.return_value = mock_zentao_responses["stories"]
            client.get_tasks_by_project_async.return_value = mock_zentao_responses["tasks"]
            client.get_bugs_by_project_async.return_value = mock_zentao_responses["bugs"]
            client.get_users_by_department_async.return_value = mock_zentao_responses["users"]
            client.get_story_info_async.return_value = mock_zentao_responses["stories"]
            client.get_task_detail_async.return_value = {
                "rsCode": "********",
                "msg": "正常返回",
                "body": mock_zentao_responses["tasks"]["body"][0]
            }
            client.get_bug_detail_async.return_value = {
                "rsCode": "********",
                "msg": "正常返回",
                "body": mock_zentao_responses["bugs"]["body"][0]
            }
        elif scenario == "api_error":
            # 配置API错误响应
            error_response = mock_error_responses["api_error"]
            client.get_all_departments_async.return_value = error_response
            client.get_all_projects_async.return_value = error_response
            client.get_stories_by_project_async.return_value = error_response
        elif scenario == "network_error":
            # 配置网络错误
            client.get_all_departments_async.side_effect = Exception("网络连接失败")
            client.get_all_projects_async.side_effect = TimeoutError("请求超时")
        elif scenario == "empty_response":
            # 配置空响应
            client.get_all_departments_async.return_value = None
            client.get_all_projects_async.return_value = {}
        
        return client
    
    return create_mock_client

@pytest.fixture
def sample_analysis_data():
    """分析功能测试数据"""
    return {
        "project_analysis": {
            "project_info": {
                "id": 143,
                "name": "测试项目A",
                "status": "doing"
            },
            "stories_data": [
                {
                    "id": 28673,
                    "title": "用户登录功能",
                    "status": "active",
                    "stage": "developing",
                    "estimate": 16.0,
                    "pri": 1
                },
                {
                    "id": 28674,
                    "title": "用户注册功能",
                    "status": "closed",
                    "stage": "released",
                    "estimate": 12.0,
                    "pri": 2
                }
            ],
            "tasks_data": [
                {
                    "id": 371999,
                    "name": "实现登录接口",
                    "status": "doing",
                    "type": "devel",
                    "estimate": 8.0,
                    "consumed": 6.0,
                    "left": 2.0
                },
                {
                    "id": 372000,
                    "name": "登录页面开发",
                    "status": "done",
                    "type": "devel",
                    "estimate": 8.0,
                    "consumed": 8.0,
                    "left": 0.0
                }
            ],
            "bugs_data": [
                {
                    "id": 184213,
                    "title": "登录页面异常",
                    "status": "active",
                    "severity": 1,
                    "pri": 1
                }
            ]
        },
        "workload_analysis": {
            "personnel_workload": {
                "developer1": {
                    "stories_created": 5,
                    "stories_assigned": 3,
                    "tasks_assigned": 8,
                    "bugs_assigned": 2,
                    "total_story_estimate": 40.0,
                    "total_task_estimate": 32.0,
                    "total_task_consumed": 28.0,
                    "total_task_left": 4.0
                },
                "developer2": {
                    "stories_created": 3,
                    "stories_assigned": 2,
                    "tasks_assigned": 5,
                    "bugs_assigned": 1,
                    "total_story_estimate": 24.0,
                    "total_task_estimate": 20.0,
                    "total_task_consumed": 18.0,
                    "total_task_left": 2.0
                }
            }
        },
        "bug_analysis": {
            "severity_distribution": {
                0: 2,  # 冒烟
                1: 5,  # 严重
                2: 8,  # 一般
                3: 3,  # 次要
                4: 1   # 低级
            },
            "status_distribution": {
                "active": 12,
                "resolved": 5,
                "closed": 2,
                "released": 1
            },
            "project_distribution": {
                "测试项目A": 15,
                "演示项目B": 5
            }
        }
    }

@pytest.fixture
def performance_test_data():
    """性能测试数据生成器"""
    def generate_large_dataset(size=1000, data_type="stories"):
        """生成大量测试数据"""
        if data_type == "stories":
            return [
                {
                    "id": i,
                    "title": f"需求{i}",
                    "status": "active" if i % 2 == 0 else "closed",
                    "stage": "developing" if i % 3 == 0 else "released",
                    "estimate": float(i % 20 + 1),
                    "pri": (i % 4) + 1,
                    "project": (i % 10) + 100
                }
                for i in range(1, size + 1)
            ]
        elif data_type == "tasks":
            return [
                {
                    "id": i,
                    "name": f"任务{i}",
                    "status": "doing" if i % 3 == 0 else "done",
                    "type": "devel" if i % 2 == 0 else "test",
                    "estimate": float(i % 16 + 1),
                    "consumed": float(i % 12 + 1),
                    "left": float(max(0, (i % 16 + 1) - (i % 12 + 1))),
                    "story": (i % 100) + 1000,
                    "project": (i % 10) + 100
                }
                for i in range(1, size + 1)
            ]
        elif data_type == "bugs":
            return [
                {
                    "id": i,
                    "title": f"Bug{i}",
                    "status": ["active", "resolved", "closed"][i % 3],
                    "severity": (i % 5),
                    "pri": (i % 4) + 1,
                    "project": (i % 10) + 100,
                    "assignedTo": f"developer{(i % 5) + 1}"
                }
                for i in range(1, size + 1)
            ]
        else:
            return []
    
    return generate_large_dataset

# 测试数据验证辅助函数
def validate_zentao_response(response, expected_fields=None):
    """验证禅道API响应格式"""
    if expected_fields is None:
        expected_fields = ["rsCode", "msg", "body"]
    
    assert isinstance(response, dict), "响应应该是字典格式"
    
    for field in expected_fields:
        assert field in response, f"响应中缺少必需字段: {field}"
    
    assert response["rsCode"] == "********", f"响应码错误: {response.get('rsCode')}"
    assert isinstance(response["body"], (list, dict, type(None))), "body字段格式错误"
    
    return True

def validate_analysis_result(result, required_sections=None):
    """验证分析结果格式"""
    if required_sections is None:
        required_sections = ["success", "message"]
    
    assert isinstance(result, dict), "分析结果应该是字典格式"
    
    for section in required_sections:
        assert section in result, f"分析结果中缺少必需部分: {section}"
    
    if "success" in result:
        assert isinstance(result["success"], bool), "success字段应该是布尔值"
    
    return True