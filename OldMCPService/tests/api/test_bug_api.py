import pytest

class TestBugAPI:
    """Bug相关API测试"""
    
    def test_get_bugs_by_time_range(self, api_client, test_data):
        """测试用时间段查询bug列表"""
        response = api_client.get_bugs_by_time_range(
            test_data["time_range"]["startDate"],
            test_data["time_range"]["endDate"]
        )
        
        # 验证响应结构
        assert response is not None
        assert "rsCode" in response
        assert "msg" in response
        assert "body" in response
        assert response["rsCode"] == "00000000"
        
        # 验证返回的bug数据
        bugs = response["body"]
        # 如果返回的是BugModel对象，转换为字典进行验证
        if hasattr(bugs, '__iter__') and not isinstance(bugs, (str, dict)):
            # 可能是模型对象列表
            bugs = [bug.dict() if hasattr(bug, 'dict') else bug for bug in bugs]
        assert isinstance(bugs, list)
        
        # 验证bug数据结构
        if bugs:
            bug = bugs[0]
            assert "id" in bug
            assert "assignedTo" in bug
            assert "project" in bug
            assert "status" in bug
            assert "title" in bug
    
    def test_get_bugs_by_time_range_and_dept(self, api_client, test_data):
        """测试根据时间段和部门查询bug"""
        response = api_client.get_bugs_by_time_range_and_dept(
            test_data["time_range"]["startDate"],
            test_data["time_range"]["endDate"],
            test_data["dept_id"]
        )
        
        # 验证响应结构
        assert response is not None
        assert "rsCode" in response
        assert "msg" in response
        assert "body" in response
        assert response["rsCode"] == "00000000"
    
    def test_get_bug_detail(self, api_client, test_data):
        """测试根据BUGID查询BUG详情"""
        response = api_client.get_bug_detail(test_data["bug_id"])
        
        # 验证响应结构
        assert response is not None
        assert "rsCode" in response
        assert "msg" in response
        assert "body" in response
        assert response["rsCode"] == "00000000"
        
        # 验证bug详情数据 - 检查多种可能的字段名
        body = response["body"]
        
        # 打印响应体结构，帮助调试
        print(f"响应体结构: {body.keys()}")
        
        # 尝试不同的字段名
        if "data" in body:
            bug_detail = body["data"]
        elif "bugdetail" in body:
            bug_detail = body["bugdetail"]
        elif "detail" in body:
            bug_detail = body["detail"]
        else:
            # 如果找不到预期的字段，直接使用整个body
            bug_detail = body
        
        # 验证bug ID，如果bug_detail是字典
        if isinstance(bug_detail, dict):
            assert "id" in bug_detail, f"响应中缺少ID字段: {bug_detail}"
            assert bug_detail["id"] == test_data["bug_id"], f"Bug ID不匹配: 期望 {test_data['bug_id']}, 实际 {bug_detail['id']}"
        elif isinstance(bug_detail, list) and bug_detail:
            # 如果是列表，检查第一个元素
            assert "id" in bug_detail[0], f"响应中缺少ID字段: {bug_detail[0]}"
            assert bug_detail[0]["id"] == test_data["bug_id"], f"Bug ID不匹配: 期望 {test_data['bug_id']}, 实际 {bug_detail[0]['id']}"
        else:
            assert False, f"无法在响应中找到有效的bug详情数据: {body}"
    
    def test_get_bugs_by_project(self, api_client, test_data):
        """测试根据项目ID查询项目BUG列表"""
        response = api_client.get_bugs_by_project(test_data["project_id"])
        
        # 验证响应结构
        assert response is not None
        assert "rsCode" in response
        assert "msg" in response
        assert "body" in response
        assert response["rsCode"] == "00000000"
        
        # 验证返回的bug数据
        bugs = response["body"]["bugdetail"]
        assert isinstance(bugs, list)