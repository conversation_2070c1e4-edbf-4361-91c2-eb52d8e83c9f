import pytest

class TestProjectAPI:
    """项目相关API测试"""
    
    def test_get_all_projects(self, api_client):
        """测试获取所有项目"""
        response = api_client.get_all_projects()
        
        # 验证响应结构
        assert response is not None
        assert "rsCode" in response
        assert "msg" in response
        assert "body" in response
        assert response["rsCode"] == "00000000"
        
        # 验证返回的项目数据
        projects = response["body"]
        # 如果返回的是ProjectModel对象，转换为字典进行验证
        if hasattr(projects, '__iter__') and not isinstance(projects, (str, dict)):
            # 可能是模型对象列表
            projects = [project.dict() if hasattr(project, 'dict') else project for project in projects]
        assert isinstance(projects, list)
        
        # 验证项目数据结构
        if projects:
            project = projects[0]
            assert "id" in project
            assert "name" in project
            assert "code" in project
            assert "begin" in project
            assert "end" in project
            assert "status" in project