import pytest

class TestTaskAPI:
    """任务相关API测试"""
    
    def test_get_tasks_by_project(self, api_client, test_data):
        """测试根据项目ID查询项目任务列表"""
        response = api_client.get_tasks_by_project(test_data["project_id"])
        
        # 验证响应结构
        assert response is not None
        assert "rsCode" in response
        assert "msg" in response
        assert "body" in response
        assert response["rsCode"] == "00000000"
        
        # 验证返回的任务数据
        tasks = response["body"]["data"]
        assert isinstance(tasks, list)
        
        # 验证任务数据结构
        if tasks:
            task = tasks[0]
            assert "id" in task
            assert "project" in task
            assert "name" in task
            assert "status" in task
    
    def test_get_task_by_id(self, api_client, test_data):
        """测试根据任务ID查询任务详情"""
        response = api_client.get_task_by_id(test_data["task_id"])
        
        # 验证响应结构
        assert response is not None
        assert "rsCode" in response
        assert "msg" in response
        assert "body" in response
        assert response["rsCode"] == "00000000"
        
        # 验证任务详情数据
        task_detail = response["body"]["data"]
        assert task_detail["id"] == test_data["task_id"]