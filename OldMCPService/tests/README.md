# 测试文件夹说明

本目录包含禅道MCP服务器的所有测试文件，按功能模块进行组织。

## 📁 目录结构

```
tests/
├── conftest.py                     # pytest配置文件
├── README.md                       # 本说明文件
├── core/                          # 核心功能测试
│   ├── test_basic_functionality.py    # 基本功能测试
│   ├── test_auth.py                   # 认证模块测试
│   ├── test_cache.py                  # 缓存系统测试
│   ├── test_config_auth.py            # 配置管理和认证测试
│   └── test_error_handling.py         # 错误处理测试
├── api/                           # API接口测试
│   ├── test_department_api.py         # 部门API测试
│   ├── test_project_api.py            # 项目API测试
│   ├── test_story_api.py              # 需求API测试
│   ├── test_task_api.py               # 任务API测试
│   └── test_bug_api.py                # Bug API测试
├── mcp/                           # MCP相关测试
│   ├── tools/                         # MCP工具测试
│   │   ├── test_analysis_tools.py     # 分析增强层工具测试
│   │   ├── test_direct_tools.py       # 直接接口层工具测试
│   │   ├── test_bug_tools.py          # Bug工具测试
│   │   └── test_project_tools.py      # 项目工具测试
│   └── resources/                     # MCP资源测试
│       ├── test_bug_resources.py      # Bug资源测试
│       ├── test_project_resources.py  # 项目资源测试
│       ├── test_story_resources.py    # 需求资源测试
│       └── test_task_resources.py     # 任务资源测试
├── integration/                   # 集成测试
│   ├── test_integration.py            # 系统集成测试
│   ├── test_client_cache_integration.py # 客户端缓存集成测试
│   └── test_mcp_client.py             # MCP客户端测试
└── system/                        # 系统测试
    ├── test_connectivity_runner.py    # 连通性测试
    ├── test_health_check.py           # 健康检查测试
    └── test_performance.py            # 性能测试
```

## 🧪 测试分类说明

### Core（核心功能测试）
测试系统的基础功能，包括：
- 模块导入和基本功能验证
- 用户认证和会话管理
- 缓存系统功能
- 配置管理
- 错误处理机制

### API（API接口测试）
测试禅道系统的各个API接口：
- 部门管理API
- 项目管理API
- 需求管理API
- 任务管理API
- Bug管理API

### MCP（模型上下文协议测试）
测试MCP相关功能：
- **Tools**: MCP工具层测试，包括分析工具和直接接口工具
- **Resources**: MCP资源层测试，测试各种资源的访问和管理

### Integration（集成测试）
测试系统各组件间的集成：
- 系统整体集成测试
- 客户端与缓存的集成测试
- MCP客户端集成测试

### System（系统测试）
测试系统级功能：
- 网络连通性测试
- 系统健康检查
- 性能测试

## 🚀 运行测试

### 运行所有测试
```bash
pytest tests/
```

### 按模块运行测试
```bash
# 核心功能测试
pytest tests/core/

# API接口测试
pytest tests/api/

# MCP功能测试
pytest tests/mcp/

# 集成测试
pytest tests/integration/

# 系统测试
pytest tests/system/
```

### 运行特定测试文件
```bash
pytest tests/core/test_auth.py
pytest tests/api/test_project_api.py
```

### 运行测试并生成覆盖率报告
```bash
pytest tests/ --cov=zentao_mcp --cov-report=html
```

## 📋 测试要求

### 环境准备
1. 确保已安装所有依赖：`pip install -r requirements.txt`
2. 配置测试环境变量（如需要）
3. 确保禅道系统可访问（用于集成测试）

### 测试规范
- 所有测试文件以 `test_` 开头
- 测试函数以 `test_` 开头
- 使用 pytest 框架
- 遵循 AAA 模式（Arrange, Act, Assert）
- 适当使用 fixtures 和 mocks

### 测试覆盖率
- 目标覆盖率：≥80%
- 核心功能覆盖率：≥90%
- 关键API覆盖率：≥85%

## 🔧 维护说明

### 添加新测试
1. 根据功能选择合适的目录
2. 遵循现有的命名规范
3. 添加适当的文档说明
4. 确保测试的独立性和可重复性

### 测试文件组织原则
- **单一职责**：每个测试文件专注于一个模块或功能
- **清晰分类**：按功能模块组织，便于维护
- **避免重复**：定期检查和清理重复的测试用例
- **文档完整**：保持测试说明和注释的完整性

## 📊 测试统计

- **总测试文件数**：26个
- **测试覆盖模块**：核心功能、API接口、MCP功能、集成测试、系统测试
- **最后更新**：2025年8月27日

---

如有测试相关问题，请参考项目文档或联系开发团队。