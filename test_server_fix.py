#!/usr/bin/env python3
"""
测试服务器修复后的API功能
"""
import requests
import json
import time
from typing import Dict, Any

# 服务器配置
BASE_URL = "http://localhost:8002"
API_BASE = f"{BASE_URL}/api/v1"

def test_server_health():
    """测试服务器健康状态"""
    print("🔍 测试服务器健康状态...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ 服务器健康检查通过")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接服务器失败: {e}")
        return False

def test_system_status():
    """测试系统状态"""
    print("\n🔍 测试系统状态...")
    try:
        response = requests.get(f"{API_BASE}/system/status")
        if response.status_code == 200:
            data = response.json()
            print("✅ 系统状态获取成功")
            print(f"   - 已初始化: {data.get('initialized', False)}")
            print(f"   - 管理员用户数: {data.get('admin_users', 0)}")
            print(f"   - API密钥数: {data.get('api_keys', 0)}")
            return True
        else:
            print(f"❌ 系统状态获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 系统状态测试失败: {e}")
        return False

def test_admin_login():
    """测试管理员登录"""
    print("\n🔍 测试管理员登录...")
    try:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        response = requests.post(f"{API_BASE}/auth/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            print("✅ 管理员登录成功")
            token = data.get('access_token')
            if token:
                print(f"   - 获得访问令牌: {token[:20]}...")
                return token
            else:
                print("❌ 未获得访问令牌")
                return None
        else:
            print(f"❌ 登录失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 登录测试失败: {e}")
        return None

def test_api_with_auth(token: str):
    """使用认证令牌测试API"""
    print("\n🔍 测试认证API...")
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # 测试获取用户列表
        response = requests.get(f"{API_BASE}/admin/users", headers=headers)
        if response.status_code == 200:
            users = response.json()
            print(f"✅ 获取用户列表成功，共 {len(users)} 个用户")
            return True
        else:
            print(f"❌ 获取用户列表失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 认证API测试失败: {e}")
        return False

def test_swagger_docs():
    """测试Swagger文档"""
    print("\n🔍 测试API文档...")
    try:
        response = requests.get(f"{BASE_URL}/docs")
        if response.status_code == 200:
            print("✅ Swagger文档可访问")
            print(f"   - 文档地址: {BASE_URL}/docs")
            return True
        else:
            print(f"❌ Swagger文档访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 文档测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 Zentao MCP Backend Service 修复验证测试")
    print("=" * 60)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    test_results = []
    
    # 1. 健康检查
    test_results.append(test_server_health())
    
    # 2. 系统状态
    test_results.append(test_system_status())
    
    # 3. 管理员登录
    token = test_admin_login()
    test_results.append(token is not None)
    
    # 4. 认证API测试
    if token:
        test_results.append(test_api_with_auth(token))
    else:
        test_results.append(False)
    
    # 5. 文档测试
    test_results.append(test_swagger_docs())
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = sum(test_results)
    total = len(test_results)
    
    test_names = [
        "服务器健康检查",
        "系统状态获取", 
        "管理员登录",
        "认证API访问",
        "API文档访问"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！服务器修复成功！")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)