#!/usr/bin/env python3
"""
系统使用说明文档生成器
"""

def generate_user_manual():
    """生成用户使用手册"""
    
    manual = """
# 禅道MCP权限系统 - 用户使用手册

## 📖 系统概述

禅道MCP权限系统是一个基于FastAPI和Vue.js开发的现代化权限管理系统，提供用户管理、API密钥管理、审计日志等功能。

### 🎯 主要功能
- **用户管理**: 创建、编辑、删除用户账户
- **权限控制**: 两级权限体系（普通用户/管理员）
- **API密钥管理**: 生成和管理API访问密钥
- **审计日志**: 记录所有系统操作
- **安全认证**: JWT令牌认证和bcrypt密码加密

## 🚀 快速开始

### 1. 系统访问
- **管理界面**: http://localhost:3000
- **API文档**: http://localhost:8000/docs
- **默认管理员**: admin / admin123

### 2. 首次登录
1. 打开浏览器访问管理界面
2. 使用默认管理员账户登录
3. 建议立即修改默认密码

## 👤 用户权限说明

### 权限级别
1. **普通用户 (USER)**
   - 查看个人信息
   - 管理自己的API密钥
   - 重置自己的密码

2. **管理员 (ADMIN)**
   - 普通用户的所有权限
   - 管理其他用户账户
   - 查看审计日志
   - 管理所有API密钥



## 🎛️ 功能使用指南

### 📊 仪表板
仪表板提供系统概览信息：
- **统计信息**: 用户数量、API密钥数量等
- **快速操作**: 常用功能的快捷入口
- **系统信息**: 当前用户信息和登录状态

### 👥 用户管理

#### 查看用户列表
1. 点击导航栏"用户管理"
2. 查看所有用户的基本信息
3. 使用搜索框快速查找用户

#### 创建新用户
1. 点击"新建用户"按钮
2. 填写用户信息：
   - **用户名**: 唯一标识符
   - **邮箱**: 用于通知和找回密码
   - **密码**: 初始密码（用户可后续修改）
   - **用户类型**: 选择权限级别
3. 点击"创建"完成

#### 管理现有用户
- **编辑**: 修改用户基本信息
- **重置密码**: 为用户生成新的临时密码
- **启用/禁用**: 控制用户账户状态
- **删除**: 永久删除用户账户（谨慎操作）

### 🔑 API密钥管理

#### 创建API密钥
1. 访问"API Keys"页面
2. 点击"新建密钥"
3. 设置密钥信息：
   - **名称**: 密钥用途描述
   - **描述**: 详细说明
   - **权限**: 选择访问权限范围
4. 保存后获得密钥字符串

#### 使用API密钥
```bash
# 在HTTP请求头中添加认证信息
Authorization: Bearer YOUR_API_KEY
```

#### 管理密钥
- **查看**: 显示密钥基本信息（不显示完整密钥）
- **重置**: 生成新的密钥字符串
- **删除**: 撤销密钥访问权限

### 📝 审计日志

#### 查看操作记录
1. 访问"审计日志"页面
2. 查看所有系统操作记录
3. 包含信息：
   - **操作时间**: 精确到秒
   - **操作用户**: 执行操作的用户
   - **操作类型**: 创建、修改、删除等
   - **操作对象**: 被操作的资源
   - **操作详情**: 具体操作内容

#### 日志筛选
- **时间范围**: 选择特定时间段
- **用户筛选**: 查看特定用户的操作
- **操作类型**: 筛选特定类型的操作

### 👤 个人资料管理

#### 修改个人信息
1. 点击右上角用户头像
2. 选择"个人资料"
3. 修改可编辑的信息
4. 保存更改

#### 修改密码
1. 在个人资料页面
2. 点击"修改密码"
3. 输入当前密码和新密码
4. 确认修改

## 🔧 API接口使用

### 认证接口

#### 登录获取令牌
```bash
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

#### 获取当前用户信息
```bash
GET /api/v1/auth/me
Authorization: Bearer YOUR_TOKEN
```

### 用户管理接口

#### 获取用户列表
```bash
GET /api/v1/users
Authorization: Bearer YOUR_TOKEN
```

#### 创建用户
```bash
POST /api/v1/users
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password123",
  "user_type": "USER"
}
```

### API密钥接口

#### 获取密钥列表
```bash
GET /api/v1/api-keys
Authorization: Bearer YOUR_TOKEN
```

#### 创建API密钥
```bash
POST /api/v1/api-keys
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "name": "My API Key",
  "description": "For external service",
  "permissions": ["read", "write"]
}
```

## 🛡️ 安全最佳实践

### 密码安全
- 使用强密码（至少8位，包含字母、数字、特殊字符）
- 定期更换密码
- 不要共享账户密码

### API密钥安全
- 定期轮换API密钥
- 为不同用途创建不同的密钥
- 及时删除不再使用的密钥
- 不要在代码中硬编码密钥

### 账户安全
- 及时禁用离职员工账户
- 定期审查用户权限
- 监控异常登录活动

## 🚨 故障排除

### 登录问题
**问题**: 无法登录系统
**解决方案**:
1. 检查用户名和密码是否正确
2. 确认账户是否被禁用
3. 清除浏览器缓存和Cookie
4. 联系管理员重置密码

### 权限问题
**问题**: 无法访问某些功能
**解决方案**:
1. 确认当前用户权限级别
2. 联系管理员提升权限
3. 检查是否需要重新登录

### API调用问题
**问题**: API请求返回401错误
**解决方案**:
1. 检查API密钥是否正确
2. 确认密钥是否已过期
3. 验证请求头格式是否正确
4. 检查密钥权限是否足够

### 页面加载问题
**问题**: 页面无法正常加载
**解决方案**:
1. 检查网络连接
2. 确认服务是否正常运行
3. 清除浏览器缓存
4. 尝试使用其他浏览器

## 📞 技术支持

### 联系方式
- **技术支持**: 联系系统管理员
- **问题反馈**: 通过系统内置反馈功能
- **紧急情况**: 联系IT部门

### 常用资源
- **API文档**: http://localhost:8000/docs
- **系统状态**: http://localhost:8000/health
- **操作日志**: 系统内审计日志页面

## 📋 附录

### 系统要求
- **浏览器**: Chrome 80+, Firefox 75+, Safari 13+
- **网络**: 稳定的网络连接
- **分辨率**: 最低1024x768

### 更新日志
- **v1.0.0**: 初始版本发布
  - 基础用户管理功能
  - API密钥管理
  - 审计日志记录
  - 权限控制系统

### 术语表
- **JWT**: JSON Web Token，用于身份认证的令牌
- **API**: Application Programming Interface，应用程序接口
- **CRUD**: Create, Read, Update, Delete，增删改查操作
- **bcrypt**: 密码哈希算法
"""
    
    return manual

def generate_admin_guide():
    """生成管理员指南"""
    
    guide = """
# 禅道MCP权限系统 - 管理员指南

## 🔧 系统部署

### 环境要求
- **Python**: 3.8+
- **Node.js**: 16+
- **数据库**: SQLite（默认）或PostgreSQL
- **操作系统**: Linux/macOS/Windows

### 安装步骤

#### 1. 后端服务部署
```bash
# 克隆代码
git clone <repository_url>
cd zentaomcpserver

# 安装Python依赖
cd zentao-mcp-backend-service
pip install -r requirements.txt

# 初始化数据库
python init_admin.py

# 启动服务
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
```

#### 2. 前端服务部署
```bash
# 安装依赖
cd zentao-token-web
npm install  # 或 bun install

# 构建生产版本
npm run build

# 启动开发服务器
npm run dev
```

### 配置管理

#### 环境变量
```bash
# 数据库配置
DATABASE_URL=sqlite:///./zentao_mcp.db

# JWT配置
SECRET_KEY=your-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS配置
ALLOWED_ORIGINS=http://localhost:3000
```

#### 配置文件
- **后端配置**: `app/core/config.py`
- **前端配置**: `.env` 文件

## 👥 用户管理

### 初始化管理员
```bash
# 创建超级管理员
python create_admin.py --username admin --password admin123 --email <EMAIL>
```

### 批量用户操作
```python
# 批量创建用户脚本示例
import requests

users = [
    {"username": "user1", "email": "<EMAIL>", "password": "pass123", "user_type": "USER"},
    {"username": "user2", "email": "<EMAIL>", "password": "pass123", "user_type": "ADMIN"}
]

for user in users:
    response = requests.post("http://localhost:8000/api/v1/users", json=user, headers=headers)
    print(f"Created user: {user['username']}")
```

### 用户权限管理
- **权限提升**: 将普通用户提升为管理员
- **权限降级**: 降低用户权限级别
- **批量操作**: 批量修改用户状态

## 🔑 API密钥管理

### 系统级API密钥
```bash
# 创建系统级API密钥
curl -X POST http://localhost:8000/api/v1/api-keys \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{"name":"System Key","permissions":["admin"]}'
```

### 密钥轮换策略
- **定期轮换**: 建议每90天轮换一次
- **自动化脚本**: 编写自动轮换脚本
- **通知机制**: 密钥更新时通知相关用户

## 📊 监控和维护

### 系统监控
```bash
# 检查系统状态
curl http://localhost:8000/health

# 查看系统指标
curl http://localhost:8000/metrics
```

### 日志管理
- **应用日志**: `logs/app.log`
- **访问日志**: `logs/access.log`
- **错误日志**: `logs/error.log`

### 数据库维护
```sql
-- 清理过期会话
DELETE FROM user_sessions WHERE expires_at < datetime('now');

-- 清理旧审计日志（保留90天）
DELETE FROM audit_logs WHERE created_at < datetime('now', '-90 days');

-- 数据库优化
VACUUM;
ANALYZE;
```

## 🛡️ 安全配置

### HTTPS配置
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:3000;
    }
    
    location /api {
        proxy_pass http://localhost:8000;
    }
}
```

### 防火墙配置
```bash
# 只允许必要端口
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw deny 8000   # 直接访问后端
ufw deny 3000   # 直接访问前端
```

### 安全加固
- **定期更新依赖**
- **启用请求限制**
- **配置CORS策略**
- **启用审计日志**

## 📈 性能优化

### 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_users_username ON admin_users(username);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_api_keys_user_id ON api_keys(user_id);
```

### 缓存配置
```python
# Redis缓存配置
REDIS_URL = "redis://localhost:6379/0"
CACHE_TTL = 300  # 5分钟
```

### 负载均衡
```yaml
# Docker Compose示例
version: '3.8'
services:
  app1:
    build: .
    ports:
      - "8001:8000"
  app2:
    build: .
    ports:
      - "8002:8000"
  nginx:
    image: nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
```

## 🔄 备份和恢复

### 数据备份
```bash
#!/bin/bash
# 备份脚本
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/zentao_mcp"

# 备份数据库
cp zentao_mcp.db "$BACKUP_DIR/db_$DATE.db"

# 备份配置文件
tar -czf "$BACKUP_DIR/config_$DATE.tar.gz" app/core/config.py .env

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.db" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

### 数据恢复
```bash
# 恢复数据库
cp /backup/zentao_mcp/db_20240829_120000.db zentao_mcp.db

# 重启服务
systemctl restart zentao-mcp
```

## 🚨 故障处理

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查数据库文件权限
ls -la zentao_mcp.db

# 重新创建数据库
python init_admin.py --force
```

#### 2. 内存使用过高
```bash
# 检查进程状态
ps aux | grep uvicorn

# 重启服务
systemctl restart zentao-mcp
```

#### 3. 登录失败
```python
# 重置管理员密码
from app.services.password_service import PasswordService
from app.models.admin import AdminUser
from app.core.database import SessionLocal

db = SessionLocal()
admin = db.query(AdminUser).filter(AdminUser.username == "admin").first()
admin.password_hash = PasswordService.hash_password("new_password")
db.commit()
```

### 紧急恢复程序
1. **停止所有服务**
2. **恢复最新备份**
3. **验证数据完整性**
4. **重启服务**
5. **验证功能正常**

## 📋 维护清单

### 日常维护（每日）
- [ ] 检查系统状态
- [ ] 查看错误日志
- [ ] 监控资源使用

### 周期维护（每周）
- [ ] 清理临时文件
- [ ] 检查磁盘空间
- [ ] 更新安全补丁

### 定期维护（每月）
- [ ] 数据库优化
- [ ] 备份验证
- [ ] 性能分析
- [ ] 安全审计

### 年度维护
- [ ] 系统升级
- [ ] 硬件检查
- [ ] 灾难恢复演练
- [ ] 安全评估
"""
    
    return guide

def main():
    """主函数"""
    # 生成用户手册
    user_manual = generate_user_manual()
    with open("USER_MANUAL.md", "w", encoding="utf-8") as f:
        f.write(user_manual)
    
    # 生成管理员指南
    admin_guide = generate_admin_guide()
    with open("ADMIN_GUIDE.md", "w", encoding="utf-8") as f:
        f.write(admin_guide)
    
    print("📚 文档生成完成:")
    print("  - USER_MANUAL.md (用户使用手册)")
    print("  - ADMIN_GUIDE.md (管理员指南)")
    print("\n" + "="*50)
    print("📖 快速访问:")
    print("  - 用户界面: http://localhost:3000")
    print("  - API文档: http://localhost:8000/docs")
    print("  - 默认账户: admin/admin123")
    print("="*50)

if __name__ == "__main__":
    main()