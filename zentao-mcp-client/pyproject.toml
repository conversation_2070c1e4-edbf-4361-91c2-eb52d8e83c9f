[project]
name = "zentao-mcp-client"
version = "0.1.0"
description = "Zentao MCP Client - Lightweight proxy client"
authors = [
    {name = "Zentao MCP Team"}
]
dependencies = [
    "fastmcp>=0.4.0",
    "httpx>=0.25.0",
    "click>=8.1.0",
    "pydantic>=2.4.0",
    "python-dotenv>=1.0.0",
    "configparser>=6.0.0",
]
requires-python = ">=3.10"

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.11.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.5.0",
    "pyinstaller>=6.0.0",
]

[project.scripts]
zentao-mcp-client = "zentao_mcp_client.cli:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py310']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true