# Zentao MCP Client

轻量级Zentao MCP代理客户端，提供与原有HTTP/STDIO/SSE使用方式完全兼容的体验。

## 安装

```bash
pip install zentao-mcp-client
```

或从源码安装：

```bash
cd zentao-mcp-client
pip install -e .
```

## 配置

首次使用需要配置后端服务连接信息：

```bash
zentao-mcp-client configure
```

按提示输入：
- 后端服务URL（如：http://your-server:8000）
- API Key

## 使用

启动客户端代理服务：

```bash
zentao-mcp-client start
```

查看配置信息：

```bash
zentao-mcp-client info
```

## 功能特性

- 完全兼容原有的HTTP/STDIO/SSE使用方式
- 自动转发请求到云端后端服务
- 支持所有Zentao MCP工具和资源
- 简单的配置管理
- 轻量级设计，资源占用少

## 支持的工具

- 部门管理：获取部门列表、部门用户
- 项目管理：获取项目列表、项目需求、项目任务、项目Bug
- Bug管理：按时间范围查询、Bug详情
- 需求管理：需求详情
- 任务管理：任务详情
- 用户管理：用户信息查询