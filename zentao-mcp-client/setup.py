"""
Setup script for zentao-mcp-client
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="zentao-mcp-client",
    version="0.1.0",
    author="Zentao MCP Team",
    description="Lightweight proxy client for Zentao MCP Backend Service",
    long_description=long_description,
    long_description_content_type="text/markdown",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
    ],
    python_requires=">=3.10",
    install_requires=[
        "fastmcp>=0.4.0",
        "httpx>=0.25.0",
        "click>=8.1.0",
        "pydantic>=2.4.0",
        "python-dotenv>=1.0.0",
    ],
    entry_points={
        "console_scripts": [
            "zentao-mcp-client=zentao_mcp_client.cli:main",
        ],
    },
)