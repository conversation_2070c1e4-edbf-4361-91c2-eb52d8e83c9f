"""
客户端配置管理模块
"""

import os
import configparser
from pathlib import Path
from typing import Optional


class ClientConfig:
    """客户端配置管理"""
    
    def __init__(self):
        self.config_dir = Path.home() / ".zentao_mcp_client"
        self.config_file = self.config_dir / "config.ini"
        self.config = configparser.ConfigParser()
        
        # 确保配置目录存在
        self.config_dir.mkdir(exist_ok=True)
        
        # 加载现有配置
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        if self.config_file.exists():
            self.config.read(self.config_file)
        else:
            # 创建默认配置
            self.config['DEFAULT'] = {
                'backend_url': '',
                'api_key': ''
            }
    
    def save_config(self) -> None:
        """保存配置文件"""
        with open(self.config_file, 'w') as f:
            self.config.write(f)
    
    def set_backend_url(self, url: str) -> None:
        """设置后端服务URL"""
        if not self.config.has_section('client'):
            self.config.add_section('client')
        self.config.set('client', 'backend_url', url)
        self.save_config()
    
    def set_api_key(self, api_key: str) -> None:
        """设置API Key"""
        if not self.config.has_section('client'):
            self.config.add_section('client')
        self.config.set('client', 'api_key', api_key)
        self.save_config()
    
    def get_backend_url(self) -> Optional[str]:
        """获取后端服务URL"""
        try:
            return self.config.get('client', 'backend_url')
        except (configparser.NoSectionError, configparser.NoOptionError):
            return None
    
    def get_api_key(self) -> Optional[str]:
        """获取API Key"""
        try:
            return self.config.get('client', 'api_key')
        except (configparser.NoSectionError, configparser.NoOptionError):
            return None
    
    def is_configured(self) -> bool:
        """检查是否已配置"""
        backend_url = self.get_backend_url()
        api_key = self.get_api_key()
        return bool(backend_url and api_key)
    
    def get_config_info(self) -> dict:
        """获取配置信息（不包含敏感信息）"""
        backend_url = self.get_backend_url()
        api_key = self.get_api_key()
        
        return {
            'config_file': str(self.config_file),
            'backend_url': backend_url or 'Not configured',
            'api_key_configured': bool(api_key),
            'api_key_preview': f"{api_key[:8]}..." if api_key and len(api_key) > 8 else 'Not configured'
        }