"""
核心代理逻辑模块 - 使用FastMCP SDK
"""

import asyncio
import logging
import httpx
from typing import Any, Dict, List
from fastmcp import FastMCP
from .config import ClientConfig

logger = logging.getLogger(__name__)


class ZentaoMCPProxy:
    """Zentao MCP代理服务"""
    
    def __init__(self, config: ClientConfig):
        self.config = config
        self.backend_url = config.get_backend_url()
        self.api_key = config.get_api_key()
        self.client = httpx.AsyncClient(timeout=30.0)
        
        # 初始化FastMCP
        self.mcp = FastMCP("Zentao MCP Client")
        
        # 注册工具
        self._register_tools()
        
        # 注册资源
        self._register_resources()
    
    def _register_tools(self):
        """注册所有工具"""
        
        # 部门相关工具
        @self.mcp.tool()
        async def zentao_get_all_departments() -> List[Dict[str, Any]]:
            """获取所有部门列表"""
            return await self._forward_request("tool_call", "zentao_get_all_departments", {})
        
        @self.mcp.tool()
        async def zentao_get_users_by_department(dept_id: int) -> List[Dict[str, Any]]:
            """根据部门ID获取用户列表"""
            return await self._forward_request("tool_call", "zentao_get_users_by_department", {"dept_id": dept_id})
        
        # 项目相关工具
        @self.mcp.tool()
        async def zentao_get_all_projects() -> List[Dict[str, Any]]:
            """获取所有项目列表"""
            return await self._forward_request("tool_call", "zentao_get_all_projects", {})
        
        @self.mcp.tool()
        async def zentao_get_stories_by_project(project_id: int) -> List[Dict[str, Any]]:
            """根据项目ID获取需求列表"""
            return await self._forward_request("tool_call", "zentao_get_stories_by_project", {"project_id": project_id})
        
        @self.mcp.tool()
        async def zentao_get_tasks_by_project(project_id: int) -> List[Dict[str, Any]]:
            """根据项目ID获取任务列表"""
            return await self._forward_request("tool_call", "zentao_get_tasks_by_project", {"project_id": project_id})
        
        @self.mcp.tool()
        async def zentao_get_bugs_by_project(project_id: int) -> List[Dict[str, Any]]:
            """根据项目ID获取Bug列表"""
            return await self._forward_request("tool_call", "zentao_get_bugs_by_project", {"project_id": project_id})
        
        # Bug相关工具
        @self.mcp.tool()
        async def zentao_get_bugs_by_time_range(start_date: str, end_date: str) -> List[Dict[str, Any]]:
            """根据时间范围获取Bug列表"""
            return await self._forward_request("tool_call", "zentao_get_bugs_by_time_range", {
                "start_date": start_date,
                "end_date": end_date
            })
        
        @self.mcp.tool()
        async def zentao_get_bug_detail(bug_id: int) -> Dict[str, Any]:
            """获取Bug详情"""
            return await self._forward_request("tool_call", "zentao_get_bug_detail", {"bug_id": bug_id})
        
        # 需求相关工具
        @self.mcp.tool()
        async def zentao_get_story_detail(story_id: int) -> Dict[str, Any]:
            """获取需求详情"""
            return await self._forward_request("tool_call", "zentao_get_story_detail", {"story_id": story_id})
        
        # 任务相关工具
        @self.mcp.tool()
        async def zentao_get_task_detail(task_id: int) -> Dict[str, Any]:
            """获取任务详情"""
            return await self._forward_request("tool_call", "zentao_get_task_detail", {"task_id": task_id})
        
        # 用户相关工具
        @self.mcp.tool()
        async def zentao_get_user_info(user_account: str) -> Dict[str, Any]:
            """获取用户信息"""
            return await self._forward_request("tool_call", "zentao_get_user_info", {"user_account": user_account})
    
    def _register_resources(self):
        """注册资源"""
        
        @self.mcp.resource("zentao://departments")
        async def departments_resource() -> str:
            """部门资源"""
            result = await self._forward_request("resource_access", "zentao://departments", {})
            return str(result)
        
        @self.mcp.resource("zentao://projects")
        async def projects_resource() -> str:
            """项目资源"""
            result = await self._forward_request("resource_access", "zentao://projects", {})
            return str(result)
    
    async def _forward_request(self, request_type: str, name: str, arguments: Dict[str, Any]) -> Any:
        """转发请求到后端服务"""
        try:
            # 构建请求数据
            request_data = {
                "type": request_type,
                "name": name,
                "arguments": arguments
            }
            
            # 构建请求头
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # 发送请求到后端
            response = await self.client.post(
                f"{self.backend_url}/api/v1/mcp/execute",
                json=request_data,
                headers=headers
            )
            
            response.raise_for_status()
            result = response.json()
            
            # 检查响应状态
            if result.get("status") == "success":
                return result.get("data")
            else:
                error_msg = result.get("error", "未知错误")
                logger.error(f"后端服务返回错误: {error_msg}")
                raise Exception(f"后端服务错误: {error_msg}")
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP请求失败: {e.response.status_code} - {e.response.text}")
            raise Exception(f"后端服务请求失败: {e.response.status_code}")
        except Exception as e:
            logger.error(f"转发请求失败: {e}")
            raise
    
    async def close(self):
        """关闭代理服务"""
        await self.client.aclose()


def start_proxy_server(host: str, port: int, config: ClientConfig):
    """启动代理服务器"""
    
    async def run_server():
        proxy = ZentaoMCPProxy(config)
        
        try:
            # 启动FastMCP服务器
            await proxy.mcp.run(
                transport="stdio",  # 支持STDIO模式
                # 也可以配置HTTP和SSE模式
            )
        except KeyboardInterrupt:
            logger.info("收到中断信号，正在关闭服务...")
        finally:
            await proxy.close()
    
    # 运行异步服务器
    try:
        asyncio.run(run_server())
    except KeyboardInterrupt:
        logger.info("服务已停止")