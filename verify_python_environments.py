#!/usr/bin/env python3
"""
Python虚拟环境验证脚本
验证所有Python项目的依赖配置和虚拟环境状态
"""

import subprocess
import sys
from pathlib import Path
import json

def run_command(cmd, cwd=None):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            cwd=cwd
        )
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return False, "", str(e)

def check_project_environment(project_path):
    """检查单个项目的Python环境"""
    project_name = project_path.name
    print(f"\n🔍 检查项目: {project_name}")
    print("=" * 50)
    
    results = {
        "project": project_name,
        "path": str(project_path),
        "checks": {}
    }
    
    # 检查pyproject.toml
    pyproject_file = project_path / "pyproject.toml"
    if pyproject_file.exists():
        print("✅ pyproject.toml 存在")
        results["checks"]["pyproject_toml"] = True
    else:
        print("❌ pyproject.toml 不存在")
        results["checks"]["pyproject_toml"] = False
        return results
    
    # 检查uv.lock
    uv_lock_file = project_path / "uv.lock"
    if uv_lock_file.exists():
        print("✅ uv.lock 存在")
        results["checks"]["uv_lock"] = True
    else:
        print("❌ uv.lock 不存在")
        results["checks"]["uv_lock"] = False
    
    # 检查虚拟环境
    venv_path = project_path / ".venv"
    if venv_path.exists():
        print("✅ .venv 虚拟环境存在")
        results["checks"]["venv_exists"] = True
    else:
        print("❌ .venv 虚拟环境不存在")
        results["checks"]["venv_exists"] = False
        return results
    
    # 检查Python版本
    success, stdout, stderr = run_command(
        'uv run python -c "import sys; print(sys.version)"',
        cwd=project_path
    )
    if success:
        python_version = stdout.split()[0]
        print(f"✅ Python版本: {python_version}")
        results["checks"]["python_version"] = python_version
    else:
        print(f"❌ Python版本检查失败: {stderr}")
        results["checks"]["python_version"] = None
    
    # 检查关键依赖
    key_imports = {
        "zentao-mcp-backend-service": ["fastapi", "sqlalchemy", "pydantic"],
        "zentao-mcp-client": ["mcp"],
        "OldMCPService": ["mcp"]
    }
    
    if project_name in key_imports:
        print(f"🔍 检查关键依赖...")
        results["checks"]["dependencies"] = {}
        
        for dep in key_imports[project_name]:
            success, stdout, stderr = run_command(
                f'uv run python -c "import {dep}; print(\'{dep} OK\')"',
                cwd=project_path
            )
            if success:
                print(f"  ✅ {dep}")
                results["checks"]["dependencies"][dep] = True
            else:
                print(f"  ❌ {dep}: {stderr}")
                results["checks"]["dependencies"][dep] = False
    
    # 检查uv环境状态
    success, stdout, stderr = run_command("uv tree", cwd=project_path)
    if success:
        print("✅ uv依赖树正常")
        results["checks"]["uv_tree"] = True
    else:
        print(f"⚠️ uv依赖树检查: {stderr}")
        results["checks"]["uv_tree"] = False
    
    return results

def main():
    """主函数"""
    print("🚀 Python虚拟环境验证脚本")
    print("=" * 60)
    
    # 项目根目录
    root_path = Path.cwd()
    
    # Python项目列表
    python_projects = [
        "zentao-mcp-backend-service",
        "zentao-mcp-client", 
        "OldMCPService"
    ]
    
    all_results = []
    
    # 检查根目录是否还有Python环境残留
    print("\n🔍 检查根目录清理状态")
    print("=" * 50)
    
    root_venv = root_path / ".venv"
    root_pycache = root_path / "__pycache__"
    
    if not root_venv.exists():
        print("✅ 根目录 .venv 已清理")
    else:
        print("❌ 根目录 .venv 仍然存在")
    
    if not root_pycache.exists():
        print("✅ 根目录 __pycache__ 已清理")
    else:
        print("❌ 根目录 __pycache__ 仍然存在")
    
    # 检查每个Python项目
    for project_name in python_projects:
        project_path = root_path / project_name
        if project_path.exists():
            results = check_project_environment(project_path)
            all_results.append(results)
        else:
            print(f"\n❌ 项目目录不存在: {project_name}")
    
    # 生成总结报告
    print("\n" + "=" * 60)
    print("📊 验证总结报告")
    print("=" * 60)
    
    for result in all_results:
        project = result["project"]
        checks = result["checks"]
        
        print(f"\n📁 {project}:")
        
        # 统计通过的检查
        passed = sum(1 for v in checks.values() if v is True or (isinstance(v, dict) and all(v.values())))
        total = len(checks)
        
        if "dependencies" in checks:
            dep_passed = sum(1 for v in checks["dependencies"].values() if v is True)
            dep_total = len(checks["dependencies"])
            print(f"  依赖检查: {dep_passed}/{dep_total} 通过")
        
        print(f"  总体状态: {passed}/{total} 检查通过")
        
        if passed == total:
            print("  🎉 状态: 完美")
        elif passed >= total * 0.8:
            print("  ✅ 状态: 良好")
        else:
            print("  ⚠️ 状态: 需要关注")
    
    # 保存详细结果到文件
    results_file = root_path / "python_env_verification.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细结果已保存到: {results_file}")
    print("\n🎯 虚拟环境验证完成！")

if __name__ == "__main__":
    main()