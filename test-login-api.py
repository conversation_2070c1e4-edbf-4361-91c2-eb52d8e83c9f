#!/usr/bin/env python3

import requests
import json

def test_login_api():
    """测试登录API"""
    
    # API配置
    base_url = "http://localhost:8000"
    login_url = f"{base_url}/api/v1/admin/auth/login"
    
    # 测试数据
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    print("🔍 测试登录API")
    print("=" * 50)
    print(f"URL: {login_url}")
    print(f"数据: {json.dumps(login_data, indent=2)}")
    
    try:
        # 发送登录请求
        response = requests.post(
            login_url,
            json=login_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"\n📡 响应状态: {response.status_code}")
        print(f"📡 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 登录成功!")
            print(f"Token: {data.get('access_token', 'N/A')[:50]}...")
            print(f"用户ID: {data.get('user_id', 'N/A')}")
            print(f"用户名: {data.get('username', 'N/A')}")
            print(f"用户类型: {data.get('user_type', 'N/A')}")
            
        else:
            print("❌ 登录失败!")
            try:
                error_data = response.json()
                print(f"错误详情: {error_data}")
            except:
                print(f"错误内容: {response.text}")
                
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败! 请确保后端服务正在运行")
    except requests.exceptions.Timeout:
        print("❌ 请求超时!")
    except Exception as e:
        print(f"❌ 未知错误: {e}")

def test_health_check():
    """测试健康检查"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务健康检查通过")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except:
        print("❌ 服务未启动或无法连接")
        return False

if __name__ == "__main__":
    print("🚀 开始API测试")
    print("=" * 50)
    
    # 先检查服务状态
    if test_health_check():
        print()
        test_login_api()
    else:
        print("\n请先启动后端服务:")
        print("cd zentao-mcp-backend-service")
        print("uvicorn main:app --host 0.0.0.0 --port 8000 --reload")