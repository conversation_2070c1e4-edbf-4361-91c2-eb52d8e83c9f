#!/usr/bin/env python3
"""
前后端集成测试脚本
使用Selenium进行自动化UI测试
"""

import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class IntegrationTester:
    def __init__(self, backend_url="http://localhost:8000", frontend_url="http://localhost:3000"):
        self.backend_url = backend_url
        self.frontend_url = frontend_url
        self.driver = None
        self.wait = None
        
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--headless")  # 无头模式
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            self.log("✅ 浏览器驱动初始化成功")
            return True
        except Exception as e:
            self.log(f"❌ 浏览器驱动初始化失败: {e}")
            return False
    
    def log(self, message: str, level: str = "INFO"):
        """日志输出"""
        print(f"[{level}] {message}")
    
    def test_backend_health(self):
        """测试后端服务健康状态"""
        self.log("=== 测试后端服务健康状态 ===")
        try:
            response = requests.get(f"{self.backend_url}/health", timeout=5)
            if response.status_code == 200:
                self.log("✅ 后端服务正常运行")
                return True
            else:
                self.log(f"❌ 后端服务异常: {response.status_code}")
                return False
        except Exception as e:
            self.log(f"❌ 无法连接后端服务: {e}")
            return False
    
    def test_frontend_accessibility(self):
        """测试前端页面可访问性"""
        self.log("=== 测试前端页面可访问性 ===")
        try:
            response = requests.get(self.frontend_url, timeout=5)
            if response.status_code == 200:
                self.log("✅ 前端页面可访问")
                return True
            else:
                self.log(f"❌ 前端页面异常: {response.status_code}")
                return False
        except Exception as e:
            self.log(f"❌ 无法访问前端页面: {e}")
            return False
    
    def test_login_page(self):
        """测试登录页面"""
        self.log("=== 测试登录页面 ===")
        try:
            self.driver.get(self.frontend_url)
            
            # 等待页面加载
            time.sleep(2)
            
            # 检查是否重定向到登录页面
            current_url = self.driver.current_url
            if "/login" in current_url or "login" in self.driver.page_source.lower():
                self.log("✅ 成功访问登录页面")
                
                # 检查登录表单元素
                try:
                    username_input = self.wait.until(
                        EC.presence_of_element_located((By.NAME, "username"))
                    )
                    password_input = self.driver.find_element(By.NAME, "password")
                    login_button = self.driver.find_element(By.TYPE, "submit")
                    
                    self.log("✅ 登录表单元素存在")
                    return True
                except TimeoutException:
                    self.log("❌ 登录表单元素不存在")
                    return False
            else:
                self.log("❌ 未能访问登录页面")
                return False
                
        except Exception as e:
            self.log(f"❌ 登录页面测试失败: {e}")
            return False
    
    def test_login_process(self):
        """测试登录流程"""
        self.log("=== 测试登录流程 ===")
        try:
            # 填写登录信息
            username_input = self.driver.find_element(By.NAME, "username")
            password_input = self.driver.find_element(By.NAME, "password")
            
            username_input.clear()
            username_input.send_keys("admin")
            
            password_input.clear()
            password_input.send_keys("admin123")
            
            # 点击登录按钮
            login_button = self.driver.find_element(By.TYPE, "submit")
            login_button.click()
            
            # 等待页面跳转
            time.sleep(3)
            
            # 检查是否成功登录（通过URL变化或页面内容判断）
            current_url = self.driver.current_url
            if "/dashboard" in current_url or "仪表板" in self.driver.page_source:
                self.log("✅ 登录成功，跳转到仪表板")
                return True
            else:
                self.log(f"❌ 登录失败，当前URL: {current_url}")
                return False
                
        except Exception as e:
            self.log(f"❌ 登录流程测试失败: {e}")
            return False
    
    def test_dashboard_page(self):
        """测试仪表板页面"""
        self.log("=== 测试仪表板页面 ===")
        try:
            # 检查仪表板关键元素
            page_source = self.driver.page_source
            
            if "仪表板" in page_source or "Dashboard" in page_source:
                self.log("✅ 仪表板页面加载成功")
                
                # 检查统计卡片
                if "总用户数" in page_source or "用户" in page_source:
                    self.log("✅ 统计信息显示正常")
                
                # 检查导航菜单
                if "用户管理" in page_source:
                    self.log("✅ 导航菜单显示正常")
                
                return True
            else:
                self.log("❌ 仪表板页面内容异常")
                return False
                
        except Exception as e:
            self.log(f"❌ 仪表板页面测试失败: {e}")
            return False
    
    def test_user_management_page(self):
        """测试用户管理页面"""
        self.log("=== 测试用户管理页面 ===")
        try:
            # 尝试访问用户管理页面
            self.driver.get(f"{self.frontend_url}/users")
            time.sleep(2)
            
            page_source = self.driver.page_source
            
            if "用户管理" in page_source:
                self.log("✅ 用户管理页面加载成功")
                
                # 检查用户列表表格
                if "用户信息" in page_source or "权限类型" in page_source:
                    self.log("✅ 用户列表表格显示正常")
                
                # 检查操作按钮
                if "新建用户" in page_source:
                    self.log("✅ 操作按钮显示正常")
                
                return True
            else:
                self.log("❌ 用户管理页面内容异常")
                return False
                
        except Exception as e:
            self.log(f"❌ 用户管理页面测试失败: {e}")
            return False
    
    def test_api_integration(self):
        """测试API集成"""
        self.log("=== 测试API集成 ===")
        try:
            # 在浏览器中执行JavaScript来测试API调用
            script = """
            return fetch('/api/v1/auth/me', {
                headers: {
                    'Authorization': 'Bearer ' + localStorage.getItem('auth_token')
                }
            }).then(response => response.ok).catch(() => false);
            """
            
            result = self.driver.execute_script(script)
            
            if result:
                self.log("✅ API集成测试成功")
                return True
            else:
                self.log("❌ API集成测试失败")
                return False
                
        except Exception as e:
            self.log(f"❌ API集成测试异常: {e}")
            return False
    
    def test_responsive_design(self):
        """测试响应式设计"""
        self.log("=== 测试响应式设计 ===")
        try:
            # 测试不同屏幕尺寸
            sizes = [
                (1920, 1080),  # 桌面
                (768, 1024),   # 平板
                (375, 667)     # 手机
            ]
            
            for width, height in sizes:
                self.driver.set_window_size(width, height)
                time.sleep(1)
                
                # 检查页面是否正常显示
                page_source = self.driver.page_source
                if "仪表板" in page_source or "Dashboard" in page_source:
                    self.log(f"✅ {width}x{height} 尺寸显示正常")
                else:
                    self.log(f"❌ {width}x{height} 尺寸显示异常")
                    return False
            
            return True
            
        except Exception as e:
            self.log(f"❌ 响应式设计测试失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        if self.driver:
            self.driver.quit()
            self.log("✅ 浏览器驱动已关闭")
    
    def run_integration_tests(self):
        """运行完整的集成测试"""
        self.log("🚀 开始前后端集成测试")
        self.log("=" * 60)
        
        tests_passed = 0
        tests_total = 0
        
        # 1. 后端健康检查
        tests_total += 1
        if self.test_backend_health():
            tests_passed += 1
        else:
            self.log("❌ 后端服务不可用，跳过后续测试")
            return False
        
        # 2. 前端可访问性检查
        tests_total += 1
        if self.test_frontend_accessibility():
            tests_passed += 1
        else:
            self.log("❌ 前端服务不可用，跳过后续测试")
            return False
        
        # 3. 设置浏览器驱动
        if not self.setup_driver():
            self.log("❌ 无法设置浏览器驱动，跳过UI测试")
            return False
        
        try:
            # 4. 登录页面测试
            tests_total += 1
            if self.test_login_page():
                tests_passed += 1
            
            # 5. 登录流程测试
            tests_total += 1
            if self.test_login_process():
                tests_passed += 1
            else:
                self.log("❌ 登录失败，跳过后续需要认证的测试")
                return False
            
            # 6. 仪表板页面测试
            tests_total += 1
            if self.test_dashboard_page():
                tests_passed += 1
            
            # 7. 用户管理页面测试
            tests_total += 1
            if self.test_user_management_page():
                tests_passed += 1
            
            # 8. API集成测试
            tests_total += 1
            if self.test_api_integration():
                tests_passed += 1
            
            # 9. 响应式设计测试
            tests_total += 1
            if self.test_responsive_design():
                tests_passed += 1
            
        finally:
            self.cleanup()
        
        # 测试结果
        self.log("=" * 60)
        self.log(f"🎯 集成测试完成: {tests_passed}/{tests_total} 通过")
        
        if tests_passed == tests_total:
            self.log("🎉 所有集成测试通过！")
            return True
        else:
            self.log(f"⚠️  有 {tests_total - tests_passed} 个测试失败")
            return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='前后端集成测试')
    parser.add_argument('--backend', default='http://localhost:8000', help='后端服务地址')
    parser.add_argument('--frontend', default='http://localhost:3000', help='前端服务地址')
    
    args = parser.parse_args()
    
    tester = IntegrationTester(args.backend, args.frontend)
    
    try:
        success = tester.run_integration_tests()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        tester.cleanup()
        exit(1)
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        tester.cleanup()
        exit(1)

if __name__ == "__main__":
    main()