#!/usr/bin/env python3
"""
手动测试指南生成器
"""

def generate_test_guide():
    """生成手动测试指南"""
    
    guide = """
# 禅道MCP权限系统 - 手动测试指南

## 🚀 测试环境准备

### 1. 启动服务
```bash
# 启动后端服务
cd zentao-mcp-backend-service
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 启动前端服务
cd zentao-token-web
bun run dev
```

### 2. 访问地址
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **前端界面**: http://localhost:3000

### 3. 默认管理员账户
- **用户名**: admin
- **密码**: admin123

## 📋 测试清单

### 🔐 认证功能测试

#### 1. 登录测试
- [ ] 访问 http://localhost:3000
- [ ] 应该自动跳转到登录页面
- [ ] 使用正确账户登录 (admin/admin123)
- [ ] 应该跳转到仪表板页面
- [ ] 使用错误账户登录
- [ ] 应该显示错误信息

#### 2. 权限验证
- [ ] 未登录时访问 /users 应该跳转到登录页
- [ ] 登录后可以正常访问各个页面
- [ ] 登出后应该清除认证状态

### 📊 仪表板功能测试

#### 1. 页面显示
- [ ] 仪表板标题显示正确
- [ ] 统计卡片显示数据
- [ ] 快速操作链接可点击
- [ ] 系统信息显示当前用户

#### 2. 导航功能
- [ ] 点击"用户管理"跳转正确
- [ ] 点击"API Keys"跳转正确
- [ ] 用户菜单显示正确信息
- [ ] 登出功能正常工作

### 👥 用户管理功能测试

#### 1. 用户列表
- [ ] 访问 /users 显示用户列表
- [ ] 用户信息显示完整（用户名、邮箱、类型、状态）
- [ ] 搜索功能正常工作
- [ ] 刷新按钮正常工作

#### 2. 创建用户
- [ ] 点击"新建用户"打开模态框
- [ ] 填写用户信息并提交
- [ ] 新用户出现在列表中
- [ ] 表单验证正常工作

#### 3. 用户操作
- [ ] 编辑用户功能
- [ ] 重置密码功能
- [ ] 启用/禁用用户功能
- [ ] 删除用户功能（不能删除超级管理员）

### 🔑 API Key管理测试

#### 1. API Key列表
- [ ] 访问 /api-keys 显示Key列表
- [ ] Key信息显示完整

#### 2. Key操作
- [ ] 创建新的API Key
- [ ] 删除API Key
- [ ] Key权限设置

### 📝 审计日志测试

#### 1. 日志查看
- [ ] 访问 /audit-logs 显示日志列表
- [ ] 日志信息完整（操作、时间、用户等）
- [ ] 日志筛选功能

### 🎨 界面响应式测试

#### 1. 不同屏幕尺寸
- [ ] 桌面端显示正常 (1920x1080)
- [ ] 平板端显示正常 (768x1024)
- [ ] 手机端显示正常 (375x667)

#### 2. 交互体验
- [ ] 按钮hover效果
- [ ] 表单交互流畅
- [ ] 加载状态显示
- [ ] 错误提示友好

### 🔧 API接口测试

#### 1. 认证接口
```bash
# 登录
curl -X POST http://localhost:8000/api/v1/auth/login \\
  -H "Content-Type: application/json" \\
  -d '{"username":"admin","password":"admin123"}'

# 获取当前用户
curl -X GET http://localhost:8000/api/v1/auth/me \\
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 2. 用户管理接口
```bash
# 获取用户列表
curl -X GET http://localhost:8000/api/v1/users \\
  -H "Authorization: Bearer YOUR_TOKEN"

# 创建用户
curl -X POST http://localhost:8000/api/v1/users \\
  -H "Authorization: Bearer YOUR_TOKEN" \\
  -H "Content-Type: application/json" \\
  -d '{"username":"testuser","email":"<EMAIL>","password":"test123","user_type":"USER"}'
```

## 🐛 常见问题排查

### 1. 后端服务问题
- 检查端口8000是否被占用
- 检查数据库文件是否存在
- 查看控制台错误信息

### 2. 前端服务问题
- 检查端口3000是否被占用
- 检查依赖是否安装完整
- 查看浏览器控制台错误

### 3. 认证问题
- 检查token是否正确设置
- 检查API请求头格式
- 验证用户权限级别

### 4. 数据库问题
- 重新初始化数据库
- 检查管理员账户是否存在
- 验证数据表结构

## ✅ 测试完成标准

### 基础功能
- [ ] 所有页面可正常访问
- [ ] 用户认证流程完整
- [ ] 权限控制正确实施
- [ ] 数据操作功能正常

### 用户体验
- [ ] 界面美观易用
- [ ] 响应速度快
- [ ] 错误处理友好
- [ ] 操作反馈及时

### 安全性
- [ ] 密码加密存储
- [ ] 会话管理安全
- [ ] 权限验证严格
- [ ] 审计日志完整

## 📊 测试报告模板

### 测试环境
- 操作系统: 
- 浏览器: 
- 后端版本: 
- 前端版本: 

### 测试结果
- 通过测试: ___/___
- 失败测试: ___/___
- 阻塞问题: ___个

### 问题记录
1. 问题描述:
   - 重现步骤:
   - 期望结果:
   - 实际结果:
   - 严重程度:

### 建议改进
1. 功能改进建议
2. 性能优化建议
3. 用户体验改进建议
"""
    
    return guide

def main():
    """主函数"""
    guide = generate_test_guide()
    
    # 保存到文件
    with open("MANUAL_TEST_GUIDE.md", "w", encoding="utf-8") as f:
        f.write(guide)
    
    print("📋 手动测试指南已生成: MANUAL_TEST_GUIDE.md")
    print("\n" + "="*50)
    print("🚀 快速测试步骤:")
    print("1. 访问 http://localhost:3000")
    print("2. 使用 admin/admin123 登录")
    print("3. 测试各个功能页面")
    print("4. 检查API接口响应")
    print("="*50)

if __name__ == "__main__":
    main()